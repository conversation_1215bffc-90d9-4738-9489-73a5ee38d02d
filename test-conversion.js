#!/usr/bin/env node

const { htmlToLexical } = require('@tryghost/kg-html-to-lexical');
const { render: renderMarkdownToHtml } = require('@tryghost/kg-markdown-html-renderer');

// Test markdown with code blocks
const testMarkdown = `# Test Post

Here's some text with a code block:

\`\`\`javascript
function hello() {
  console.log("Hello world!");
}
\`\`\`

And another one:

\`\`\`elixir
defmodule Test do
  def hello do
    IO.puts("Hello from Elixir!")
  end
end
\`\`\`

Some inline code: \`const x = 42\`

And a plain code block:

\`\`\`
plain text code
no language specified
\`\`\`
`;

console.log('🔍 Testing markdown to lexical conversion...\n');

console.log('📝 Original markdown:');
console.log(testMarkdown);
console.log('\n' + '='.repeat(50) + '\n');

try {
  // Step 1: Convert markdown to HTML
  console.log('🔄 Step 1: Converting markdown to HTML...');
  const html = renderMarkdownToHtml(testMarkdown);
  console.log('📄 Generated HTML:');
  console.log(html);
  console.log('\n' + '='.repeat(50) + '\n');

  // Step 2: Convert HTML to lexical
  console.log('🔄 Step 2: Converting HTML to lexical...');
  const lexical = htmlToLexical(html);
  console.log('📄 Generated lexical structure:');
  console.log(JSON.stringify(lexical, null, 2));

  // Step 3: Check for code blocks
  console.log('\n' + '='.repeat(50) + '\n');
  console.log('🔍 Analyzing lexical structure for code blocks...');

  function findCodeBlocks(node, path = '') {
    if (node && typeof node === 'object') {
      if (node.type === 'codeblock') {
        console.log(`📄 Found code block at ${path}:`);
        console.log(`   Type: ${node.type}`);
        console.log(`   Language: ${node.language || 'none'}`);
        console.log(`   Code: ${node.code?.substring(0, 50)}...`);
        console.log(`   Caption: ${node.caption || 'none'}`);
      }

      if (Array.isArray(node)) {
        node.forEach((item, index) => findCodeBlocks(item, `${path}[${index}]`));
      } else {
        Object.keys(node).forEach(key => {
          findCodeBlocks(node[key], `${path}.${key}`);
        });
      }
    }
  }

  findCodeBlocks(lexical, 'root');

} catch (error) {
  console.error('❌ Error during conversion:', error.message);
  console.error(error.stack);
}
