---
title: "About"
date: 2021-05-09T12:26:49+02:00
author: "<PERSON>"
showToc: false
TocOpen: false
hidemeta: true
comments: false
canonicalURL: "https://solnic.dev/about"
disableShare: true
disableHLJS: true
hideSummary: true
searchHidden: true
ShowReadingTime: false
ShowBreadCrumbs: false
ShowPostNavLinks: false
---

I am a software consultant with over 20 years of experience. My interests include data processing, API integrations, infrastructure and observability, TDD/BDD and effective ways of testing, team leadership, and mentorship.

I have worked with teams and clients from around the world, helping to build new applications from scratch and supporting maintenance work on large systems.

I enjoy collaborating with people, offering help and inspiration, and experimenting with new ideas. Since 2009, I have been an active contributor and maintainer of various open-source software (OSS) projects. I am a former core team member of the DataMapper project, the creator of the popular Virtus gem, the creator and lead developer of the rom-rb project, the co-founder and core developer of dry-rb project and Hanami core team member.

I write technical articles for my blog on topics such as programming in Elixir and Ruby, interesting OSS projects, testing techniques, refactoring, and database management. Additionally, I am an occasional conference speaker, having had the privilege to present at events like RedDotRubyConf, EuRuCamp, RubyNation, BaRuCo (now FullStackFest), RubyConf Australia, and Brighton Ruby.

In 2017, I was honored as a final nominee for the Ruby Prize award by the Ruby Association.
