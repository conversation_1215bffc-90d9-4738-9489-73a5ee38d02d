/* GitHub Sponsors page custom styles for Ghost.io */
/*
SOLUTION: Ghost.io stripped the HTML structure during import.
You need to manually create the HTML structure in Ghost.io's editor.

INSTRUCTIONS:
1. Go to Ghost.io admin → Pages → GitHub Sponsors
2. Delete the current content
3. Use the HTML card in Ghost.io editor
4. Paste the HTML structure below into the HTML card
5. Add this CSS to Code Injection → Site Header

HTML STRUCTURE TO PASTE IN GHOST.IO HTML CARD:
<div class="github-sponsors-page">
  <h2>Current Sponsors</h2>
  <p>I'm incredibly grateful for the support from my current GitHub Sponsors. Your contributions help me dedicate more time to open source work and make it sustainable. Thank you! 🙏</p>

  <div class="sponsors-grid current-sponsors">
    <div class="sponsor-card">
      <a href="https://github.com/avo-hq" target="_blank" rel="noopener" class="sponsor-link">
        <img src="https://avatars.githubusercontent.com/u/66615189?v=4" alt="Avo" class="sponsor-avatar">
        <div class="sponsor-info">
          <h3>Avo</h3>
        </div>
      </a>
    </div>
    <!-- Add more sponsor cards here -->
  </div>

  <h2>Past Sponsors</h2>
  <p>Thank you to all my past sponsors for their support! 🙏</p>

  <div class="sponsors-list past-sponsors">
    <a href="https://github.com/getsentry" target="_blank" rel="noopener" class="sponsor-item">
      <img src="https://avatars.githubusercontent.com/u/1396951?v=4" alt="Sentry" class="sponsor-avatar-small">
      <span>@getsentry</span>
    </a>
    <!-- Add more past sponsors here -->
  </div>
</div>
*/

/* CSS for the manually created HTML structure */
.github-sponsors-page {
  max-width: 800px;
  margin: 0 auto;
}

.github-sponsors-page h2 {
  margin: 2rem 0 1rem 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.github-sponsors-page p {
  margin: 1rem 0;
  line-height: 1.6;
}

/* Current sponsors grid */
.sponsors-grid.current-sponsors {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.sponsor-card {
  border: 1px solid rgba(0,0,0,0.1);
  border-radius: 12px;
  padding: 0;
  background: #fff;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  overflow: hidden;
}

.sponsor-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.sponsor-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  text-decoration: none;
  color: inherit;
}

.sponsor-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.sponsor-info h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Past sponsors list */
.sponsors-list.past-sponsors {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 2rem 0;
}

.sponsor-item {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(0,0,0,0.05);
  border-radius: 20px;
  text-decoration: none;
  color: inherit;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.sponsor-item:hover {
  background: rgba(0,0,0,0.1);
}

.sponsor-avatar-small {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .sponsor-card {
    background: #1a1a1a;
    border-color: rgba(255,255,255,0.1);
  }

  .sponsor-item {
    background: rgba(255,255,255,0.1);
  }

  .sponsor-item:hover {
    background: rgba(255,255,255,0.2);
  }
}

/* Past sponsors: compact list */
.sponsor-list.past-sponsors-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.sponsor-list.past-sponsors-list .sponsor-list-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 6px 0;
  border-bottom: 1px solid var(--color-border, rgba(0,0,0,0.06));
}

/* Small avatar for past sponsors */
.sponsor-list.past-sponsors-list .sponsor-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

/* Handle link */
.sponsor-handle {
  color: inherit;
  text-decoration: none;
  font-weight: 600;
}
.sponsor-handle:hover {
  text-decoration: underline;
}

/* Ghost card spacing adjustments */
.kg-card.sponsors-section {
  padding: 0;
  box-shadow: none;
  border: 0;
  background: transparent;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .sponsor-card {
    border-color: rgba(255,255,255,0.12);
    box-shadow: 0 .5px 1px rgba(0,0,0,0.3);
  }
  .sponsor-list.past-sponsors-list .sponsor-list-item {
    border-bottom-color: rgba(255,255,255,0.12);
  }
}

