---
title: GitHub Sponsorship
date: '2019-09-04'
categories:
- blog
tags:
- archives
- blog
- github
- oss
- sponsorship
slug: github-sponsorship
aliases:
- "/2019/09/04/github-sponsorship"
- "/github-sponsorship"
---

GitHub has recently started their [Sponsors program](https://github.com/sponsors) and I was lucky to get an invite pretty early on when it's still in a closed beta phase. I received the invite on August 30th and it took me **exactly one month** to finally fill out my profile and get it published. I'm saying "finally" because even though I knew I wanted to try it out, it was not immediately obvious to me how exactly I want it to work in my case. In this article I'd like to share with you some of my thoughts on working on Open Source and explain why I hesitated to get my sponsorship profile published.

## Why do people work on Open Source?

First, let's stop for a second and think about why peopled work on Open Source anyway. This can vary depending on a person but I think most common reasons include:

- You want to learn
- You want to help others
- You want to become better at programming

These points look pretty nice and positive, don't they? :) To balance this a bit here are a couple of less nice reasons that may surprise some of you. People may start working on Open Source also because of:

- The pressure that it's the right thing to do if you want to find a good job (ie lots of job offers include an expectation that a candidate should contribute to OSS)
- Assumption that it's what developers are supposed to do, otherwise they are not "true and passionate developers"

I'm pretty sure a lot of you can relate to this. Personally I consider this to be a problem because **it's not** a good type of motivation to get involved in Open Source.

## Benefits of Open Source work

Luckily, there are many wonderful things that may happen when you start contributing to Open Source. Most notable ones include:

- You can learn a lot and become better at what you do
- You can meet a lot of amazing people from all over the world
- You may get a chance to speak at conferences
- It may become a bit simpler for you to get a job (although in my opinion it's highly speculative)

I believe these benefits keep people motivated to continue working on Open Source despite all of the downsides aka the dark side of Open Source work.

## Open Source can become your second job

This is the moment when **the fun** part of working on Open Source **ends**. Because it's a gradual process that can easily span many months if not years, you may not even notice that something that used to be this amazing hobby that you love **is now your job**. It can become your second job because you will find yourself doing _some things_ because you feel obliged to do them, rather than because you _want to do them_. That's what having a job means, except the "feel" part, because in an actual job that you're paid to do you really are obliged.

The feeling of being obliged to do _some things_ is a tricky one. On one hand you know you are not obliged to do anything, it's your personal time, you can do whatever you want. Except that...you want to support the users of your projects whenever they report a problem, you want to write documentation, you want to be there to answer their questions, you want to add features even though you don't need them but in your head they make sense so you go and do it...and...**oh shit it's a job now**.

It can become a job and for **many Open Source contributors it does become their second job**. The problem? Nobody really has the time to do this job and to make things worse barely any Open Source contributor is being paid to do it. As a result many contributors flip the table and quit.

This is the reason why **I am very happy to see GitHub Sponsors**!

## It's not that simple!

When GitHub Sponsors got launched I started looking at the first sponsorship profiles to see what people write in their bios and what kind of sponsorship tiers they have. I needed an inspiration because I knew it will be tricky for me to figure this out for myself. What I've mostly seen so far are people with very similar stories - a person has been working on a ton of OSS projects for years (some Node folks build & maintain hundreds of projects, crazy), they need support because they would love to do it full-time and be able to make a living out of it. They offer perks for their sponsors like saying "thank you" on Twitter, or listing people in some "thank you" pages, or...providing prioritized user support for sponsors who are willing to pay more.

Setting up tiers **was the harderst thing for me** and here's why. I have [a full time job that I really love](https://theorem.co), I don't really want to work on Open Source full-time (it would be way too stressful) which means I can't really offer perks like "a guarantee to reply to support questions within 24 hours". Not possible for me. I don't have gazillion followers on Twitter and my blog is not a super popular medium which means tweeting about you or your company is probably not that big of a deal for you. Putting "your company logo" on my website is also a no-go because, again, I have a full-time job and adding ads of other companies on my website would require me to make sure it's legally OK, and I simply do not want to deal with such issues.

## My Sponsorship

I spent so much time thinking about the tiers that I was actually very close to abandoning the whole idea of opening my sponsorship profile because it felt like it's not a good fit for me. Then it hit me - I've been thinking about producing various content related to my Open Source work and programming in general. I've been barely sharing anything with anyone (except the code, obviously) and it feels like I've learned so much over 9+ years of my Open Source involvement that I really want to do something about it now.

As I outlined in my sponsorship bio, I would like to start writing here on a regular basis as well as start producing screencasts about programming (mostly Ruby) and Open Source projects that I'm working on. I believe this is something people could benefit from and it should be a win-win situation.

I'll announce more details once I get everything sorted out and for now **I am open to feedback and questions** so please feel to [get in touch](https://solnic.codes/contact/)!

I'm **very grateful** that I already have sponsors - **thank you so much!**
