---
title: 2024 Status update
date: '2024-02-13'
tags:
- personal
slug: 2024-status-update
aliases:
- "/2024/02/13/2024-status-update"
- "/2024-status-update"
---

I'm working on becoming more active online and returning to my projects, so I thought it would be a good time to share what I've been up to lately. Over the past three years, many aspects of my life have changed, from receiving an ADHD diagnosis and moving twice, to landing my first Elixir job. Nowadays, I can confidently say that life is good and I'm doing well. The journey to this point is a story I'd love to share, but I'll save it for another time. Today, I just want to focus on updating you about my work and projects, as exciting developments are underway!

# 💫 Elixir

In April 2022 I joined [valued.app](http://valued.app) as the first engineer. This has been my very first job where Elixir is the primary language. I'm very grateful that the founders believed in me and hired me despite the fact I had no real Elixir experience. I've been busting my ass off to become a productive Elixir developer and it feels like I have succeeded. I **know this** because whenever I write <PERSON>, I keep adding `do` after a method signature just out of habit 😉

At valued.app I mostly focus on the backend, but every now and then I get to work with Phoenix and LiveView and I really enjoy it. After about 1.5 year of doing a lot of data-intensive Elixir work, I realized I'm experienced enough to write a library and so [Drops project was born](https://solnic.dev/introducing-elixir-drops). Porting a complex Ruby library to Elixir has been a fascinating process for me, it's another topic I'd love to cover in detail here on this blog or maybe screencasts (what?! yes, read on!).

# 💎 Ruby and Open Source

Learning Elixir and building a complex system at work was quite demanding, and because of this my Ruby Open Source involvement suffered. I have to admit that I needed to actively work on my "open source shame" (y'all OSS maintainers know what I mean, yeah?) so that I could just let it go for a while and focus on my current priorities.

Eventually, I realized that this break from Ruby was very healthy for me. I got a lot of perspective and it allowed me to understand better what I should focus on going forward. One of the biggest things I noticed, is how much **functionality** we have built over time in Hanami, dry-rb and rom-rb projects. The only problem is that many things were not polished enough and the docs were always lacking. This made me think that if I could go back in time, I would build less and document more and I'm absolutely sure things would turn out better. Sounds so obvious? Yet I needed about a decade to be able to see it clearly. I believe the reasons why I focused so much on building features are complex and personal too (in a nutshell: I needed to keep my brain very busy, and writing docs didn't cut it).

# ✨ Working on a Ruby course

In 2022 I started working on a Ruby course, initially it was called "Data Oriented Web Development in Ruby" but no matter how hard I tried, I could not memorize the acronym, so I ended up renaming it to **"Data Oriented Ruby on Rails"**, or in short "DOROR". It's also just a better title.

It was really hard to work on this course while at the same time learning Elixir, so the progress was very slow. At some point I decided to just put it on hold and see how things will evolve. Luckily, I was able to get back to working on the course in December last year with a fresh mind full of ideas. Roughly 50% of the lessons are done and I plan to finish everything and publish the course before the end of March.

The course explains the most important learnings from my 17 years of writing Ruby both professionally and as a creator of many Open Source libraries. It explains "crazy" things like objects that don't change, modeling objects like functions, composing functionality with no fuss, dealing with failure in a flexible and elegant way and more. These concepts are explained in the context of a typical Rails application, which should make it much simpler to grok for people who are familiar with Rails.

There are around 200 people who signed up for the course's newsletter at podia.com where it's going to be launched (which blew my mind to be honest). I suspect that some of them are no longer interested given that I announced the course 1.5 year ago and it's still in the works, but maybe they are, who knows? 🙂

I'll be sending the first update via course's newsletter soon, so if you're interested, now it's a good time to [sign up and join the waitlist](https://solnic.podia.com/data-oriented-ruby-on-rails)!

# 🌸 Hanami and friends

I did not manage to do much work on Hanami since we released 2.0. Huge props to Luca Guidi and Tim Riley, and all the contributors, for pushing the project forward. We've got 2.1 release around the corner and I hope to get involved again very soon. I've been analyzing what we've accomplished so far (which includes dry-rb and rom-rb) trying to come up with a plan how to improve and properly document **what we already have**, instead of chasing the next big feature like I used to do. There are big tasks awaiting, like porting our sites to a new documentation system ([hanamirb.org](https://hanamirb.org) uses customized Hugo setup and [dry-rb.org](https://dry-rb.org) and [rom-rb.org](https://rom-rb.org) are using customized Middleman setup). I also want to focus on basic maintenance and providing learning content.

My Elixir experience is very helpful here too. Seeing how things work in the Elixir ecosystem is an amazing inspiration for me. And it goes both ways! There are many things that exist in Ruby that I **would love** to see in Elixir too.

# 🎥 YouTube channel

Back in 2021 I used to have a YouTube channel. It grew very quickly hitting 1k subscribers and I got a lot of positive feedback. Unfortunately due to personal reasons I shut it down (actually, I deleted it along with all the screencasts that I recorded except a couple of episodes). Since then I thought about re-establishing this channel many times, especially that I got messages from people asking me if I will ever do it again because they enjoyed my screencasts a lot (which was **amazing** and I'm very grateful for this).

I **believe** I'm ready to start screencasting again. However, there are a few adjustments I need to make to ensure that producing the screencasts won't be as time-consuming as before. If I succeed, my channel will be revived, and it's thrilling just to think about it 🙂 One idea I have is to begin with a more laid-back, vlog-like format and simply showcase some projects I'm currently working on. Another aspect that **I enjoy** is discussing work, tools, and systems that I use to manage multiple tasks. Reviewing new apps and tools is also a subject that interests me.

If you're interested, please [subscribe](https://www.youtube.com/@solnic) as that's going to be a great source of extra motivation for me!

# 🧠 Mental Health

I've been meaning to write about mental health for years now. I suppose I just wasn't **truly ready** before. There's at least one post I plan to write that will summarize my experience, starting with an ADHD diagnosis and utter chaos, followed by several months of attempting to "fix" myself using medication, and finally achieving peace of mind and significant control over my ADHD. I genuinely feel like "I made it," and I want to share my story with the world because maybe it will help someone. This was **the hardest thing** I've ever done in my life.

Taking care of my mental health is also the reason why I changed my approach to working on, let's call them "extra projects" 🙂 I'm doing my best to work less but more effectively, and I'm focusing on taking time to relax and recharge my energy levels.

So far, so good!

# 🙇 Thank you

I'd like to thank everybody who supported me over the years. It's been a long and a difficult ride for me and I'm just happy that I'm...here.

If you have any questions or comments, feel free to reach out! You can find me on [Mastodon](https://hachyderm.io/@solnic), [X (Twitter)](https://x.com/solnic29a) and [Bluesky](https://bsky.app/profile/solnic.bsky.social).
