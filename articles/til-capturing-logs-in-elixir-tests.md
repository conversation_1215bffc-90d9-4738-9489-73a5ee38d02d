---
title: TIL - Capturing logs in Elixir tests
date: "2025-03-22T08:33:50.000Z"
tags:
  - elixir
  - testing
  - exunit
  - logging
  - til
slug: til-capturing-logs-in-elixir-tests
---

Testing logging can be tricky but recently I've learned that Elixir's ExUnit ships with a `CaptureLog` module that makes it easy to test logging behavior. I was really happy to see this built-in!

Let's say you have a function that processes a template and logs errors when something goes wrong. You want to test both the error handling and ensure the right messages are being logged. Here's a simplified version of such a function:

```
def render_template(%SourceFile{is_template: true} = source_file, vars) do
  case Solid.parse(source_file.content) do
    {:ok, template} -&gt;
      # Template parsing succeeded, render it...

    {:error, reason} -&gt;
      Logger.error("Template parsing failed", reason: reason)
      {:error, :template_parsing_failed}
  end
end

```

Without `ExUnit.CaptureLog`, testing this would result in error messages cluttering your test output, even though those errors are expected as part of the test.

`ExUnit.CaptureLog` allows you to:

1. $1
2. $1
3. $1

Here's how I used it:

```
defmodule YourTest do
  use ExUnit.Case

  import ExUnit.CaptureLog

  test "handles invalid template syntax" do
    file = %SourceFile{
      content: "{{ unclosed tag",
      is_template: true
    }

    # Capture logs during template rendering
    log = capture_log(fn -&gt;
      assert {:error, "Template parsing failed: " &lt;&gt; _} =
        render_template(file, %{})
    end)

    # Assert against the captured log content
    assert log =~ "Template parsing failed"
    assert log =~ "expected end of string"
    assert log =~ "{{ unclosed tag"
  end
end

```

## Breaking Down the Test

Let's see what's happening:

1. $1

```
import ExUnit.CaptureLog

```

1. $1

```
log = capture_log(fn -&gt;
  # Code that generates logs
end)

```

1. $1

- Takes a function as an argument
- Executes that function
- Captures any log output during execution
- Returns the captured log as a string

1. $1

```
assert log =~ "Template parsing failed"

```

## Benefits

Using `CaptureLog` provides several advantages:

**Clean Test Output**: Error logs don't pollute your test output, making it easier to spot real test failures.

**Explicit Verification**: You can verify that your error handling is not just returning the right values, but also logging the right information.

**Better Documentation**: The test clearly shows what log messages are expected when errors occur.

`ExUnit.CaptureLog` is a great tool for testing logging behavior in your Elixir applications. It helps you write more comprehensive tests while keeping your test output clean. Next time you need to test code that generates logs, remember that you can capture and verify those logs as part of your test assertions.

Today I learned!

## Further Reading

- [ExUnit.CaptureLog documentation](https://hexdocs.pm/ex_unit/ExUnit.CaptureLog.html)
- [Elixir Logger documentation](https://hexdocs.pm/logger/Logger.html)