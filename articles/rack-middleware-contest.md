---
title: Rack Middleware Contest
date: '2009-10-12'
categories:
- blog
tags:
- archives
- blog
- oss
slug: rack-middleware-contest
aliases:
- "/2009/10/12/rack-middleware-contest"
- "/rack-middleware-contest"
---

CodeRack is a coding contest dreamed up by a group of the Ruby programmers at [Lunar Logic Polska](http://www.lunarlogicpolska.com) who were excited about the possibilities of Rack middleware. The team wants to encourage Ruby developers to explore the possibilities and what better way than to hold a contest? The secondary goal of the contest is to generate a set of open source solutions that will solve real problems and inspire others. Every entry will be released under the MIT open source license.

Programmers are encouraged to submit contest entries that will be judged based on the cleverness of the application and the elegance of the code. Entries can be submitted at [coderack.org](http://www.coderack.org) until midnight EST November 15th. Finalists are scheduled to be announced on the 1st of December and public voting will run for one month. The final winners will be announced on the 5th of January.

The first round of the contest will be judged by an elite panel of judges including <PERSON> of O’Reilly Media, <PERSON> and <PERSON><PERSON> of GitHub, <PERSON> of 37Signals, <PERSON><PERSON><PERSON> of Engine Yard and Rails core team member, <PERSON> of Heroku, Core Rails team member <PERSON>, and the Rails Envy team of <PERSON> and <PERSON>.

Once the finalists have been selected by the panel, the public will vote for the top prize winners.

Prizes have been donated by Bytemark Hosting, GitHub, Jetbrains, Mindmeister, Freelance Total, Heroku, Rackspace Hosting, Peepcode, BDDCasts, and Zenbe Shareflow. The top prize includes a dedicated quad core server package and is valued at over $3000. Every entrant will receive a credit from bddcasts.com and $30 credit from Heroku. All finalists will receive a package including Zenbe Shareflow subscriptions, a RubyMine license from JetBrains, and five credits from bddcasts.com. Details of all of the prize packages will soon be available on the coderack.org website.

More information about the contest, including the contest rules, can be found at [coderack.org](http://coderack.org).

ps. This post is a copy from [Lunar Logic Polska blog](http://www.lunarlogicpolska.com/2009/10/8/lunar-logic-polska-launches-coderack-global-coding-contest)
