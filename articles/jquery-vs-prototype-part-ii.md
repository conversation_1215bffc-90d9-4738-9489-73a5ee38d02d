---
title: j<PERSON><PERSON>y vs Prototype - part II
date: "2008-02-03T00:00:00.000Z"
tags:
  - archives
  - blog
  - javascript
  - jquery
  - prototype
  - "#Import 2025-08-12 09:41"
slug: jquery-vs-prototype-part-ii
excerpt: Recently, new versions of jQuery and Prototype have been released – it’s a perfect moment for a part number 2. On the official Prototype blog we can read that the general performance of CSS selectors is now improved, unfortunately only for Safari 3, but Element#up/#down/#next/#previous should...
---

Recently, new versions of jQuery and Prototype have been released – it’s a perfect moment for a part number 2. On the official Prototype blog we [can read](http://www.prototypejs.org/2008/1/25/prototype-1-6-0-2-bug-fixes-performance-improvements-and-security) that the general performance of CSS selectors is now improved, unfortunately only for Safari 3, but Element#up/#down/#next/#previous should now be faster on all browsers, it’s a good news as they were really slow. On the other hand we have jQuery [official announcement](http://jquery.com/blog/2008/01/15/jquery-122-2nd-birthday-present) with information that j<PERSON><PERSON>y is now 300% faster – we’ll see!

This time I made a step forward and decided to use a custom JavaScript-based testing environment instead of running tests using Firebug profiler. The obvious advantage is that I was able to run all the tests on 4 different browsers. New test cases aren’t much different then in the first part, let’s say it’s a modification of the previous ones with some extra operations and a little more complex HTML structure.

### Test environment setup

Libraries:

- jQuery 1.2.2
- Prototype *******

All the tests were run on the following browsers:

- Firefox ********
- Konqueror 4.00.00
- Opera 9.50_beta1
- Internet Explorer 7 (**but using Windows on VirtualBox!**)

A tiny piece of JavaScript code is responsible for running the tests, each operation is called only once inside a try-catch block, so the essential part looks like this:

```
    try {
      var start = new Date;
      test();
      var end = new Date - start;
      this.writeResults(test, end);
    } catch(e) {
      test.resultCell.innerHTML = 'Exception caught: '+e.message+'';
    }

```

There is a 3 seconds break between each test run, results are automatically inserted into the results table. If you want, you can check it out on your own, just go [right here](http://solnic.codes/test_runner/index.html) and hit the ‘run tests!’ button.

### The results

I’m happy to see that all tests pass on the latest Konqueror, previous version from KDE3 fails on some Prototype tests. I don’t own Mac, so you won’t see Safari results here, although I’ve run the tests on my friend’s MacBook with very similar hardware as my laptop has (Intel Core Duo 2ghz + 2 gigs of RAM), and it was faster even then Konqueror (no, it doesn’t mean his MacBook is faster then my laptop!!!! ;)).

I’ve run everything 3 times, here are average results in ms:

      #LibraryTestFirefoxKonquerorIE7Opera**1**jQuery“` erb $(‘td.counter’).addClass(‘marked’) ”`96.632.37037Prototype“` erb $$(‘td.counter’).each(function(el){el.addClassName(‘marked’)}) ”`108.349.685875.7**2**jQuery“` erb$(‘td.counter span.special’).removeClass(‘special’)”`6223.646.625.6Prototype“` erb $$(‘td.counter span.special’).each(function(el) {el.removeClassName(‘special’)}) ”`2823.716724.7**3**jQuery“` erb$(‘td.content span.odd’).css(‘color’, ‘red’)”`124.740.363.738.3Prototype“` erb$$(‘td.content span.odd’).each(function(el) {**el.setStyle(‘color: red’)
})”`55.73129733.74**jQuery“` erb $(‘td.content span.even’).before(‘### text

’) ”`382.7177.3373.7205.3Prototype“` erb $$(‘td.content span.even’).each(function(el) { el.insert({before:‘### text

’}) }) ”`35990.7527138.7**5**jQuery“` erb$(‘td.content h3’).show()”`178.7227.783.31161.7Prototype“` erb$$(‘td.content h3’).each(Element.show)”`3821250.719**6**jQuery“` erb$(‘div.special’).hide()”`9081.333.7375.3Prototype“` erb$$(‘div.special’).each(Element.hide)”`18773.312**7**jQuery“` erb$(‘div.special, td.content .odd’).toggle()”`637.7431.75171360.3Prototype“` erb$$(‘div.special, td.content .odd’).each(Element.toggle)”`7143.7106.743**8**jQuery“` erb$(‘span.odd’).remove()”`132.759.3123.366.7Prototype“` erb$$(‘span.odd’).each(Element.remove)”`2911.736.719.3**9**jQuery“` erb$(‘#data p.lost:first’).html(‘gotcha!’)”`51.7103.3Prototype“` erb$(‘data’).down(‘p.lost’).update(‘gotcha!’)”`11.72107.3### Conclusion #2

Prototype was at least 2 times faster then jQuery in 15 cases, and jQuery was faster then Prototype in 8 cases. What library should I choose? In my case I will stick with Prototype, because it offers the same functionality as jQuery does + more and it’s faster. jQuery is probably better for projects where there’s a need for some fancy UI effects and that’s it, but it’s just an assumption, correct me if I’m wrong…