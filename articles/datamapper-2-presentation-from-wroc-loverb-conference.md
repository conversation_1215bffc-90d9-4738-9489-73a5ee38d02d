---
title: DataMapper-2 Presentation From wroc_love.rb Conference
date: '2012-03-13'
categories:
- blog
tags:
- archives
- blog
- conferences
- datamapper
- ruby
slug: datamapper-2-presentation-from-wroc-loverb-conference
aliases:
- "/2012/03/13/datamapper-2-presentation-from-wroc-loverb-conference"
- "/datamapper-2-presentation-from-wroc-loverb-conference"
---

Once again thank you for the awesome event that took place in Wrocław last weekend - [the wroc\_love.rb conference](http://wrocloverb.com). We’ve had fantastic and inspiring talks and many great discussions. It’s clear to me that a new era in our community has started and people are willing to learn and embrace patterns from other languages without being scared that we’re going to lose the “agile” aspect of programming in Ruby and Rails.

My personal favorite presentations were <PERSON><PERSON><PERSON>’s [“Decoupling Persistence”](http://decoupling-wrocloverb-2012.heroku.com/) and <PERSON>’s [“It’s Business Time”](http://www.saturnflyer.com/blog/jim/2012/03/13/wroclove-rb-presentation/). We also had a very interesting panel discussion about OOP and Rails.

Most of the talks and discussions should be available online soon. If you’re interested in seeing them make sure to follow wroc\_love.rb on [twitter](http://twitter.com/wrocloverb) and/or watch their [blog](http://blog.wrocloverb.com/) for any future announcements.

Meanwhile here are the slides from the talk I gave about development of DataMapper 2:

[http://speakerdeck.com/embed/4f5cada96e13db027c00de5b.js](http://speakerdeck.com/embed/4f5cada96e13db027c00de5b.js)
