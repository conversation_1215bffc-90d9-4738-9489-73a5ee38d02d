.github-sponsors-page {
  max-width: 800px;
  margin: 0 auto;
}

.github-sponsors-page h2 {
  margin: 2rem 0 1rem 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.github-sponsors-page p {
  margin: 1rem 0;
  line-height: 1.6;
}

.sponsors-section {
  margin: 3rem 0;
}

.gh-content .section-header {
  text-align: center;
  position: relative;
  margin: 3rem 0 2rem 0;
  font-weight: 600;
  font-size: 2.5rem;
}

.section-header::before,
.section-header::after {
  content: '';
  position: absolute;
  top: 50%;
  width: calc(50% - 120px);
  height: 1px;
  background: #e2e8f0;
}

.section-header::before {
  left: 0;
}

.section-header::after {
  right: 0;
}

.section-title {
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.sponsors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.sponsor-card {
  border: 1px solid rgba(0,0,0,0.1);
  border-radius: 12px;
  padding: 0;
  background: #fff;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  overflow: hidden;
}

.sponsor-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.sponsor-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  text-decoration: none;
  color: inherit;
}

.sponsor-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.sponsor-info h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.sponsors-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 2rem 0;
}

.sponsor-item {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(0,0,0,0.05);
  border-radius: 20px;
  text-decoration: none;
  color: inherit;
  font-size: 1.5rem;
  transition: background-color 0.2s ease;
  text-decoration: none;
}

.sponsors-list .sponsor-item {
  text-decoration: none;
}

.sponsor-item:hover {
  background: rgba(0,0,0,0.1);
}

.sponsor-avatar-small {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

@media (prefers-color-scheme: dark) {
  .sponsor-card {
    background: #1a1a1a;
    border-color: rgba(255,255,255,0.1);
  }
  .sponsor-item {
    background: rgba(255,255,255,0.1);
  }
  .sponsor-item:hover {
    background: rgba(255,255,255,0.2);
  }
}
