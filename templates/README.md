# Ghost.io Templates

This directory contains templates for generating HTML content for Ghost.io pages using Handlebars.js template engine.

## Structure

```
templates/
├── styles/           # CSS files for each page
│   ├── github-sponsors.css
│   └── open-source.css
├── components/       # Reusable Handlebars components (partials)
│   ├── sponsor-card.hbs
│   ├── sponsor-item.hbs
│   └── project-card.hbs
└── pages/           # Full page templates
    ├── github-sponsors.hbs
    └── open-source.hbs
```

## Template Syntax

The template engine uses standard Handlebars.js syntax:

### Variable Interpolation
```html
<h1>{{title}}</h1>
<p>Welcome {{user.name}}!</p>
```

### Conditionals
```html
{{#if user.isActive}}
  <p>User is active</p>
{{/if}}
```

### Loops
```html
{{#each items}}
  <li>{{name}} - {{price}}</li>
{{/each}}
```

### Component Inclusion
```html
{{>component-name}}
```

## Usage

The template engine is used automatically by the `scripts/ghost.js` file:

```javascript
const TemplateEngine = require('../lib/template-engine');
const templateEngine = new TemplateEngine('templates');

// Render a page with data
const html = templateEngine.renderPage('github-sponsors', data);
```

Templates use the `.hbs` extension to clearly indicate they contain Handlebars template syntax.

## Editing Templates

### To modify page layout:
Edit files in `templates/pages/`

### To modify styling:
Edit files in `templates/styles/`

### To modify components:
Edit files in `templates/components/`

## Logo Management

The system automatically handles project logos:

### Convention
- Place logo files in `assets/images/{project-id}.png`
- The script will automatically upload them to Ghost CMS
- Uploaded images are cached to avoid re-uploading

### Supported Projects
Currently supports automatic logo upload for:
- `dry-rb.png` → dry-rb project
- `rom-rb.png` → rom-rb project

### How it works
1. Script checks for `assets/images/{project.id}.png`
2. If found, uploads to Ghost CMS via Admin API
3. Returns Ghost CDN URL for use in templates
4. Falls back to external URLs for known projects

## Benefits

- **Industry Standard**: Uses Handlebars.js, a mature and well-supported template engine
- **Separation of concerns**: HTML, CSS, and JavaScript are separated
- **Maintainability**: Easy to update templates without touching code
- **Reusability**: Components (partials) can be reused across pages
- **Performance**: Templates are compiled to optimized functions
- **Security**: Built-in XSS protection from Handlebars
- **Ghost.io compatible**: Generates HTML with embedded CSS for easy copy/paste
- **Automatic logo management**: Uploads and manages project logos via Ghost CMS

## Testing

Run a dry-run to test template changes:

```bash
# Test GitHub sponsors page
npm run ghost-sync:sponsors -- --dry-run

# Test open source page
node scripts/ghost.js --page open-source --dry-run
```
