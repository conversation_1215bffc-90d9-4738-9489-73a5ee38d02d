/* from reset */
::-webkit-scrollbar-track {
    background: 0 0;
}

.list:not(.dark)::-webkit-scrollbar-track {
    background: var(--code-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--tertiary);
    border: 5px solid var(--theme);
    border-radius: var(--radius);
}

.list:not(.dark)::-webkit-scrollbar-thumb {
    border: 5px solid var(--code-bg);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary);
}

::-webkit-scrollbar:not(.highlighttable, .highlight table, .gist .highlight) {
    background: var(--theme);
}

/* from post-single */
.post-content .highlighttable td .highlight pre code::-webkit-scrollbar {
    display: none;
}

.post-content :not(table) ::-webkit-scrollbar-thumb {
    border: 2px solid var(--code-block-bg);
    background: rgb(113, 113, 117);
}

.post-content :not(table) ::-webkit-scrollbar-thumb:hover {
    background: rgb(163, 163, 165);
}

.gist table::-webkit-scrollbar-thumb {
    border: 2px solid rgb(255, 255, 255);
    background: rgb(173, 173, 173);
}

.gist table::-webkit-scrollbar-thumb:hover {
    background: rgb(112, 112, 112);
}

.post-content table::-webkit-scrollbar-thumb {
    border-width: 2px;
}

/* from zmedia */
@media screen and (min-width: 768px) {

    /* reset */
    ::-webkit-scrollbar {
        width: 19px;
        height: 11px;
    }
}
