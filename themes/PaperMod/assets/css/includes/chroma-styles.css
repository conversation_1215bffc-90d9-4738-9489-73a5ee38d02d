/* Background */ .bg { color: #cad3f5; background-color: #24273a; }
/* PreWrapper */ .chroma { color: #cad3f5; background-color: #24273a; }
/* Other */ .chroma .x {  }
/* Error */ .chroma .err { color: #ed8796 }
/* CodeLine */ .chroma .cl {  }
/* LineLink */ .chroma .lnlinks { outline: none; text-decoration: none; color: inherit }
/* LineTableTD */ .chroma .lntd { vertical-align: top; padding: 0; margin: 0; border: 0; }
/* LineTable */ .chroma .lntable { border-spacing: 0; padding: 0; margin: 0; border: 0; }
/* LineHighlight */ .chroma .hl { background-color: #474733 }
/* LineNumbersTable */ .chroma .lnt { white-space: pre; -webkit-user-select: none; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #8087a2 }
/* LineNumbers */ .chroma .ln { white-space: pre; -webkit-user-select: none; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #8087a2 }
/* Line */ .chroma .line { display: flex; }
/* Keyword */ .chroma .k { color: #c6a0f6 }
/* KeywordConstant */ .chroma .kc { color: #f5a97f }
/* KeywordDeclaration */ .chroma .kd { color: #ed8796 }
/* KeywordNamespace */ .chroma .kn { color: #8bd5ca }
/* KeywordPseudo */ .chroma .kp { color: #c6a0f6 }
/* KeywordReserved */ .chroma .kr { color: #c6a0f6 }
/* KeywordType */ .chroma .kt { color: #ed8796 }
/* Name */ .chroma .n {  }
/* NameAttribute */ .chroma .na { color: #8aadf4 }
/* NameBuiltin */ .chroma .nb { color: #91d7e3 }
/* NameBuiltinPseudo */ .chroma .bp { color: #91d7e3 }
/* NameClass */ .chroma .nc { color: #eed49f }
/* NameConstant */ .chroma .no { color: #eed49f }
/* NameDecorator */ .chroma .nd { color: #8aadf4; font-weight: bold }
/* NameEntity */ .chroma .ni { color: #8bd5ca }
/* NameException */ .chroma .ne { color: #f5a97f }
/* NameFunction */ .chroma .nf { color: #8aadf4 }
/* NameFunctionMagic */ .chroma .fm { color: #8aadf4 }
/* NameLabel */ .chroma .nl { color: #91d7e3 }
/* NameNamespace */ .chroma .nn { color: #f5a97f }
/* NameOther */ .chroma .nx {  }
/* NameProperty */ .chroma .py { color: #f5a97f }
/* NameTag */ .chroma .nt { color: #c6a0f6 }
/* NameVariable */ .chroma .nv { color: #f4dbd6 }
/* NameVariableClass */ .chroma .vc { color: #f4dbd6 }
/* NameVariableGlobal */ .chroma .vg { color: #f4dbd6 }
/* NameVariableInstance */ .chroma .vi { color: #f4dbd6 }
/* NameVariableMagic */ .chroma .vm { color: #f4dbd6 }
/* Literal */ .chroma .l {  }
/* LiteralDate */ .chroma .ld {  }
/* LiteralString */ .chroma .s { color: #a6da95 }
/* LiteralStringAffix */ .chroma .sa { color: #ed8796 }
/* LiteralStringBacktick */ .chroma .sb { color: #a6da95 }
/* LiteralStringChar */ .chroma .sc { color: #a6da95 }
/* LiteralStringDelimiter */ .chroma .dl { color: #8aadf4 }
/* LiteralStringDoc */ .chroma .sd { color: #6e738d }
/* LiteralStringDouble */ .chroma .s2 { color: #a6da95 }
/* LiteralStringEscape */ .chroma .se { color: #8aadf4 }
/* LiteralStringHeredoc */ .chroma .sh { color: #6e738d }
/* LiteralStringInterpol */ .chroma .si { color: #a6da95 }
/* LiteralStringOther */ .chroma .sx { color: #a6da95 }
/* LiteralStringRegex */ .chroma .sr { color: #8bd5ca }
/* LiteralStringSingle */ .chroma .s1 { color: #a6da95 }
/* LiteralStringSymbol */ .chroma .ss { color: #a6da95 }
/* LiteralNumber */ .chroma .m { color: #f5a97f }
/* LiteralNumberBin */ .chroma .mb { color: #f5a97f }
/* LiteralNumberFloat */ .chroma .mf { color: #f5a97f }
/* LiteralNumberHex */ .chroma .mh { color: #f5a97f }
/* LiteralNumberInteger */ .chroma .mi { color: #f5a97f }
/* LiteralNumberIntegerLong */ .chroma .il { color: #f5a97f }
/* LiteralNumberOct */ .chroma .mo { color: #f5a97f }
/* Operator */ .chroma .o { color: #91d7e3; font-weight: bold }
/* OperatorWord */ .chroma .ow { color: #91d7e3; font-weight: bold }
/* Punctuation */ .chroma .p {  }
/* Comment */ .chroma .c { color: #6e738d; font-style: italic }
/* CommentHashbang */ .chroma .ch { color: #6e738d; font-style: italic }
/* CommentMultiline */ .chroma .cm { color: #6e738d; font-style: italic }
/* CommentSingle */ .chroma .c1 { color: #6e738d; font-style: italic }
/* CommentSpecial */ .chroma .cs { color: #6e738d; font-style: italic }
/* CommentPreproc */ .chroma .cp { color: #6e738d; font-style: italic }
/* CommentPreprocFile */ .chroma .cpf { color: #6e738d; font-weight: bold; font-style: italic }
/* Generic */ .chroma .g {  }
/* GenericDeleted */ .chroma .gd { color: #ed8796; background-color: #363a4f }
/* GenericEmph */ .chroma .ge { font-style: italic }
/* GenericError */ .chroma .gr { color: #ed8796 }
/* GenericHeading */ .chroma .gh { color: #f5a97f; font-weight: bold }
/* GenericInserted */ .chroma .gi { color: #a6da95; background-color: #363a4f }
/* GenericOutput */ .chroma .go {  }
/* GenericPrompt */ .chroma .gp {  }
/* GenericStrong */ .chroma .gs { font-weight: bold }
/* GenericSubheading */ .chroma .gu { color: #f5a97f; font-weight: bold }
/* GenericTraceback */ .chroma .gt { color: #ed8796 }
/* GenericUnderline */ .chroma .gl { text-decoration: underline }
/* TextWhitespace */ .chroma .w {  }
