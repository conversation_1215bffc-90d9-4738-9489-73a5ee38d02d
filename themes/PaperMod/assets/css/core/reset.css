*,
::after,
::before {
    box-sizing: border-box;
}

html {
    -webkit-tap-highlight-color: transparent;
    overflow-y: scroll;
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

a,
button,
body,
h1,
h2,
h3,
h4,
h5,
h6 {
    color: var(--primary);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-size: 18px;
    line-height: 1.6;
    word-break: break-word;
    background: var(--theme);
}

article,
aside,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
table {
    display: block;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    line-height: 1.2;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
    margin-top: 0;
    margin-bottom: 0;
}

ul {
    padding: 0;
}

a {
    text-decoration: none;
}

body,
figure,
ul {
    margin: 0;
}

table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    overflow-x: auto;
    word-break: keep-all;
}

button,
input,
textarea {
    padding: 0;
    font: inherit;
    background: 0 0;
    border: 0;
}

input,
textarea {
    outline: 0;
}

button,
input[type=button],
input[type=submit] {
    cursor: pointer;
}

input:-webkit-autofill,
textarea:-webkit-autofill {
    box-shadow: 0 0 0 50px var(--theme) inset;
}

img {
    display: block;
    max-width: 100%;
}
