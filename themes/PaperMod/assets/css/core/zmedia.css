@media screen and (max-width: 768px) {
    /* theme-vars */
    :root {
        --gap: 14px;
    }

    /* profile-mode */
    .profile img {
        transform: scale(0.85);
    }

    /* post-entry */
    .first-entry {
        min-height: 260px;
    }

    /* archive */
    .archive-month {
        flex-direction: column;
    }

    .archive-year {
        margin-top: 20px;
    }

    /* footer */
    .footer {
        padding: calc((var(--footer-height) - var(--gap) - 10px) / 2) var(--gap);
    }
}

/* footer */
@media screen and (max-width: 900px) {
    .list .top-link {
        transform: translateY(-5rem);
    }
}

@media screen and (max-width: 340px) {
    .share-buttons {
        justify-content: unset;
    }
}

@media (prefers-reduced-motion) {
    /* terms; profile-mode; post-single; post-entry; post-entry; search; search */
    .terms-tags a:active,
    .button:active,
    .post-entry:active,
    .top-link,
    #searchResults .focus,
    #searchResults li:active {
        transform: none;
    }
}
