#searchbox input {
    padding: 4px 10px;
    width: 100%;
    color: var(--primary);
    font-weight: bold;
    border: 2px solid var(--tertiary);
    border-radius: var(--radius);
}

#searchbox input:focus {
    border-color: var(--secondary);
}

#searchResults li {
    list-style: none;
    border-radius: var(--radius);
    padding: 10px;
    margin: 10px 0;
    position: relative;
    font-weight: 500;
}

#searchResults {
    margin: 10px 0;
    width: 100%;
}

#searchResults li:active {
    transition: transform 0.1s;
    transform: scale(0.98);
}

#searchResults a {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    outline: none;
}

#searchResults .focus {
    transform: scale(0.98);
    border: 2px solid var(--tertiary);
}
