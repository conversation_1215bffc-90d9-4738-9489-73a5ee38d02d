.main {
    position: relative;
    min-height: calc(100vh - var(--header-height) - var(--footer-height));
    max-width: calc(var(--main-width) + var(--gap) * 2);
    margin: auto;
    padding: var(--gap);
}

.page-header h1 {
    font-size: 40px;
}

.pagination {
    display: flex;
}

.pagination a {
    color: var(--theme);
    font-size: 13px;
    line-height: 36px;
    background: var(--primary);
    border-radius: calc(36px / 2);
    padding: 0 16px;
}

.pagination .next {
    margin-inline-start: auto;
}


.social-icons a {
    display: inline-flex;
    padding: 10px;
}

.social-icons a svg {
    height: 26px;
    width: 26px;
}

code {
    direction: ltr;
}

div.highlight,
pre {
    position: relative;
}

.copy-code {
    display: none;
    position: absolute;
    top: 4px;
    right: 4px;
    color: rgba(255, 255, 255, 0.8);
    background: rgba(78, 78, 78, 0.8);
    border-radius: var(--radius);
    padding: 0 5px;
    font-size: 14px;
    user-select: none;
}

div.highlight:hover .copy-code,
pre:hover .copy-code {
    display: block;
}
