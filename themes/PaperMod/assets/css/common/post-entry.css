.first-entry {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 320px;
    margin: var(--gap) 0 calc(var(--gap) * 2) 0;
}

.first-entry .entry-header {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

.first-entry .entry-header h1 {
    font-size: 34px;
    line-height: 1.3;
}

.first-entry .entry-content {
    margin: 14px 0;
    font-size: 16px;
    -webkit-line-clamp: 3;
}

.first-entry .entry-footer {
    font-size: 14px;
}

.home-info .entry-content {
    -webkit-line-clamp: unset;
}

.post-entry {
    position: relative;
    margin-bottom: var(--gap);
    padding: var(--gap);
    background: var(--entry);
    border-radius: var(--radius);
    transition: transform 0.1s;
    border: 1px solid var(--border);
}

.post-entry:active {
    transform: scale(0.96);
}

.tag-entry .entry-cover {
    display: none;
}

.entry-header h2 {
    font-size: 24px;
    line-height: 1.3;
}

.entry-content {
    margin: 8px 0;
    color: var(--secondary);
    font-size: 14px;
    line-height: 1.6;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.entry-footer {
    color: var(--secondary);
    font-size: 13px;
}

.entry-link {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}

.entry-hint {
    color: var(--secondary);
}

.entry-hint-parent {
    display: flex;
    justify-content: space-between;
}

.entry-cover {
    font-size: 14px;
    margin-bottom: var(--gap);
    text-align: center;
}

.entry-cover img {
    border-radius: var(--radius);
    pointer-events: none;
    width: 100%;
    height: auto;
}

.entry-cover a {
    color: var(--secondary);
    box-shadow: 0 1px 0 var(--primary);
}
