{{- if (.Param "ShowBreadCrumbs") -}}
<div class="breadcrumbs">
    {{- $url := replace .Parent.Permalink (printf "%s" site.Home.Permalink) "" }}
    {{- $lang_url := strings.TrimPrefix (printf "%s/" .Lang) $url -}}

    <a href="{{ "" | absLangURL }}">{{ i18n "home" | default "Home" }}</a>
    {{- $scratch := newScratch }}
    {{- range $index, $element := split $lang_url "/" }}

    {{- $scratch.Add "path" (printf "%s/" $element )}}
    {{- $bc_pg := site.GetPage ($scratch.Get "path") -}}

    {{- if (and ($bc_pg) (gt (len . ) 0))}}
    {{- print "&nbsp;»&nbsp;" | safeHTML -}}<a href="{{ $bc_pg.Permalink }}">{{ $bc_pg.Name }}</a>
    {{- end }}

    {{- end -}}
</div>
{{- end -}}
