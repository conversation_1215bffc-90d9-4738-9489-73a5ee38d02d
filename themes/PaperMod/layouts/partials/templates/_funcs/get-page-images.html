{{- $imgs := slice }}
{{- $imgParams := .Params.images }}
{{- $resources := .Resources.ByType "image" -}}
{{/* Find featured image resources if the images parameter is empty. */}}
{{- if not $imgParams }}
  {{- $featured := $resources.GetMatch "*feature*" -}}
  {{- if not $featured }}{{ $featured = $resources.GetMatch "{*cover*,*thumbnail*}" }}{{ end -}}
  {{- with $featured }}
    {{- $imgs = $imgs | append (dict
      "Image" .
      "RelPermalink" .RelPermalink
      "Permalink" .Permalink) }}
  {{- end }}
{{- end }}
{{/* Use the first one of site images as the fallback. */}}
{{- if and (not $imgParams) (not $imgs) }}
  {{- with site.Params.images }}
    {{- $imgParams = first 1 . }}
  {{- end }}
{{- end }}
{{/* Parse page's images parameter. */}}
{{- range $imgParams }}
  {{- $img := . }}
  {{- $url := urls.Parse $img }}
  {{- if eq $url.Scheme "" }}
    {{/* Internal image. */}}
    {{- with $resources.GetMatch $img -}}
      {{/* Image resource. */}}
      {{- $imgs = $imgs | append (dict
        "Image" .
        "RelPermalink" .RelPermalink
        "Permalink" .Permalink) }}
    {{- else }}
      {{- $imgs = $imgs | append (dict
        "RelPermalink" (relURL $img)
        "Permalink" (absURL $img)
      ) }}
    {{- end }}
  {{- else }}
    {{/* External image */}}
    {{- $imgs = $imgs | append (dict
      "RelPermalink" $img
      "Permalink" $img
    ) }}
  {{- end }}
{{- end }}
{{- return $imgs }}
