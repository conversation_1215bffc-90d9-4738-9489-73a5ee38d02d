{{- if .Params.cover.image -}}
<meta name="twitter:card" content="summary_large_image">
{{- if (ne $.Params.cover.relative true) }}
<meta name="twitter:image" content="{{ .Params.cover.image | absURL }}">
{{- else }}
<meta name="twitter:image" content="{{ (path.Join .RelPermalink .Params.cover.image ) | absURL }}">
{{- end}}
{{- else }}
{{- $images := partial "templates/_funcs/get-page-images" . -}}
{{- with index $images 0 -}}
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:image" content="{{ .Permalink }}">
{{- else -}}
<meta name="twitter:card" content="summary">
{{- end -}}
{{- end }}
<meta name="twitter:title" content="{{ .Title }}">
<meta name="twitter:description" content="{{ with .Description }}{{ . }}{{ else }}{{if .IsPage}}{{ .Summary }}{{ else }}{{ with site.Params.description }}{{ . }}{{ end }}{{ end }}{{ end -}}">

{{- $twitterSite := "" }}
{{- with site.Params.social }}
  {{- if reflect.IsMap . }}
    {{- with .twitter }}
      {{- $content := . }}
      {{- if not (strings.HasPrefix . "@") }}
        {{- $content = printf "@%v" . }}
      {{- end }}
      <meta name="twitter:site" content="{{ $content }}">
    {{- end }}
  {{- end }}
{{- end }}
