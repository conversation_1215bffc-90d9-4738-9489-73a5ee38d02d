<meta property="og:url" content="{{ .Permalink }}">

{{- with or site.Title site.Params.title | plainify }}
  <meta property="og:site_name" content="{{ . }}">
{{- end }}

{{- with or .Title site.Title site.Params.title | plainify }}
  <meta property="og:title" content="{{ . }}">
{{- end }}

{{- with or .Description .Summary site.Params.description | plainify | htmlUnescape | chomp }}
  <meta property="og:description" content="{{ . }}">
{{- end }}

{{- with or .Params.locale site.Language.LanguageCode site.Language.Lang }}
  <meta property="og:locale" content="{{ . }}">
{{- end }}

{{- if .IsPage }}
  <meta property="og:type" content="article">
  {{- with .Section }}
    <meta property="article:section" content="{{ . }}">
  {{- end }}
  {{- $ISO8601 := "2006-01-02T15:04:05-07:00" }}
  {{- with .PublishDate }}
    <meta property="article:published_time" {{ .Format $ISO8601 | printf "content=%q" | safeHTMLAttr }}>
  {{- end }}
  {{- with .Lastmod }}
    <meta property="article:modified_time" {{ .Format $ISO8601 | printf "content=%q" | safeHTMLAttr }}>
  {{- end }}
  {{- range .GetTerms "tags" | first 6 }}
    <meta property="article:tag" content="{{ .Page.Title | plainify }}">
  {{- end }}
{{- else }}
  <meta property="og:type" content="website">
{{- end }}

{{- if .Params.cover.image -}}
  {{- if (ne .Params.cover.relative true) }}
    <meta property="og:image" content="{{ .Params.cover.image | absURL }}">
  {{- else}}
    <meta property="og:image" content="{{ (path.Join .RelPermalink .Params.cover.image ) | absURL }}">
  {{- end}}
{{- else }}
  {{- with partial "templates/_funcs/get-page-images" . }}
    {{- range . | first 6 }}
      <meta property="og:image" content="{{ .Permalink }}">
    {{- end }}
  {{- end }}
{{- end }}

{{- with .Params.audio }}
  {{- range . | first 6  }}
    <meta property="og:audio" content="{{ . | absURL }}">
  {{- end }}
{{- end }}

{{- with .Params.videos }}
  {{- range . | first 6 }}
    <meta property="og:video" content="{{ . | absURL }}">
  {{- end }}
{{- end }}

{{- range .GetTerms "series" }}
  {{- range .Pages | first 7 }}
    {{- if ne $ . }}
      <meta property="og:see_also" content="{{ .Permalink }}">
    {{- end }}
  {{- end }}
{{- end }}

{{- with site.Params.social }}
  {{- if reflect.IsMap . }}
    {{- with .facebook_app_id }}
      <meta property="fb:app_id" content="{{ . }}">
    {{- else }}
      {{- with .facebook_admin }}
        <meta property="fb:admins" content="{{ . }}">
      {{- end }}
    {{- end }}
  {{- end }}
{{- end }}
