{{- if .IsTranslated -}}
{{- if (ne .Layout "search") }}
{{- if or .Params.author site.Params.author (.Param "ShowReadingTime") (not .Date.IsZero) }}&nbsp;|&nbsp;{{- end -}}
{{- end }}
{{- i18n "translations" | default "Translations" }}:
<ul class="i18n_list">
    {{- range .Translations }}
    <li>
        <a href="{{ .Permalink }}">
            {{- if (and site.Params.displayFullLangName (.Language.LanguageName)) }}
            {{- .Language.LanguageName | emojify -}}
            {{- else }}
            {{- .<PERSON> | title -}}
            {{- end -}}
        </a>
    </li>
    {{- end }}
</ul>
{{- end -}}
