{{- with site.Params.homeInfoParams }}
<article class="first-entry home-info">
    <header class="entry-header">
        <h1>{{ .Title | markdownify }}</h1>
    </header>
    <div class="entry-content">
        {{ .Content | markdownify }}
    </div>
    <footer class="entry-footer">
        {{ partial "social_icons.html" (dict "align" site.Params.homeInfoParams.AlignSocialIconsTo) }}
    </footer>
</article>
{{- end -}}
