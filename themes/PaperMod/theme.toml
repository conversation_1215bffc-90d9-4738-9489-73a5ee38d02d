# theme.toml template for a Hugo theme
# See https://github.com/gohugoio/hugoThemes#themetoml for an example

name = "PaperMod"
license = "MIT"
licenselink = "https://github.com/adityatelange/hugo-PaperMod/blob/master/LICENSE"
description = "A fast, clean, responsive Hugo theme"
homepage = "https://adityatelange.github.io/hugo-PaperMod/"
tags = [
  "responsive",
  "simple",
  "clean",
  "light",
  "dark",
  "blog",
  "minimalist",
  "highlight.js",
  "search"
]
features = [
  "responsive",
  "single-column",
  "blog",
  "cover-image",
  "table-of-contents",
  "opengraph",
  "highlight.js",
  "favicon",
  "archive",
  "share-icons",
  "cover",
  "multilingual",
  "social-icons",
  "minified-assets",
  "theme-toggle",
  "menu-location-indicator",
  "scroll-to-top",
  "search"
]
min_version = "0.125.7"

[author]
  name = "Aditya Telange"
  homepage = "https://github.com/adityatelange/"

# If porting an existing theme
[original]
  name = "Paper"
  author = "nanxiaobei"
  homepage = "https://github.com/nanxiaobei"
  repo = "https://github.com/nanxiaobei/hugo-paper/"
