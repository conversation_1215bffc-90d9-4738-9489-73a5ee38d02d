name: Deploy <PERSON>od Demo to Pages

on:
  push:
    paths-ignore:
      - "images/**"
      - "LICENSE"
      - "README.md"
    branches:
      - master
      - exampleSite
  workflow_dispatch:
    # manual run
    inputs:
      hugoVersion:
        description: "Hugo Version"
        required: false
        default: "0.125.7"

# Allow one concurrent deployment
concurrency:
  group: "pages"
  cancel-in-progress: true

# Default to bash
defaults:
  run:
    shell: bash

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

jobs:
  # Build job
  build:
    runs-on: ubuntu-latest
    env:
      HUGO_VERSION: ${{ github.event.inputs.hugoVersion || '0.125.7' }}
    steps:
      - name: Install Hugo CLI
        run: |
          wget -O ${{ runner.temp }}/hugo.deb https://github.com/gohugoio/hugo/releases/download/v${HUGO_VERSION}/hugo_${HUGO_VERSION}_linux-amd64.deb \
          && sudo dpkg -i ${{ runner.temp }}/hugo.deb
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: exampleSite
      - name: Setup Pages
        id: pages
        uses: actions/configure-pages@v5
      - name: Get Theme
        run: git submodule update --init --recursive
      - name: Update theme to Latest commit
        run: git submodule update --remote --merge
      - name: Build with Hugo
        run: |
          hugo \
            --buildDrafts --gc \
            --baseURL ${{ steps.pages.outputs.base_url }}
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./public
  # Deployment job
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
