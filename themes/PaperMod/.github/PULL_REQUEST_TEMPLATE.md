<!--

## READ BEFORE OPENING A PR

Thank you for contributing to hugo-PaperMod!
Please fill out the following questions to make it easier for us to review your
changes. You do not need to check all the boxes below.

**NOTE**: PaperMod does not have any external dependencies fetched from 3rd party
CDN servers. However we do have custom Head/Footer extender templates which you can use
to add those to your website.
https://github.com/adityatelange/hugo-PaperMod/wiki/FAQs#custom-head--footer

-->


**What does this PR change? What problem does it solve?**

<!--
Describe the changes and their purpose here, as detailed as and if  needed.

Please do not add 2 unrelated changes in a single PR as it is difficult to track/revert those in future.
-->


**Was the change discussed in an issue or in the Discussions before?**

<!--
Link issues and relevant Discussions posts here.

If this PR resolves an issue on GitHub, use "Closes #1234" so that the issue
is closed automatically when this PR is merged.
-->


## PR Checklist

- [ ] This change adds/updates translations and I have used the [template present here](https://github.com/adityatelange/hugo-PaperMod/wiki/Translations#want-to-add-your-language-).
- [ ] I have enabled [maintainer edits for this PR](https://help.github.com/en/github/collaborating-with-issues-and-pull-requests/allowing-changes-to-a-pull-request-branch-created-from-a-fork).
- [ ] I have verified that the code works as described/as intended.
- [ ] This change adds a Social Icon which has a permissive license to use it.
- [ ] This change **does not** include any CDN resources/links.
- [ ] This change **does not** include any unrelated scripts such as bash and python scripts.
- [ ] This change updates the overridden internal templates from HUGO's repository.
