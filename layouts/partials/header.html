{{- /* theme-toggle is enabled */}}
{{- if (not .Site.Params.disableThemeToggle) }}
{{- /* theme is light */}}
{{- if (eq .Site.Params.defaultTheme "light") }}
<script>
  if (localStorage.getItem("pref-theme") === "dark") {
    document.body.classList.add('dark');
  }

</script>
{{- /* theme is dark */}}
{{- else if (eq .Site.Params.defaultTheme "dark") }}
<script>
  if (localStorage.getItem("pref-theme") === "light") {
    document.body.classList.remove('dark')
  }

</script>
{{- else }}
{{- /* theme is auto */}}
<script>
  if (localStorage.getItem("pref-theme") === "dark") {
    document.body.classList.add('dark');
  } else if (localStorage.getItem("pref-theme") === "light") {
    document.body.classList.remove('dark')
  } else if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
    document.body.classList.add('dark');
  }

</script>
{{- end }}
{{- /* theme-toggle is disabled and theme is auto */}}
{{- else if (and (ne .Site.Params.defaultTheme "light") (ne .Site.Params.defaultTheme "dark"))}}
<script>
  if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
    document.body.classList.add('dark');
  }

</script>
{{- end }}
<noscript>
  <style type="text/css">
    #theme-toggle,
    .top-link {
      display: none;
    }
  </style>
  {{- if (and (ne .Site.Params.defaultTheme "light") (ne .Site.Params.defaultTheme "dark")) }}
  <style>
    @media (prefers-color-scheme: dark) {
      :root {
        --theme: #1d1e20;
        --entry: #2e2e33;
        --primary: rgba(255, 255, 255, 0.84);
        --secondary: rgba(255, 255, 255, 0.56);
        --tertiary: rgba(255, 255, 255, 0.16);
        --content: rgba(255, 255, 255, 0.74);
        --hljs-bg: #2e2e33;
        --code-bg: #37383e;
        --border: #333;
      }

      .list {
        background: var(--theme);
      }

      .list:not(.dark)::-webkit-scrollbar-track {
        background: 0 0;
      }

      .list:not(.dark)::-webkit-scrollbar-thumb {
        border-color: var(--theme);
      }
    }
  </style>
  {{- end }}
</noscript>

{{- if not .IsHome }}
<header class="header">
  <nav class="nav">
    <div class="logo">
      {{- $label_text := (.Site.Params.label.text | default .Site.Title) }}
      {{- $label_icon := (.Site.Params.label.icon | default .Site.Params.icon) }}
      {{- $label_icon_height := (.Site.Params.label.iconHeight | default " 30px") }}
      {{- if .Site.Title }}
      <a href="{{ "" | absLangURL }}" accesskey="h" title="{{ $label_text }} (Alt + H)">
        {{- if $label_icon }}
        <img src="{{- $label_icon -}}" alt="logo" aria-label="logo"
          height="{{- $label_icon_height -}}">
        {{- end -}}
        {{- $label_text -}}
      </a>
      {{- end }}
    </div>
    {{- $currentPage := . }}
    <ul id="menu">
      {{- range .Site.Menus.main }}
      {{- $menu_item_url := (cond (strings.HasSuffix .URL "/") .URL (printf "%s/" .URL) ) | absLangURL }}
      {{- $page_url:= $currentPage.Permalink | absLangURL }}
      {{- $is_search := eq ($.Site.GetPage .KeyName).Layout `search` }}
      <li>
        <a href="{{ .URL | absLangURL }}" title="{{ .Title | default .Name }} {{- cond $is_search (" (Alt + /)" | safeHTMLAttr) ("" | safeHTMLAttr ) }}"
          {{- cond $is_search (" accesskey=/" | safeHTMLAttr) ("" | safeHTMLAttr ) }}>
          <span {{- if eq $menu_item_url $page_url }} class="active" {{- end }}>
            {{- .Name -}}
          </span>
        </a>
      </li>
      {{- end }}
      {{- if (not .Site.Params.disableThemeToggle) }}
      <li class="theme-toggle">
        <button id="theme-toggle" accesskey="t" title="(Alt + T)">
          <svg id="moon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round">
            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
          </svg>
          <svg id="sun" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round">
            <circle cx="12" cy="12" r="5"></circle>
            <line x1="12" y1="1" x2="12" y2="3"></line>
            <line x1="12" y1="21" x2="12" y2="23"></line>
            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
            <line x1="1" y1="12" x2="3" y2="12"></line>
            <line x1="21" y1="12" x2="23" y2="12"></line>
            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
          </svg>
        </button>
      </li>
      {{- end }}
    </ul>
  </nav>
</header>
{{- end }}
