{{- define "main" }}

<article class="post-single">
  <header class="post-header">
    {{ partial "breadcrumbs.html" . }}
    <h1 class="post-title entry-hint-parent">
      {{ .Title }}
      {{- if .Draft }}
      <span class="entry-hint" title="Draft">
        <svg xmlns="http://www.w3.org/2000/svg" height="35" viewBox="0 -960 960 960" fill="currentColor">
          <path
            d="M160-410v-60h300v60H160Zm0-165v-60h470v60H160Zm0-165v-60h470v60H160Zm360 580v-123l221-220q9-9 20-13t22-4q12 0 23 4.5t20 13.5l37 37q9 9 13 20t4 22q0 11-4.5 22.5T862.09-380L643-160H520Zm300-263-37-37 37 37ZM580-220h38l121-122-18-19-19-18-122 121v38Zm141-141-19-18 37 37-18-19Z" />
        </svg>
      </span>
      {{- end }}
    </h1>
    {{- if .Description }}
    <div class="post-description">
      {{ .Description }}
    </div>
    {{- end }}
    {{- if not (.Param "hideMeta") }}
    <div class="post-meta">
      {{- partial "post_meta.html" . -}}
      {{- partial "translation_list.html" . -}}
      {{- partial "edit_post.html" . -}}
      {{- partial "post_canonical.html" . -}}
    </div>
    {{- end }}
    {{- $tags := .Language.Params.Taxonomies.tag | default "tags" }}
    <ul class="post-tags">
      {{- range ($.GetTerms $tags) }}
      <li><a href="{{ .Permalink }}">{{ .LinkTitle }}</a></li>
      {{- end }}
    </ul>
  </header>
  {{- $isHidden := (.Param "cover.hiddenInSingle") | default (.Param "cover.hidden") | default false }}
  {{- partial "cover.html" (dict "cxt" . "IsSingle" true "isHidden" $isHidden) }}
  {{- if (.Param "ShowToc") }}
  {{- partial "toc.html" . }}
  {{- end }}

  {{- if .Content }}
  <div class="post-content">
    {{- if not (.Param "disableAnchoredHeadings") }}
    {{- partial "anchored_headings.html" .Content -}}
    {{- else }}{{ .Content }}{{ end }}
  </div>
  {{- end }}

  <footer class="post-footer">
    {{- if (.Param "ShowPostNavLinks") }}
    {{- partial "post_nav_links.html" . }}
    {{- end }}
    {{- if (and site.Params.ShowShareButtons (ne .Params.disableShare true)) }}
    {{- partial "share-buttons.html" . -}}
    {{- end }}
  </footer>

  {{- if eq .Type "posts" }}
  <div class="post-newsletter">
    <div class="newsletter-content">
      <h2>Stay up to date</h2>
      <p>Subscribe to my newsletter to get notified about new blog posts and receive content about Elixir, Ruby, Open Source, software design, building products, and more.</p>
    </div>
    {{ partial "newsletter.html" . }}
  </div>
  {{- end }}
</article>

<style>
  .post-newsletter {
    margin: 4rem 0 2rem;
    padding: 2rem;
    background: var(--entry);
    border-radius: var(--radius);
    border: 1px solid var(--border);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .post-newsletter .newsletter-content {
    margin-bottom: 1.5rem;
  }

  .post-newsletter h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #4a90e2, #9b51e0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  [theme="dark"] .post-newsletter h2 {
    background: linear-gradient(45deg, #60a5fa, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .post-newsletter p {
    color: var(--secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 0;
  }

  /* Newsletter form styles - unified for both home and single post pages */
  .post-newsletter .embeddable-buttondown-form {
    display: flex;
    width: 100%;
    margin: 0;
    padding: 0;
  }

  .post-newsletter .embeddable-buttondown-form label {
    display: none;
  }

  .post-newsletter .embeddable-buttondown-form input[type="email"] {
    flex: 1;
    min-width: 0;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border: 1px solid var(--border);
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
    border-right: none;
    background: var(--theme);
    color: var(--primary);
    height: 48px;
  }

  .post-newsletter .embeddable-buttondown-form input[type="email"]::placeholder {
    color: var(--secondary);
    opacity: 0.8;
  }

  .post-newsletter .embeddable-buttondown-form input[type="email"]:focus {
    outline: none;
    border-color: var(--primary);
  }

  .post-newsletter .embeddable-buttondown-form input[type="submit"] {
    padding: 0 1.5rem;
    font-size: 1rem;
    border: none;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    background: var(--primary);
    color: var(--theme);
    cursor: pointer;
    white-space: nowrap;
    transition: opacity 0.2s;
    height: 48px;
  }

  .post-newsletter .embeddable-buttondown-form input[type="submit"]:hover {
    opacity: 0.9;
  }

  .post-newsletter .embeddable-buttondown-form p {
    display: none;
  }

  @media (max-width: 768px) {
    .post-newsletter {
      padding: 1.5rem;
      margin: 3rem 0 1.5rem;
    }

    .post-newsletter h2 {
      font-size: 1.8rem;
    }

    .post-newsletter p {
      font-size: 1rem;
    }

    .post-newsletter .embeddable-buttondown-form {
      flex-direction: column;
      gap: 0.75rem;
    }

    .post-newsletter .embeddable-buttondown-form input[type="email"] {
      border-radius: 6px;
      border-right: 1px solid var(--border);
    }

    .post-newsletter .embeddable-buttondown-form input[type="submit"] {
      width: 100%;
      border-radius: 6px;
    }
  }
</style>

{{- end }}{{/* end main */}}
