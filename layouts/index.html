{{- define "main" }}

<div class="home-layout">
  <!-- Left column: Profile section -->
  <div class="profile-section">
    {{- if site.Params.profileMode.imageUrl }}
    <img src="{{ site.Params.profileMode.imageUrl }}"
      alt="{{ site.Params.profileMode.imageTitle | default " Profile" }}"
      class="profile-image"
      width="{{ site.Params.profileMode.imageWidth }}"
      height="{{ site.Params.profileMode.imageHeight }}">
    {{- end }}

    <h1 class="profile-title">{{ site.Params.profileMode.imageTitle }}</h1>

    {{- with site.Params.profileMode.subtitle }}
    <div class="profile-subtitle">{{ . }}</div>
    {{- end }}

    <!-- Social Icons -->
    <div class="social-icons">
      {{- range site.Params.socialIcons }}
      <a href="{{ .url }}" target="_blank" rel="noopener noreferrer me" title="{{ .name | title }}">
        {{ partial "svg.html" . }}
      </a>
      {{- end }}
    </div>

    <!-- Navigation Buttons -->
    <div class="profile-buttons">
      {{- range site.Params.profileMode.buttons }}
      <a class="button" href="{{ .url }}">{{ .name }}</a>
      {{- end }}
    </div>
  </div>

  <!-- Right column: Products and Newsletter -->
  <div class="content-section">
    <!-- Products Showcase -->
    <div class="products-section">
      <h2>Latest Products</h2>
      <div class="startup-grid">
        <!-- JustCrosspost -->
        <div class="startup-card justcrosspost">
          <div class="startup-screenshot">
            <img src="{{ site.Params.startups.justcrosspost.screenshot }}" alt="JustCrosspost.app screenshot" />
          </div>
          <h3>JustCrossPost.app</h3>
          <p>{{ site.Params.startups.justcrosspost.description }}</p>
          <a href="https://justcrosspost.app" class="button" target="_blank">Learn More</a>
        </div>
        <!-- RepoBot -->
        <div class="startup-card repobot">
          <div class="startup-screenshot">
            <img src="{{ site.Params.startups.repobot.screenshot }}" alt="RepoBot.app screenshot" />
          </div>
          <h3>RepoBot.app</h3>
          <p>{{ site.Params.startups.repobot.description }}</p>
          <a href="https://repobot.app" class="button" target="_blank">Learn More</a>
        </div>
      </div>

      <!-- Newsletter Signup -->
      <div class="home-newsletter">
        <div class="newsletter-content">
          <h2>Stay up to date</h2>
          <p>Subscribe to my newsletter to get notified about new blog posts and receive content about Elixir, Ruby, Open Source, software design, building products, and more.</p>
        </div>
        {{ partial "newsletter.html" . }}
      </div>
    </div>
  </div>
</div>

<!-- Posts Section -->
<div class="posts-section">
  <h2>Latest Posts</h2>
  <div class="posts-grid">
    {{- $pages := where site.RegularPages "Type" "in" site.Params.mainSections }}
    {{- range first 6 $pages }}
    <article class="post-entry">
      <header class="entry-header">
        <h3>{{ .Title }}</h3>
        <time class="entry-date">{{ .Date.Format "January 2, 2006" }}</time>
      </header>
      <div class="entry-content">
        <p>{{ .Summary | truncate 150 }}</p>
      </div>
      <footer class="entry-footer">
        {{- if .Params.tags }}
        <div class="entry-tags">
          {{- range .Params.tags }}
          <a href="/tags/{{ . | urlize }}">{{ . }}</a>
          {{- end }}
        </div>
        {{- end }}
      </footer>
      <a class="entry-link" aria-label="post link to {{ .Title }}" href="{{ .Permalink }}"></a>
    </article>
    {{- end }}
  </div>
</div>

<style>
  .home-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .profile-section {
    position: sticky;
    top: 2rem;
    height: fit-content;
  }

  .profile-image {
    width: 100%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 1.5rem;
  }

  .profile-title {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  .profile-subtitle {
    color: var(--secondary);
    margin-bottom: 1.5rem;
  }

  .social-icons {
    display: flex;
    gap: 1rem;
    margin: 1.5rem 0;
  }

  .profile-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .content-section {
    display: flex;
    flex-direction: column;
  }

  .products-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .startup-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .startup-card {
    padding: 1.5rem;
    border-radius: 8px;
    background: var(--entry);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .startup-card:hover {
    transform: translateY(-2px);
  }

  .startup-card.justcrosspost {
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(147, 51, 234, 0.05) 100%);
    border: 1px solid rgba(147, 51, 234, 0.2);
  }

  .startup-card.repobot {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  .startup-screenshot {
    margin: -1.5rem -1.5rem 1.5rem -1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: #fff;
    padding: 1rem;
  }

  .startup-screenshot img {
    width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .startup-card h3 {
    margin-bottom: 1rem;
    font-size: 1.4rem;
    color: var(--primary);
  }

  .startup-card p {
    margin-bottom: 1.5rem;
    flex-grow: 1;
  }

  .startup-card .button {
    align-self: flex-start;
    margin-top: auto;
    padding: 0.5rem 1rem;
    text-decoration: none;
    border-radius: 4px;
    color: #fff;
    transition: opacity 0.2s;
  }

  .startup-card .button:hover {
    opacity: 0.9;
  }

  .startup-card.justcrosspost .button {
    background: rgb(147, 51, 234);
  }

  .startup-card.repobot .button {
    background: rgb(59, 130, 246);
  }

  .home-newsletter {
    padding: 1.5rem;
    background: var(--entry);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-top: 1.5rem;
  }

  .home-newsletter .newsletter-content {
    margin-bottom: 1.5rem;
  }

  .home-newsletter h2 {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
    background: linear-gradient(45deg, #4a90e2, #9b51e0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  [theme="dark"] .home-newsletter h2 {
    background: linear-gradient(45deg, #60a5fa, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .home-newsletter p {
    color: var(--secondary);
    font-size: 0.95rem;
    line-height: 1.5;
    margin: 0;
  }

  /* Newsletter form styles - unified for both home and single post pages */
  .embeddable-buttondown-form {
    display: flex;
    width: 100%;
    margin: 0;
    padding: 0;
  }

  .embeddable-buttondown-form label {
    display: none;
  }

  .embeddable-buttondown-form input[type="email"] {
    flex: 1;
    min-width: 0;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border: 1px solid var(--border);
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
    border-right: none;
    background: var(--theme);
    color: var(--primary);
    height: 48px;
  }

  .embeddable-buttondown-form input[type="email"]::placeholder {
    color: var(--secondary);
    opacity: 0.8;
  }

  .embeddable-buttondown-form input[type="email"]:focus {
    outline: none;
    border-color: var(--primary);
  }

  .embeddable-buttondown-form input[type="submit"] {
    padding: 0 1.5rem;
    font-size: 1rem;
    border: none;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    background: var(--primary);
    color: var(--theme);
    cursor: pointer;
    white-space: nowrap;
    transition: opacity 0.2s;
    height: 48px;
  }

  .embeddable-buttondown-form input[type="submit"]:hover {
    opacity: 0.9;
  }

  .embeddable-buttondown-form p {
    display: none;
  }

  .posts-section {
    margin-top: 4rem;
    max-width: 1200px;
    margin: 4rem auto 0;
    padding: 0 2rem;
  }

  .posts-section h2 {
    font-size: 1.8rem;
    margin-bottom: 2rem;
  }

  .posts-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .post-entry {
    background: var(--entry);
    border-radius: 8px;
    padding: 1.5rem;
    position: relative;
    transition: transform 0.2s;
    border: 1px solid var(--border);
    height: 100%;
  }

  .post-entry:hover {
    transform: translateY(-2px);
  }

  .entry-header h3 {
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
  }

  .entry-date {
    color: var(--secondary);
    font-size: 0.9rem;
  }

  .entry-content {
    margin: 1rem 0;
  }

  .entry-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .entry-tags a {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
    background: var(--tertiary);
    border-radius: 4px;
    color: var(--secondary);
    text-decoration: none;
  }

  @media (max-width: 1200px) {
    .posts-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 768px) {
    .home-layout {
      grid-template-columns: 1fr;
    }

    .profile-section {
      position: static;
      margin-bottom: 2rem;
      text-align: center;
    }

    .profile-image {
      max-width: 200px;
      margin: 0 auto 1.5rem;
    }

    .social-icons {
      justify-content: center;
    }

    .startup-grid,
    .posts-grid {
      grid-template-columns: 1fr;
    }

    .embeddable-buttondown-form {
      flex-direction: column;
      gap: 0.75rem;
    }

    .embeddable-buttondown-form input[type="email"] {
      border-radius: 6px;
      border-right: 1px solid var(--border);
    }

    .embeddable-buttondown-form input[type="submit"] {
      width: 100%;
      border-radius: 6px;
    }

    .home-newsletter {
      padding: 1.25rem;
    }

    .home-newsletter h2 {
      font-size: 1.25rem;
    }

    .home-newsletter p {
      font-size: 0.9rem;
    }
  }
</style>

{{- end }}
