const fs = require('fs');
const path = require('path');
const Handlebars = require('handlebars');

/**
 * Template engine using Handlebars.js
 * Supports variable interpolation, loops, conditionals, and component includes
 */
class TemplateEngine {
  constructor(templatesDir = 'templates') {
    this.templatesDir = path.resolve(templatesDir);
    this.templateCache = new Map();
    this.compiledTemplateCache = new Map();
    this.handlebars = Handlebars.create();

    // Register partials (components) on initialization
    this._registerPartials();
  }

  /**
   * Register all partials (components) with Handlebars
   */
  _registerPartials() {
    const componentsDir = path.join(this.templatesDir, 'components');

    if (!fs.existsSync(componentsDir)) {
      return;
    }

    const componentFiles = fs.readdirSync(componentsDir);

    for (const file of componentFiles) {
      if (file.endsWith('.hbs')) {
        const componentName = path.basename(file, '.hbs');
        const componentPath = path.join(componentsDir, file);
        const content = fs.readFileSync(componentPath, 'utf8');
        this.handlebars.registerPartial(componentName, content);
      }
    }
  }

  /**
   * Load a template file and cache it
   */
  loadTemplate(templatePath) {
    const fullPath = path.join(this.templatesDir, templatePath);

    if (this.templateCache.has(fullPath)) {
      return this.templateCache.get(fullPath);
    }

    if (!fs.existsSync(fullPath)) {
      throw new Error(`Template not found: ${fullPath}`);
    }

    const content = fs.readFileSync(fullPath, 'utf8');
    this.templateCache.set(fullPath, content);
    return content;
  }

  /**
   * Compile a template using Handlebars and cache the compiled function
   */
  compileTemplate(templatePath) {
    const fullPath = path.join(this.templatesDir, templatePath);

    if (this.compiledTemplateCache.has(fullPath)) {
      return this.compiledTemplateCache.get(fullPath);
    }

    const templateContent = this.loadTemplate(templatePath);
    const compiledTemplate = this.handlebars.compile(templateContent);
    this.compiledTemplateCache.set(fullPath, compiledTemplate);

    return compiledTemplate;
  }

  /**
   * Render a template with data using Handlebars
   */
  render(templatePath, data = {}) {
    const compiledTemplate = this.compileTemplate(templatePath);
    return compiledTemplate(data);
  }

  /**
   * Render a page template with embedded CSS
   */
  renderPage(pageName, data = {}) {
    const pageTemplate = this.render(`pages/${pageName}.hbs`, data);

    // Check if there's a corresponding CSS file
    const cssPath = path.join(this.templatesDir, 'styles', `${pageName}.css`);
    let css = '';

    if (fs.existsSync(cssPath)) {
      css = fs.readFileSync(cssPath, 'utf8');
    }

    // Embed CSS in the page
    if (css) {
      const styleTag = `<style>\n${css}\n</style>\n\n`;
      return styleTag + pageTemplate;
    }

    return pageTemplate;
  }

  /**
   * Clear template cache (useful for development)
   */
  clearCache() {
    this.templateCache.clear();
    this.compiledTemplateCache.clear();
    // Re-register partials after clearing cache
    this._registerPartials();
  }
}

module.exports = TemplateEngine;
