#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { GhostAPI } = require('./ghost-api');
const { ContentConverter } = require('./content-converter');

/**
 * Post synchronization utilities for Ghost and local articles
 */
class PostSync {
  constructor(config, options = {}) {
    this.config = config;
    this.options = {
      verbose: false,
      dryRun: false,
      articlesDir: 'articles',
      ...options
    };
    this.ghostAPI = new GhostAPI(config, options);
  }

  /**
   * Sync posts from Ghost to local articles directory
   */
  async syncPostsToLocal(title = null) {
    console.log('🔄 Starting post sync from Ghost to local articles...');
    
    // Find posts to sync
    const posts = await this.ghostAPI.findPosts(title);
    
    if (posts.length === 0) {
      console.log('❌ No posts found to sync');
      return { synced: 0, skipped: 0, errors: 0 };
    }
    
    console.log(`📄 Found ${posts.length} post(s) to sync`);
    
    // Ensure articles directory exists
    const articlesDir = path.resolve(this.options.articlesDir);
    if (!fs.existsSync(articlesDir)) {
      console.log(`📁 Creating articles directory: ${articlesDir}`);
      fs.mkdirSync(articlesDir, { recursive: true });
    }
    
    let syncedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    for (const post of posts) {
      try {
        // Create filename from title
        const filename = ContentConverter.createFilename(post.title) + '.md';
        const filePath = path.join(articlesDir, filename);
        
        // Convert Ghost post to article format
        const articleContent = ContentConverter.convertGhostPostToArticle(post);
        
        if (this.options.dryRun) {
          console.log(`🔍 Would sync: "${post.title}" -> ${filename}`);
          if (this.options.verbose) {
            console.log(`   File path: ${filePath}`);
            console.log(`   Content length: ${articleContent.length} characters`);
            console.log(`   Published: ${post.published_at || 'draft'}`);
            console.log(`   Tags: ${post.tags?.map(t => t.name).join(', ') || 'none'}`);
          }
          syncedCount++;
          continue;
        }
        
        // Check if file already exists
        const fileExists = fs.existsSync(filePath);
        
        if (fileExists && this.options.verbose) {
          console.log(`📝 File exists, updating: ${filename}`);
        } else if (!fileExists && this.options.verbose) {
          console.log(`📝 Creating new file: ${filename}`);
        }
        
        // Write the file
        fs.writeFileSync(filePath, articleContent, 'utf8');
        
        const action = fileExists ? 'Updated' : 'Created';
        console.log(`✅ ${action}: "${post.title}" -> ${filename}`);
        
        if (this.options.verbose) {
          console.log(`   File path: ${filePath}`);
          console.log(`   Content length: ${articleContent.length} characters`);
          console.log(`   Published: ${post.published_at || 'draft'}`);
          console.log(`   Tags: ${post.tags?.map(t => t.name).join(', ') || 'none'}`);
        }
        
        syncedCount++;
        
      } catch (error) {
        console.error(`❌ Error syncing "${post.title}":`, error.message);
        errorCount++;
      }
    }
    
    console.log(`\n✅ Sync completed!`);
    console.log(`📄 Synced ${syncedCount} post(s)`);
    if (skippedCount > 0) {
      console.log(`⏭️  Skipped ${skippedCount} post(s)`);
    }
    if (errorCount > 0) {
      console.log(`❌ Failed to sync ${errorCount} post(s)`);
    }

    return { synced: syncedCount, skipped: skippedCount, errors: errorCount };
  }

  /**
   * Sync a local article to Ghost
   */
  async syncArticleToGhost(articlePath, options = {}) {
    const {
      status = 'draft',
      useHtml = false
    } = options;

    try {
      // Read and parse the article
      const content = fs.readFileSync(articlePath, 'utf8');
      const { frontMatter, markdownContent, htmlContent } = this.parseArticle(content);

      if (!frontMatter.title) {
        throw new Error('Article must have a title in frontmatter');
      }

      // Get current user as author
      const currentUser = await this.ghostAPI.getCurrentUser();
      
      // Check if post already exists
      const slug = frontMatter.slug || ContentConverter.slugify(frontMatter.title);
      const existingPost = await this.ghostAPI.findPostBySlug(slug);

      // Create post data
      const postData = ContentConverter.createGhostPostData(
        frontMatter, 
        markdownContent, 
        htmlContent, 
        {
          status,
          author: currentUser,
          useHtml
        }
      );

      // Create or update the post
      const result = await this.ghostAPI.createOrUpdatePost(postData, existingPost);
      
      return result;

    } catch (error) {
      console.error(`❌ Error syncing article "${articlePath}":`, error.message);
      throw error;
    }
  }

  /**
   * Parse article content (frontmatter + markdown)
   */
  parseArticle(content) {
    // Extract YAML front matter
    const frontMatterMatch = content.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
    if (!frontMatterMatch) {
      throw new Error('No front matter found in article');
    }

    const yaml = require('js-yaml');
    const { marked } = require('marked');

    const frontMatter = yaml.load(frontMatterMatch[1]);
    const markdownContent = frontMatterMatch[2].trim();

    // Convert markdown to HTML
    const htmlContent = marked(markdownContent);

    return {
      frontMatter,
      markdownContent,
      htmlContent
    };
  }

  /**
   * Find all articles in the articles directory
   */
  findArticles(articlesDir = null) {
    const dir = articlesDir || this.options.articlesDir;
    const articles = [];

    try {
      const entries = fs.readdirSync(dir, { withFileTypes: true });

      for (const entry of entries) {
        if (entry.name === '_index.md') continue;

        const fullPath = path.join(dir, entry.name);

        if (entry.isDirectory()) {
          // Check for index.md in subdirectory
          const indexPath = path.join(fullPath, 'index.md');
          if (fs.existsSync(indexPath)) {
            articles.push(indexPath);
          }
        } else if (entry.name.endsWith('.md')) {
          // Direct markdown file
          articles.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading articles directory ${dir}:`, error.message);
      throw error;
    }

    return articles;
  }

  /**
   * Sync all local articles to Ghost
   */
  async syncAllArticlesToGhost(options = {}) {
    console.log('🔄 Starting sync of all local articles to Ghost...');

    const articles = this.findArticles();
    
    if (articles.length === 0) {
      console.log('❌ No articles found to sync');
      return { synced: 0, skipped: 0, errors: 0 };
    }

    console.log(`📄 Found ${articles.length} article(s) to sync`);

    let syncedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const articlePath of articles) {
      try {
        const result = await this.syncArticleToGhost(articlePath, options);
        
        if (result.action !== 'dry-run') {
          syncedCount++;
        }
        
      } catch (error) {
        console.error(`❌ Error syncing "${articlePath}":`, error.message);
        errorCount++;
      }
    }

    console.log(`\n✅ Sync completed!`);
    console.log(`📄 Synced ${syncedCount} article(s)`);
    if (skippedCount > 0) {
      console.log(`⏭️  Skipped ${skippedCount} article(s)`);
    }
    if (errorCount > 0) {
      console.log(`❌ Failed to sync ${errorCount} article(s)`);
    }

    return { synced: syncedCount, skipped: skippedCount, errors: errorCount };
  }
}

module.exports = { PostSync };
