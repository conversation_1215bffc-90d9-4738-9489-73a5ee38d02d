#!/usr/bin/env node

const yaml = require('js-yaml');
const { marked } = require('marked');
const { htmlToLexical } = require('@tryghost/kg-html-to-lexical');
const { render: renderMarkdownToHtml } = require('@tryghost/kg-markdown-html-renderer');
const crypto = require('crypto');

/**
 * Content conversion utilities for Ghost posts and local articles
 */
class ContentConverter {
  /**
   * Convert HTML content back to Markdown (basic conversion)
   */
  static htmlToMarkdown(html) {
    if (!html) return '';

    // Basic HTML to Markdown conversion
    // This is a simplified version - for production use, consider using a proper HTML to Markdown library
    return html
      .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n\n')
      .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n\n')
      .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n\n')
      .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '#### $1\n\n')
      .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '##### $1\n\n')
      .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '###### $1\n\n')
      .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')
      .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
      .replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')
      .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
      .replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')
      .replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`')
      .replace(/<pre[^>]*><code[^>]*>(.*?)<\/code><\/pre>/gis, '```\n$1\n```\n\n')
      .replace(/<pre[^>]*>(.*?)<\/pre>/gis, '```\n$1\n```\n\n')
      .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')
      .replace(/<ul[^>]*>(.*?)<\/ul>/gis, (match, content) => {
        return content.replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n') + '\n';
      })
      .replace(/<ol[^>]*>(.*?)<\/ol>/gis, (match, content) => {
        let counter = 1;
        return content.replace(/<li[^>]*>(.*?)<\/li>/gi, () => `${counter++}. $1\n`) + '\n';
      })
      .replace(/<br\s*\/?>/gi, '\n')
      .replace(/<hr\s*\/?>/gi, '\n---\n\n')
      .replace(/<[^>]*>/g, '') // Remove any remaining HTML tags
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Clean up multiple newlines
      .trim();
  }

  /**
   * Create filename from title
   */
  static createFilename(title) {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .trim();
  }

  /**
   * Create slug from title
   */
  static slugify(text) {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  /**
   * Decode HTML entities
   */
  static decodeHtmlEntities(text) {
    if (!text) return text;

    return text
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&nbsp;/g, ' ');
  }

  /**
   * Generate excerpt with 300 character limit
   */
  static generateExcerpt(frontMatter, htmlContent) {
    // First try frontmatter excerpt or description
    if (frontMatter.excerpt) {
      const decoded = this.decodeHtmlEntities(frontMatter.excerpt);
      return decoded.length > 300
        ? decoded.substring(0, 297) + '...'
        : decoded;
    }

    if (frontMatter.description) {
      const decoded = this.decodeHtmlEntities(frontMatter.description);
      return decoded.length > 300
        ? decoded.substring(0, 297) + '...'
        : decoded;
    }

    // Extract from content if no frontmatter excerpt
    if (htmlContent) {
      // Strip HTML tags and get plain text
      const plaintext = htmlContent.replace(/<[^>]*>/g, '').trim();
      const decoded = this.decodeHtmlEntities(plaintext);

      if (decoded.length <= 300) {
        return decoded;
      }

      // Truncate at word boundary to avoid cutting mid-word
      const truncated = decoded.substring(0, 297);
      const lastSpace = truncated.lastIndexOf(' ');

      if (lastSpace > 250) { // Only use word boundary if it's not too far back
        return truncated.substring(0, lastSpace) + '...';
      }

      return truncated + '...';
    }

    return null;
  }

  /**
   * Convert Ghost post to local article format
   */
  static convertGhostPostToArticle(post) {
    // Extract tags
    const tags = post.tags ? post.tags.map(tag => tag.name) : [];

    // Convert published date to ISO format
    const publishedDate = post.published_at ? new Date(post.published_at) : new Date(post.created_at);

    // Create frontmatter
    const frontmatter = {
      title: post.title,
      date: publishedDate.toISOString(),
      tags: tags,
      slug: post.slug
    };

    // Add optional fields if they exist
    if (post.custom_excerpt) {
      frontmatter.excerpt = post.custom_excerpt;
    }

    if (post.feature_image) {
      frontmatter.feature_image = post.feature_image;
    }

    if (post.featured) {
      frontmatter.featured = post.featured;
    }

    // Convert content - prefer lexical markdown if available, otherwise convert HTML
    let content = '';

    if (post.lexical) {
      try {
        const lexical = JSON.parse(post.lexical);
        // Look for markdown card in lexical
        const markdownCard = lexical.root?.children?.find(child => child.type === 'markdown');
        if (markdownCard && markdownCard.markdown) {
          content = markdownCard.markdown;
        } else {
          // Fallback to HTML conversion
          content = this.htmlToMarkdown(post.html);
        }
      } catch (error) {
        console.warn(`Warning: Could not parse lexical for "${post.title}", using HTML conversion`);
        content = this.htmlToMarkdown(post.html);
      }
    } else if (post.mobiledoc) {
      // Legacy mobiledoc support (for old posts)
      try {
        const mobiledoc = JSON.parse(post.mobiledoc);
        const markdownCard = mobiledoc.cards?.find(card => card[0] === 'markdown');
        if (markdownCard && markdownCard[1] && markdownCard[1].markdown) {
          content = markdownCard[1].markdown;
        } else {
          content = this.htmlToMarkdown(post.html);
        }
      } catch (error) {
        console.warn(`Warning: Could not parse mobiledoc for "${post.title}", using HTML conversion`);
        content = this.htmlToMarkdown(post.html);
      }
    } else {
      // Convert HTML to markdown
      content = this.htmlToMarkdown(post.html);
    }

    // Create the full article content
    const yamlFrontmatter = yaml.dump(frontmatter, {
      lineWidth: -1, // Prevent line wrapping
      quotingType: '"', // Use double quotes
      forceQuotes: false // Only quote when necessary
    });

    return `---\n${yamlFrontmatter}---\n\n${content}`;
  }

  /**
   * Parse date string
   */
  static parseDate(dateStr) {
    if (!dateStr) return null;
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  }

  /**
   * Format date for Ghost (for JSON export)
   */
  static formatGhostDate(date) {
    return date.toISOString().replace('T', ' ').replace(/\.\d{3}Z$/, '');
  }

  /**
   * Format date for Ghost API (needs full ISO format)
   */
  static formatGhostAPIDate(date) {
    return date.toISOString();
  }

  /**
   * Generate a unique ID for Ghost objects
   */
  static generateId() {
    return Math.floor(Math.random() * 1000000).toString();
  }

  /**
   * Generate UUID
   */
  static generateUUID() {
    return crypto.randomUUID();
  }

  /**
   * Create Ghost post object from local content
   */
  static createGhostPostData(frontMatter, markdownContent, htmlContent, options = {}) {
    const {
      status = 'draft',
      author = null,
      useHtml = false
    } = options;

    const postDate = this.parseDate(frontMatter.date) || new Date();
    const slug = frontMatter.slug || this.slugify(frontMatter.title);
    const publishedAt = status === 'published' ? this.formatGhostAPIDate(postDate) : null;

    const postData = {
      title: frontMatter.title,
      slug: slug,
      feature_image: frontMatter.feature_image || frontMatter.image || null,
      featured: frontMatter.featured || false,
      status: status,
      visibility: frontMatter.visibility || 'public',
      custom_excerpt: this.generateExcerpt(frontMatter, htmlContent),
      published_at: publishedAt
    };

    // Set author if provided
    if (author) {
      postData.authors = [{ id: author.id }];
    }

    // Set content format based on useHtml flag
    if (useHtml) {
      // Use HTML content directly (rendered from GitHub-flavored Markdown)
      postData.html = htmlContent;
      postData.mobiledoc = null;
      postData.lexical = null;
    } else {
      // Use MODERN LEXICAL: Convert markdown → HTML → lexical structure (NO MOBILEDOC)
      try {
        // First convert markdown to HTML using TryGhost's markdown renderer
        const ghostHtml = renderMarkdownToHtml(markdownContent);

        // Then convert HTML to lexical structure using TryGhost's converter
        const lexicalContent = htmlToLexical(ghostHtml);

        postData.lexical = JSON.stringify(lexicalContent);
        postData.mobiledoc = null; // NO MOBILEDOC EVER
        postData.html = null; // Let Ghost generate HTML from lexical
      } catch (error) {
        console.warn(`⚠️ Failed to convert markdown to lexical: ${error.message}`);
        console.warn(`   Falling back to HTML content`);
        postData.html = htmlContent;
        postData.mobiledoc = null;
        postData.lexical = null;
      }
    }

    return postData;
  }
}

module.exports = { ContentConverter };
