import { App, <PERSON><PERSON><PERSON>ie<PERSON>, <PERSON><PERSON>, Notice, Plugin, PluginSettingTab, Setting, SuggestModal, requestUrl, TFile } from "obsidian";
import * as path from "path";

// Import HTML to Markdown converter
const TurndownService = require('turndown');

// Provide a minimal jsdom shim for browser environment
if (typeof window !== 'undefined' && !(global as any).jsdom) {
	(global as any).jsdom = {
		JSDOM: class {
			constructor(html: string) {
				const parser = new DOMParser();
				this.window = {
					document: parser.parseFromString(html, 'text/html')
				};
			}
			window: any;
		}
	};
}

// Import TryGhost conversion packages
const { htmlToLexical } = require('@tryghost/kg-html-to-lexical');
const { render: renderMarkdownToHtml } = require('@tryghost/kg-markdown-html-renderer');

// Use the same API structure as our CLI ghost-api package

// Post selection modal
class PostSelectionModal extends SuggestModal<any> {
	private posts: any[];
	private onSelect: (post: any) => void;

	constructor(app: App, posts: any[], onSelect: (post: any) => void) {
		super(app);
		this.posts = posts;
		this.onSelect = onSelect;
		this.setPlaceholder("Type to search posts...");
	}

	getSuggestions(query: string): any[] {
		return this.posts.filter(post =>
			post.title.toLowerCase().includes(query.toLowerCase()) ||
			post.slug.toLowerCase().includes(query.toLowerCase())
		);
	}

	renderSuggestion(post: any, el: HTMLElement) {
		const container = el.createDiv({ cls: "ghost-post-suggestion" });

		const title = container.createDiv({ cls: "ghost-post-title" });
		title.setText(post.title);

		const meta = container.createDiv({ cls: "ghost-post-meta" });
		const status = post.status === 'published' ? '📄' : '📝';
		const publishedDate = post.published_at ?
			new Date(post.published_at).toLocaleDateString() :
			'Draft';
		const tags = post.tags?.map((t: any) => t.name).join(', ') || 'No tags';
		meta.setText(`${status} ${publishedDate} • ${tags}`);
	}

	onChooseSuggestion(post: any, evt: MouseEvent | KeyboardEvent) {
		this.onSelect(post);
	}
}

// Ghost API client for Obsidian using requestUrl
class ObsidianGhostAPI {
	private url: string;
	private key: string;
	private version: string;

	constructor(url: string, key: string, version = "v5.0") {
		this.url = url.replace(/\/$/, ''); // Remove trailing slash
		this.key = key;
		this.version = version;
	}

	private async createToken(): Promise<string> {
		const [id, secret] = this.key.split(':');
		const header = { alg: 'HS256', typ: 'JWT', kid: id };
		const payload = {
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 5 * 60,
			aud: '/admin/'
		};

		const token = this.base64urlEscape(btoa(JSON.stringify(header))) + '.' +
			this.base64urlEscape(btoa(JSON.stringify(payload)));

		// Create HMAC signature using Web Crypto API
		const encoder = new TextEncoder();
		const keyData = this.hexToArrayBuffer(secret);
		const tokenData = encoder.encode(token);

		const cryptoKey = await crypto.subtle.importKey(
			'raw',
			keyData,
			{ name: 'HMAC', hash: 'SHA-256' },
			false,
			['sign']
		);

		const signature = await crypto.subtle.sign('HMAC', cryptoKey, tokenData);
		const signatureBase64 = this.arrayBufferToBase64(signature);
		return token + '.' + this.base64urlEscape(signatureBase64);
	}

	private base64urlEscape(str: string): string {
		return str.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
	}

	private hexToArrayBuffer(hex: string): ArrayBuffer {
		const bytes = new Uint8Array(hex.length / 2);
		for (let i = 0; i < hex.length; i += 2) {
			bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
		}
		return bytes.buffer;
	}

	private arrayBufferToBase64(buffer: ArrayBuffer): string {
		const bytes = new Uint8Array(buffer);
		let binary = '';
		for (let i = 0; i < bytes.byteLength; i++) {
			binary += String.fromCharCode(bytes[i]);
		}
		return btoa(binary);
	}

	async request(endpoint: string, options: any = {}): Promise<any> {
		const token = await this.createToken();
		const url = `${this.url}/ghost/api/admin/${endpoint}`;

		const requestOptions = {
			url,
			method: options.method || 'GET',
			headers: {
				'Authorization': `Ghost ${token}`,
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				...options.headers
			},
			body: options.body ? JSON.stringify(options.body) : undefined
		};

		try {
			const response = await requestUrl(requestOptions);
			return response.json;
		} catch (error) {
			console.error('Ghost API request failed:', error);
			throw error;
		}
	}

	async getPosts(options: any = {}): Promise<any[]> {
		const params = new URLSearchParams({
			limit: options.limit?.toString() || '100',
			page: options.page?.toString() || '1',
			include: options.include || 'tags,authors',
			formats: options.formats || 'html,lexical'
		});

		const response = await this.request(`posts/?${params}`);
		return response.posts || [];
	}

	async getPostBySlug(slug: string): Promise<any> {
		const response = await this.request(`posts/slug/${slug}/?include=tags,authors&formats=html,lexical`);
		return response.posts?.[0] || null;
	}

	async createPost(postData: any): Promise<any> {
		const response = await this.request('posts/', {
			method: 'POST',
			body: { posts: [postData] }
		});
		return response.posts?.[0];
	}

	async updatePost(postData: any): Promise<any> {
		const response = await this.request(`posts/${postData.id}/`, {
			method: 'PUT',
			body: { posts: [postData] }
		});
		return response.posts?.[0];
	}
}

// Content conversion utilities
class ContentConverter {
	static htmlToMarkdown(html: string): string {
		if (!html) return '';

		// Use Turndown for proper HTML to Markdown conversion
		const turndownService = new TurndownService({
			headingStyle: 'atx',
			codeBlockStyle: 'fenced',
			fence: '```',
			emDelimiter: '*',
			strongDelimiter: '**',
			linkStyle: 'inlined',
			linkReferenceStyle: 'full'
		});

		// Add custom rule for code blocks with language detection
		turndownService.addRule('codeBlock', {
			filter: function (node: any) {
				return node.nodeName === 'PRE' && node.firstChild && node.firstChild.nodeName === 'CODE';
			},
			replacement: function (content: string, node: any) {
				const codeElement = node.firstChild;
				const className = codeElement.className || '';
				const languageMatch = className.match(/language-(\w+)/);
				const language = languageMatch ? languageMatch[1] : '';

				// Clean up the content by removing extra whitespace
				const cleanContent = content.trim();

				return '\n\n```' + language + '\n' + cleanContent + '\n```\n\n';
			}
		});

		// Add custom rule for Ghost callout cards
		turndownService.addRule('ghostCallouts', {
			filter: function (node: any) {
				// Check for Ghost's specific callout card structure
				if (node.nodeName === 'DIV') {
					const className = node.className || '';
					// Look for Ghost's kg-callout-card class
					if (className.includes('kg-callout-card')) {
						return true;
					}
				}
				return false;
			},
			replacement: function (content: string, node: any) {
				// Extract emoji and text from Ghost callout structure
				const emojiElement = node.querySelector('.kg-callout-emoji');
				const textElement = node.querySelector('.kg-callout-text');

				let emoji = '';
				let text = '';

				if (emojiElement) {
					emoji = emojiElement.textContent?.trim() || '';
				}

				if (textElement) {
					text = textElement.textContent?.trim() || '';
				}

				// If we couldn't extract specific elements, use the full content
				if (!text && content) {
					text = content.trim();
				}

				// Combine emoji and text - emoji is just decorative content, not semantic
				const finalContent = emoji && text ? `${emoji} ${text}` : (text || emoji);

				// Always use 'note' type for Ghost callouts since they don't have semantic types
				// The emoji is preserved as part of the content
				const cleanContent = finalContent.replace(/\n/g, '\n> ');
				return `\n\n> [!note]\n> ${cleanContent}\n\n`;
			}
		});

		// Add fallback rule for other callout patterns
		turndownService.addRule('genericCallouts', {
			filter: function (node: any) {
				// Check for other callout patterns
				if (node.nodeName === 'BLOCKQUOTE') {
					const className = node.className || '';
					const firstChild = node.firstChild;

					// Check for class-based callouts (e.g., class="callout-note")
					if (className.includes('callout') || className.includes('admonition') || className.includes('alert')) {
						return true;
					}

					// Check for content-based callouts (e.g., starts with "Note:", "Warning:", etc.)
					if (firstChild && firstChild.textContent) {
						const text = firstChild.textContent.trim().toLowerCase();
						if (text.startsWith('note:') || text.startsWith('warning:') || text.startsWith('info:') ||
							text.startsWith('tip:') || text.startsWith('caution:') || text.startsWith('important:')) {
							return true;
						}
					}
				}

				// Check for div-based callouts (but not Ghost's kg-callout-card which is handled above)
				if (node.nodeName === 'DIV') {
					const className = node.className || '';
					if ((className.includes('callout') || className.includes('admonition') ||
						className.includes('alert') || className.includes('note') ||
						className.includes('warning') || className.includes('info')) &&
						!className.includes('kg-callout-card')) {
						return true;
					}
				}

				return false;
			},
			replacement: function (content: string, node: any) {
				const className = node.className || '';
				let calloutType = 'note'; // default

				// Extract callout type from class name
				const classMatch = className.match(/(?:callout|admonition|alert)-(\w+)/);
				if (classMatch) {
					calloutType = classMatch[1].toLowerCase();
				} else if (className.includes('warning')) {
					calloutType = 'warning';
				} else if (className.includes('info')) {
					calloutType = 'info';
				} else if (className.includes('tip')) {
					calloutType = 'tip';
				} else if (className.includes('caution')) {
					calloutType = 'caution';
				} else if (className.includes('important')) {
					calloutType = 'important';
				}

				// Extract callout type from content if not found in class
				const firstChild = node.firstChild;
				if (firstChild && firstChild.textContent && !classMatch) {
					const text = firstChild.textContent.trim().toLowerCase();
					if (text.startsWith('note:')) {
						calloutType = 'note';
						content = content.replace(/^note:\s*/i, '');
					} else if (text.startsWith('warning:')) {
						calloutType = 'warning';
						content = content.replace(/^warning:\s*/i, '');
					} else if (text.startsWith('info:')) {
						calloutType = 'info';
						content = content.replace(/^info:\s*/i, '');
					} else if (text.startsWith('tip:')) {
						calloutType = 'tip';
						content = content.replace(/^tip:\s*/i, '');
					} else if (text.startsWith('caution:')) {
						calloutType = 'caution';
						content = content.replace(/^caution:\s*/i, '');
					} else if (text.startsWith('important:')) {
						calloutType = 'important';
						content = content.replace(/^important:\s*/i, '');
					}
				}

				// Clean up content and format as Obsidian callout
				const cleanContent = content.trim().replace(/\n/g, '\n> ');
				return `\n\n> [!${calloutType}]\n> ${cleanContent}\n\n`;
			}
		});

		return turndownService.turndown(html);
	}

	static createFilename(title: string): string {
		return title
			.toLowerCase()
			.replace(/[^\w\s-]/g, '')
			.replace(/\s+/g, '-')
			.replace(/-+/g, '-')
			.trim();
	}

	static slugify(text: string): string {
		return text
			.toLowerCase()
			.replace(/[^\w\s-]/g, '')
			.replace(/\s+/g, '-')
			.replace(/-+/g, '-')
			.trim();
	}

	static convertGhostPostToArticle(post: any): string {
		const tags = post.tags ? post.tags.map((tag: any) => tag.name) : [];
		const createdDate = post.created_at ? new Date(post.created_at) : new Date();
		const publishedDate = post.published_at ? new Date(post.published_at) : null;

		// Create frontmatter in the specified order
		const frontmatter: any = {};

		// 1. Title
		frontmatter["Title"] = post.title;

		// 2. Slug
		frontmatter["Slug"] = post.slug;

		// 3. Status
		frontmatter["Status"] = post.status || 'draft';

		// 4. Published At (only if published)
		if (publishedDate) {
			frontmatter["Published At"] = publishedDate.toISOString();
		}

		// 5. Created At
		frontmatter["Created At"] = createdDate.toISOString();

		// 6. Tags
		frontmatter["Tags"] = tags;

		// 7. Featured Image (only if exists)
		if (post.feature_image) {
			frontmatter["Featured Image"] = post.feature_image;
		}

		// 8. Featured (only if true)
		if (post.featured) {
			frontmatter["Featured"] = post.featured;
		}

		// Additional properties (not in main order)
		if (post.custom_excerpt) {
			frontmatter["Excerpt"] = post.custom_excerpt;
		}

		if (post.visibility && post.visibility !== 'public') {
			frontmatter["Visibility"] = post.visibility;
		}

		let content = '';

		// MODERN GHOST → LOCAL CONVERSION: Extract markdown from lexical OR convert HTML
		if (post.lexical) {
			try {
				console.log(`✅ LEXICAL PROCESSING: Extracting content for "${post.title}"`);
				const lexicalDoc = JSON.parse(post.lexical);

				// Look for markdown cards in lexical document
				const markdownCard = this.extractMarkdownFromLexical(lexicalDoc);
				if (markdownCard) {
					console.log(`✅ MARKDOWN EXTRACTED: Found markdown card in lexical`);
					content = markdownCard;
				} else {
					console.log(`⚠️ NO MARKDOWN CARD: Converting HTML to markdown`);
					content = this.htmlToMarkdown(post.html);
				}
			} catch (error) {
				console.warn(`⚠️ LEXICAL ERROR: Failed to process lexical for "${post.title}", using HTML`);
				content = this.htmlToMarkdown(post.html);
			}
		} else if (post.html) {
			// Direct HTML → Markdown conversion
			console.log(`✅ HTML → Markdown conversion for "${post.title}"`);
			content = this.htmlToMarkdown(post.html);
		} else {
			console.error(`❌ NO CONTENT: Post "${post.title}" has no lexical or HTML content`);
			content = '';
		}

		const yamlFrontmatter = this.objectToYaml(frontmatter);
		return `---\n${yamlFrontmatter}---\n\n${content}`;
	}

	static objectToYaml(obj: any): string {
		let yaml = '';
		for (const [key, value] of Object.entries(obj)) {
			if (Array.isArray(value)) {
				yaml += `${key}:\n`;
				for (const item of value) {
					yaml += `  - ${item}\n`;
				}
			} else if (typeof value === 'string') {
				yaml += `${key}: "${value}"\n`;
			} else {
				yaml += `${key}: ${value}\n`;
			}
		}
		return yaml;
	}

	static normalizeFrontMatter(frontMatter: any): any {
		// Map title-cased properties to lowercase equivalents for Ghost API
		const propertyMap: { [key: string]: string } = {
			"Title": "title",
			"Slug": "slug",
			"Status": "status",
			"Created At": "created_at",
			"Published At": "published_at",
			"Date": "published_at", // Map legacy "Date" to "Published At"
			"Tags": "tags",
			"Excerpt": "excerpt",
			"Featured Image": "feature_image",
			"Featured": "featured",
			"Visibility": "visibility"
		};

		const normalized: any = {};

		// Copy all properties, mapping title-cased ones to lowercase
		for (const [key, value] of Object.entries(frontMatter)) {
			const mappedKey = propertyMap[key] || key.toLowerCase();

			// Handle legacy "date" property (lowercase) - map to published_at
			if (key.toLowerCase() === 'date') {
				normalized['published_at'] = value;
			} else {
				normalized[mappedKey] = value;
			}
		}

		return normalized;
	}

	static createGhostPostData(frontMatter: any, markdownContent: string, options: any = {}): any {
		const { status = 'draft', useHtml = false } = options;

		// Map title-cased properties to lowercase for backward compatibility
		const normalizedFrontMatter = this.normalizeFrontMatter(frontMatter);

		const postDate = this.parseDate(normalizedFrontMatter.date) || new Date();
		const slug = normalizedFrontMatter.slug || this.slugify(normalizedFrontMatter.title);
		const publishedAt = status === 'published' ? postDate.toISOString() : null;

		const postData: any = {
			title: normalizedFrontMatter.title,
			slug: slug,
			feature_image: normalizedFrontMatter.feature_image || normalizedFrontMatter.image || null,
			featured: normalizedFrontMatter.featured || false,
			status: normalizedFrontMatter.status || status,
			visibility: normalizedFrontMatter.visibility || 'public',
			custom_excerpt: this.generateExcerpt(normalizedFrontMatter, markdownContent),
			published_at: publishedAt
		};

		// MODERN LEXICAL FORMAT (not deprecated mobiledoc)
		if (useHtml) {
			// Use HTML content directly
			postData.html = this.markdownToHtml(markdownContent);
			postData.mobiledoc = null;
			postData.lexical = null;
		} else {
			// Use MODERN LEXICAL: Convert markdown → HTML → lexical structure (NO MOBILEDOC)
			try {
				// First convert markdown to HTML using TryGhost's markdown renderer
				const ghostHtml = renderMarkdownToHtml(markdownContent);

				// Then convert HTML to lexical structure using TryGhost's converter
				const lexicalContent = htmlToLexical(ghostHtml);

				postData.lexical = JSON.stringify(lexicalContent);
				postData.mobiledoc = null; // NO MOBILEDOC EVER
				postData.html = null; // Let Ghost generate HTML from lexical
			} catch (error) {
				console.warn(`⚠️ Failed to convert markdown to lexical: ${error.message}`);
				console.warn(`   Falling back to HTML content`);
				postData.html = this.markdownToHtml(markdownContent);
				postData.mobiledoc = null;
				postData.lexical = null;
			}
		}

		return postData;
	}

	static parseDate(dateStr: string): Date | null {
		if (!dateStr) return null;
		const date = new Date(dateStr);
		return isNaN(date.getTime()) ? null : date;
	}

	static generateExcerpt(frontMatter: any, content: string): string | null {
		// First try frontmatter excerpt
		if (frontMatter.excerpt) {
			return frontMatter.excerpt.length > 300
				? frontMatter.excerpt.substring(0, 297) + '...'
				: frontMatter.excerpt;
		}

		// Extract from content if no frontmatter excerpt
		if (content) {
			// Get plain text from markdown
			const plaintext = content.replace(/[#*`_\[\]()]/g, '').trim();

			if (plaintext.length <= 300) {
				return plaintext;
			}

			// Truncate at word boundary
			const truncated = plaintext.substring(0, 297);
			const lastSpace = truncated.lastIndexOf(' ');

			if (lastSpace > 250) {
				return truncated.substring(0, lastSpace) + '...';
			}

			return truncated + '...';
		}

		return null;
	}

	static extractMarkdownFromLexical(lexicalDoc: any): string | null {
		// Extract markdown content from lexical document structure
		try {
			if (lexicalDoc?.root?.children) {
				for (const child of lexicalDoc.root.children) {
					// Look for markdown cards
					if (child.type === 'markdown' && child.markdown) {
						return child.markdown;
					}
				}
			}
			return null;
		} catch (error) {
			console.warn('Failed to extract markdown from lexical:', error);
			return null;
		}
	}

	static markdownToHtml(markdown: string): string {
		// EMERGENCY FIX: Use simple, safe conversion that preserves content
		// Convert line breaks to HTML line breaks
		let html = markdown.replace(/\n/g, '<br>\n');

		// Handle basic formatting only - keep it simple to avoid content loss
		html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
		html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
		html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

		// Handle headers (simple approach)
		html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');
		html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
		html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');

		// Handle Obsidian callouts (preserve them)
		html = html.replace(/^> \[!(\w+)\]\s*<br>\n((?:> .*<br>\n?)*)/gm, (_match, type, content) => {
			const cleanContent = content.replace(/^> /gm, '').replace(/<br>\n/g, ' ').trim();
			return `<div class="callout callout-${type.toLowerCase()}"><p>${cleanContent}</p></div>\n`;
		});

		return html;
	}

	static parseArticle(content: string): { frontMatter: any, markdownContent: string } {
		const frontMatterMatch = content.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
		if (!frontMatterMatch) {
			throw new Error('No front matter found in article');
		}

		const frontMatter = this.parseYaml(frontMatterMatch[1]);
		const markdownContent = frontMatterMatch[2].trim();

		return { frontMatter, markdownContent };
	}

	static parseYaml(yamlString: string): any {
		const result: any = {};
		const lines = yamlString.split('\n');

		for (const line of lines) {
			const trimmed = line.trim();
			if (!trimmed || trimmed.startsWith('#')) continue;

			if (trimmed.includes(':')) {
				const [key, ...valueParts] = trimmed.split(':');
				const value = valueParts.join(':').trim();

				if (value.startsWith('"') && value.endsWith('"')) {
					result[key.trim()] = value.slice(1, -1);
				} else if (value.startsWith('[') && value.endsWith(']')) {
					result[key.trim()] = value.slice(1, -1).split(',').map(s => s.trim());
				} else if (value === 'true') {
					result[key.trim()] = true;
				} else if (value === 'false') {
					result[key.trim()] = false;
				} else if (!isNaN(Number(value))) {
					result[key.trim()] = Number(value);
				} else {
					result[key.trim()] = value;
				}
			}
		}

		return result;
	}
}

interface GhostSyncSettings {
	ghostUrl: string;
	ghostAdminApiKey: string;
	articlesDir: string;
	verbose: boolean;
}

const DEFAULT_SETTINGS: GhostSyncSettings = {
	ghostUrl: "https://your-site.ghost.io",
	ghostAdminApiKey: "",
	articlesDir: "articles",
	verbose: false
};

export default class GhostSyncPlugin extends Plugin {
	settings: GhostSyncSettings;

	async onload() {
		await this.loadSettings();

		// Add ribbon icon for syncing current post to Ghost
		this.addRibbonIcon("upload", "Sync current post to Ghost", () => {
			this.syncCurrentPostToGhost();
		});

		// Add command for syncing current post to Ghost
		this.addCommand({
			id: "sync-current-to-ghost",
			name: "Sync current post to Ghost",
			editorCallback: () => {
				this.syncCurrentPostToGhost();
			}
		});

		// Add command for syncing current file
		this.addCommand({
			id: "sync-current-file",
			name: "Sync current file",
			editorCallback: () => {
				this.syncCurrentPostToGhost();
			}
		});

		// Add command for browsing and syncing posts from Ghost
		this.addCommand({
			id: "browse-ghost-posts",
			name: "Browse and sync posts from Ghost",
			callback: () => {
				this.browseGhostPosts();
			}
		});

		// Add command for syncing specific post from Ghost by title
		this.addCommand({
			id: "sync-from-ghost-by-title",
			name: "Sync post from Ghost by title",
			callback: () => {
				this.syncFromGhostByTitle();
			}
		});

		// Add command for syncing all posts from Ghost
		this.addCommand({
			id: "sync-all-from-ghost",
			name: "Sync all posts from Ghost to local",
			callback: () => {
				this.syncAllFromGhost();
			}
		});

		// Add command for creating new post
		this.addCommand({
			id: "create-new-post",
			name: "Ghost Sync: Create new post",
			callback: () => {
				this.createNewPost();
			}
		});

		// Add settings tab
		this.addSettingTab(new GhostSyncSettingTab(this.app, this));
	}

	async syncCurrentPostToGhost() {
		const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
		if (!activeView) {
			new Notice("No active markdown file");
			return;
		}

		const file = activeView.file;
		if (!file) {
			new Notice("No file is currently open");
			return;
		}

		// Check if the file is in the articles directory
		const articlesPath = path.normalize(this.settings.articlesDir);
		const filePath = path.normalize(file.path);

		if (!filePath.startsWith(articlesPath)) {
			new Notice(`File must be in the ${this.settings.articlesDir} directory to sync to Ghost`);
			return;
		}

		try {
			// Validate settings
			if (!this.settings.ghostAdminApiKey) {
				new Notice("Ghost Admin API key not configured. Please check plugin settings.");
				return;
			}

			// Read and parse the file content
			const content = await this.app.vault.read(file);
			const { frontMatter, markdownContent } = ContentConverter.parseArticle(content);

			// Normalize frontmatter to handle both title-cased and lowercase properties
			const normalizedFrontMatter = ContentConverter.normalizeFrontMatter(frontMatter);

			if (!normalizedFrontMatter.title) {
				new Notice("Could not find title in frontmatter");
				return;
			}

			const title = normalizedFrontMatter.title;
			new Notice(`Syncing "${title}" to Ghost...`);

			// Create Ghost API client
			const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);

			// Check if post already exists
			const slug = normalizedFrontMatter.slug || ContentConverter.slugify(title);
			const existingPost = await ghostAPI.getPostBySlug(slug);

			// Create post data
			const postData = ContentConverter.createGhostPostData(frontMatter, markdownContent, {
				status: 'published'
			});

			let result;
			if (existingPost) {
				// Update existing post
				postData.id = existingPost.id;
				postData.updated_at = existingPost.updated_at;
				result = await ghostAPI.updatePost(postData);
				new Notice(`Successfully updated "${title}" in Ghost`);
			} else {
				// Create new post
				result = await ghostAPI.createPost(postData);
				new Notice(`Successfully created "${title}" in Ghost`);
			}

			if (this.settings.verbose) {
				console.log("Ghost sync result:", result);
			}

		} catch (error) {
			console.error("Error syncing to Ghost:", error);
			new Notice(`Error syncing to Ghost: ${error.message}`);
		}
	}

	async syncFromGhostByTitle() {
		// Prompt user for post title
		const title = await this.promptForTitle();
		if (!title) return;

		try {
			// Validate settings
			if (!this.settings.ghostAdminApiKey) {
				new Notice("Ghost Admin API key not configured. Please check plugin settings.");
				return;
			}

			new Notice(`Syncing "${title}" from Ghost...`);

			// Create Ghost API client
			const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);

			// Get all posts and find the one with matching title
			const posts = await ghostAPI.getPosts();
			const post = posts.find(p => p.title.toLowerCase() === title.toLowerCase());

			if (!post) {
				new Notice(`Post "${title}" not found in Ghost`);
				return;
			}

			// Convert post to article format
			const articleContent = ContentConverter.convertGhostPostToArticle(post);
			const filename = post.slug + '.md';
			const filePath = path.posix.join(this.settings.articlesDir, filename);

			// Ensure the directory exists
			const dir = path.dirname(filePath);
			if (dir !== '.' && dir !== this.settings.articlesDir) {
				await this.app.vault.createFolder(dir).catch(() => {
					// Folder might already exist, ignore error
				});
			}

			// Check if file already exists
			const existingFile = this.app.vault.getAbstractFileByPath(filePath);
			if (existingFile) {
				// Update existing file
				await this.app.vault.modify(existingFile as any, articleContent);
				new Notice(`Updated "${title}" in ${filePath}`);
			} else {
				// Create new file
				await this.app.vault.create(filePath, articleContent);
				new Notice(`Created "${title}" in ${filePath}`);
			}

			if (this.settings.verbose) {
				console.log("Ghost sync result:", { post: post.title, file: filePath });
			}

		} catch (error) {
			console.error("Error syncing from Ghost:", error);
			new Notice(`Error syncing from Ghost: ${error.message}`);
		}
	}

	async browseGhostPosts() {
		try {
			// Validate settings
			if (!this.settings.ghostAdminApiKey) {
				new Notice("Ghost Admin API key not configured. Please check plugin settings.");
				return;
			}

			new Notice("Fetching posts from Ghost...");

			// Create Ghost API client
			const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);

			// Get all posts
			const posts = await ghostAPI.getPosts();

			if (posts.length === 0) {
				new Notice("No posts found in Ghost");
				return;
			}

			// Show post selection modal
			const modal = new PostSelectionModal(this.app, posts, async (selectedPost) => {
				try {
					new Notice(`Syncing "${selectedPost.title}" from Ghost...`);

					// Convert post to article format
					const articleContent = ContentConverter.convertGhostPostToArticle(selectedPost);
					const filename = selectedPost.slug + '.md';
					const filePath = path.posix.join(this.settings.articlesDir, filename);

					// Ensure the directory exists
					const dir = path.dirname(filePath);
					if (dir !== '.' && dir !== this.settings.articlesDir) {
						await this.app.vault.createFolder(dir).catch(() => {
							// Folder might already exist, ignore error
						});
					}

					// Check if file already exists
					const existingFile = this.app.vault.getAbstractFileByPath(filePath);
					if (existingFile) {
						// Update existing file
						await this.app.vault.modify(existingFile as any, articleContent);
						new Notice(`Updated "${selectedPost.title}" in ${filePath}`);
					} else {
						// Create new file
						await this.app.vault.create(filePath, articleContent);
						new Notice(`Created "${selectedPost.title}" in ${filePath}`);
					}

					if (this.settings.verbose) {
						console.log("Ghost sync result:", { post: selectedPost.title, file: filePath });
					}
				} catch (error) {
					console.error("Error syncing selected post:", error);
					new Notice(`Error syncing "${selectedPost.title}": ${error.message}`);
				}
			});

			modal.open();

		} catch (error) {
			console.error("Error fetching posts from Ghost:", error);
			new Notice(`Error fetching posts from Ghost: ${error.message}`);
		}
	}

	async syncAllFromGhost() {
		try {
			// Validate settings
			if (!this.settings.ghostAdminApiKey) {
				new Notice("Ghost Admin API key not configured. Please check plugin settings.");
				return;
			}

			new Notice("Syncing all posts from Ghost...");

			// Create Ghost API client
			const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);

			// Get all posts
			const posts = await ghostAPI.getPosts();

			if (posts.length === 0) {
				new Notice("No posts found in Ghost");
				return;
			}

			let syncedCount = 0;
			const errors: string[] = [];

			// Sync each post
			for (const post of posts) {
				try {
					// Convert post to article format
					const articleContent = ContentConverter.convertGhostPostToArticle(post);
					const filename = post.slug + '.md';
					const filePath = path.posix.join(this.settings.articlesDir, filename);

					// Ensure the directory exists
					const dir = path.dirname(filePath);
					if (dir !== '.' && dir !== this.settings.articlesDir) {
						await this.app.vault.createFolder(dir).catch(() => {
							// Folder might already exist, ignore error
						});
					}

					// Check if file already exists
					const existingFile = this.app.vault.getAbstractFileByPath(filePath);
					if (existingFile) {
						// Update existing file
						await this.app.vault.modify(existingFile as any, articleContent);
					} else {
						// Create new file
						await this.app.vault.create(filePath, articleContent);
					}

					syncedCount++;

					if (this.settings.verbose) {
						console.log(`Synced: ${post.title} -> ${filePath}`);
					}
				} catch (error) {
					const errorMsg = `Failed to sync "${post.title}": ${error.message}`;
					errors.push(errorMsg);
					console.error(errorMsg);
				}
			}

			if (syncedCount > 0) {
				new Notice(`Successfully synced ${syncedCount} posts from Ghost`);
			}

			if (errors.length > 0) {
				new Notice(`${errors.length} posts failed to sync. Check console for details.`);
			}

			if (this.settings.verbose) {
				console.log(`Sync complete: ${syncedCount} synced, ${errors.length} errors`);
			}

		} catch (error) {
			console.error("Error syncing from Ghost:", error);
			new Notice(`Error syncing from Ghost: ${error.message}`);
		}
	}

	async promptForTitle(): Promise<string | null> {
		return new Promise((resolve) => {
			const modal = new TitleInputModal(this.app, (title) => {
				resolve(title);
			});
			modal.open();
		});
	}

	getVaultPath(): string {
		// Try to get the vault path from the adapter
		const adapter = this.app.vault.adapter as any;
		if (adapter.basePath) {
			return adapter.basePath;
		}
		// Fallback - try to use the vault name or current directory
		return ".";
	}

	getAbsolutePath(relativePath: string): string {
		const vaultPath = this.getVaultPath();
		return path.resolve(vaultPath, relativePath);
	}

	async createNewPost() {
		try {
			// Prompt for post title
			const title = await this.promptForTitle();
			if (!title) return;

			// Generate slug from title
			const slug = ContentConverter.slugify(title);
			const filename = `${slug}.md`;
			const filePath = path.posix.join(this.settings.articlesDir, filename);

			// Check if file already exists
			const existingFile = this.app.vault.getAbstractFileByPath(filePath);
			if (existingFile) {
				new Notice(`File "${filename}" already exists`);
				return;
			}

			// Create current datetime in ISO format
			const now = new Date();
			const createdAt = now.toISOString();

			// Create frontmatter with creation datetime using title-cased properties in specified order
			const frontmatter: any = {};

			// 1. Title
			frontmatter["Title"] = title;

			// 2. Slug
			frontmatter["Slug"] = slug;

			// 3. Status
			frontmatter["Status"] = 'draft';

			// 4. Published At (not set for new drafts)
			// Will be added when post is published

			// 5. Created At
			frontmatter["Created At"] = createdAt;

			// 6. Tags
			frontmatter["Tags"] = [] as string[];

			// 7. Featured Image (not set for new posts)
			// Can be added later

			// 8. Featured (not set for new posts)
			// Can be added later

			// Create article content with frontmatter and empty content
			const yamlFrontmatter = ContentConverter.objectToYaml(frontmatter);
			const articleContent = `---\n${yamlFrontmatter}---\n\n# ${title}\n\nWrite your content here...\n`;

			// Ensure articles directory exists
			const articlesDir = this.settings.articlesDir;
			if (!await this.app.vault.adapter.exists(articlesDir)) {
				await this.app.vault.createFolder(articlesDir);
			}

			// Create the file
			const file = await this.app.vault.create(filePath, articleContent);

			// Open the new file
			const leaf = this.app.workspace.getLeaf(false);
			await leaf.openFile(file);

			new Notice(`Created new post: "${title}"`);

		} catch (error) {
			console.error("Error creating new post:", error);
			new Notice(`Error creating new post: ${error.message}`);
		}
	}



	async loadSettings() {
		this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
	}

	async saveSettings() {
		await this.saveData(this.settings);
	}
}

// Simple modal for title input
class TitleInputModal extends Modal {
	result: string;
	onSubmit: (result: string) => void;

	constructor(app: App, onSubmit: (result: string) => void) {
		super(app);
		this.onSubmit = onSubmit;
	}

	onOpen() {
		const { contentEl } = this;

		contentEl.createEl("h2", { text: "Enter post title" });

		const inputEl = contentEl.createEl("input", {
			type: "text",
			placeholder: "Post title..."
		});
		inputEl.focus();

		const buttonEl = contentEl.createEl("button", {
			text: "Sync"
		});

		const handleSubmit = () => {
			const title = inputEl.value.trim();
			if (title) {
				this.close();
				this.onSubmit(title);
			}
		};

		buttonEl.onclick = handleSubmit;
		inputEl.onkeydown = (e) => {
			if (e.key === "Enter") {
				handleSubmit();
			}
		};
	}

	onClose() {
		const { contentEl } = this;
		contentEl.empty();
	}
}

// Settings tab
class GhostSyncSettingTab extends PluginSettingTab {
	plugin: GhostSyncPlugin;

	constructor(app: App, plugin: GhostSyncPlugin) {
		super(app, plugin);
		this.plugin = plugin;
	}

	display(): void {
		const { containerEl } = this;

		containerEl.empty();

		containerEl.createEl("h2", { text: "Ghost Sync Settings" });

		new Setting(containerEl)
			.setName("Ghost site URL")
			.setDesc("Your Ghost site URL (e.g., https://your-site.ghost.io)")
			.addText(text => text
				.setPlaceholder("https://your-site.ghost.io")
				.setValue(this.plugin.settings.ghostUrl)
				.onChange(async (value) => {
					this.plugin.settings.ghostUrl = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName("Ghost Admin API Key")
			.setDesc("Your Ghost Admin API key (format: id:secret)")
			.addText(text => text
				.setPlaceholder("Enter your Ghost Admin API key")
				.setValue(this.plugin.settings.ghostAdminApiKey)
				.onChange(async (value) => {
					this.plugin.settings.ghostAdminApiKey = value;
					await this.plugin.saveSettings();
				}));



		new Setting(containerEl)
			.setName("Articles directory")
			.setDesc("Directory where your articles are stored")
			.addText(text => text
				.setPlaceholder("articles")
				.setValue(this.plugin.settings.articlesDir)
				.onChange(async (value) => {
					this.plugin.settings.articlesDir = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName("Verbose output")
			.setDesc("Show detailed output in console")
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.verbose)
				.onChange(async (value) => {
					this.plugin.settings.verbose = value;
					await this.plugin.saveSettings();
				}));
	}
}
