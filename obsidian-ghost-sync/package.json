{"name": "obsidian-ghost-sync", "version": "1.0.0", "description": "Sync posts between Obsidian and Ghost.io", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": [], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@types/node": "^16.11.6", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "builtin-modules": "3.3.0", "esbuild": "0.17.3", "obsidian": "latest", "tslib": "2.4.0", "typescript": "4.7.4"}, "dependencies": {"@tryghost/kg-html-to-lexical": "^1.2.24", "@tryghost/kg-lexical-html-renderer": "^1.3.24", "@tryghost/kg-markdown-html-renderer": "^7.1.2", "turndown": "^7.2.0"}}