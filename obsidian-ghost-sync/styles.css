/* Ghost Sync Plugin Styles */

.ghost-sync-modal {
    padding: 20px;
}

.ghost-sync-modal h2 {
    margin-bottom: 15px;
}

.ghost-sync-modal input {
    width: 100%;
    margin-bottom: 15px;
    padding: 8px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
}

.ghost-sync-modal button {
    padding: 8px 16px;
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.ghost-sync-modal button:hover {
    background: var(--interactive-accent-hover);
}

/* Post Selection Modal Styles */
.ghost-post-suggestion {
    padding: 8px 12px;
    border-bottom: 1px solid var(--background-modifier-border);
}

.ghost-post-suggestion:last-child {
    border-bottom: none;
}

.ghost-post-title {
    font-weight: 500;
    color: var(--text-normal);
    margin-bottom: 4px;
}

.ghost-post-meta {
    font-size: 0.85em;
    color: var(--text-muted);
    opacity: 0.8;
}

.ghost-post-suggestion:hover .ghost-post-title {
    color: var(--text-accent);
}

.ghost-post-suggestion:hover .ghost-post-meta {
    opacity: 1;
}
