# Ghost Sync Plugin for Obsidian

A modern Obsidian plugin that uses reusable Ghost.io API modules to sync posts between Obsidian and Ghost.io.

## Features

- **Sync to Ghost**: Send your current Obsidian note to Ghost.io as a published post
- **Sync from Ghost**: Pull a specific post from <PERSON>.io to your local articles directory
- **Sync all posts**: Download all posts from Ghost.io to your local articles directory
- **Direct API integration**: Uses reusable Ghost API modules directly (no shell commands)
- **Shared codebase**: Same modules used by CLI scripts and Obsidian plugin

## Prerequisites

- A Ghost.io site with Admin API access
- Your Ghost Admin API key (available in Ghost Admin → Integrations → Custom integrations)
- The reusable Ghost API modules must be available in `../lib/` directory

## Installation

1. Copy the plugin files to your Obsidian vault's `.obsidian/plugins/ghost-sync/` directory
2. Enable the plugin in Obsidian's Community Plugins settings
3. Configure the plugin settings:
   - **Ghost site URL**: Your Ghost site URL (e.g., `https://your-site.ghost.io`)
   - **Ghost Admin API Key**: Your Ghost Admin API key (format: `id:secret`)
   - **Articles directory**: Directory where your articles are stored (default: `articles`)
   - **Verbose output**: Enable detailed console logging

## Getting Your Ghost Admin API Key

1. Go to your Ghost Admin panel
2. Navigate to **Settings → Integrations**
3. Click **Add custom integration**
4. Give it a name (e.g., "Obsidian Sync")
5. Copy the **Admin API Key** (it should be in format: `id:secret`)
6. Paste it into the plugin settings

## Usage

### Syncing to Ghost

1. Open a markdown file in your articles directory
2. Make sure it has proper frontmatter with a `title` field
3. Use one of these methods:
   - Click the sync icon in the ribbon
   - Use Command Palette: "Sync current post to Ghost"
   - Use the keyboard shortcut (if configured)

### Syncing from Ghost

1. Use Command Palette: "Sync post from Ghost to local"
2. Enter the exact title of the post you want to sync
3. The post will be downloaded to your articles directory

### Syncing all posts

1. Use Command Palette: "Sync all posts from Ghost to local"
2. All posts will be downloaded to your articles directory

## File Format

The plugin expects and creates files in the following format:

```markdown
---
title: "Your Post Title"
date: "2025-03-14T11:05:21.000Z"
tags:
  - tag1
  - tag2
slug: your-post-slug
---

Your post content here...
```

## Commands

- **Sync current post to Ghost**: Publishes the current file to Ghost.io
- **Sync post from Ghost to local**: Downloads a specific post by title
- **Sync all posts from Ghost to local**: Downloads all posts from Ghost.io

## Settings

- **Ghost site URL**: Your Ghost site URL
- **Ghost Admin API Key**: Your Ghost Admin API key (format: id:secret)
- **Articles directory**: Directory where articles are stored
- **Verbose output**: Show detailed output in the console

## Troubleshooting

- **"Ghost Admin API key not configured"**: Make sure you've entered your API key in plugin settings
- **"Invalid Admin API key format"**: Ensure your API key is in the correct format (id:secret)
- **Connection errors**: Verify your Ghost site URL is correct and accessible
- Check the console (Ctrl+Shift+I) for detailed error messages
- Verify that the reusable Ghost API modules are available in `../lib/` directory

## License

MIT License
