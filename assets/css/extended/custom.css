body.list .main {
  max-width: 1200px !important;
  padding: var(--gap);
}

.nav {
  max-width: 1000px !important;
  margin-inline-start: auto;
  margin-inline-end: auto;
}

/* Move theme toggle to the right */
.logo-switches {
  margin-left: auto;
  order: 2;
}

#menu {
  order: 1;
  margin-right: 0;
}

.logo {
  margin-right: 0;
}

button#theme-toggle {
  padding: 0;
  margin-left: var(--gap);
  border: none;
  background: none;
  cursor: pointer;
  vertical-align: middle;
}

.header {
  padding: var(--gap);
}

#top .main {
  max-width: 1000px;
  padding: var(--gap);
}

.home-layout {
  display: grid;
  grid-template-columns: minmax(300px, 1fr) minmax(600px, 3fr);
  gap: 4rem;
  margin: 0 auto;
}

@media screen and (max-width: 1200px) {
  .home-layout {
    grid-template-columns: minmax(280px, 1fr) minmax(500px, 2.5fr);
    gap: 2rem;
    padding: 0 1.5rem;
  }
}

@media screen and (max-width: 900px) {
  .home-layout {
    grid-template-columns: 1fr;
    max-width: 700px;
    gap: 2rem;
    padding: 0 1rem;
  }

  .profile-section {
    position: static;
  }
}

.profile-section {
  position: sticky;
  top: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem;
  background: var(--entry);
  border-radius: var(--radius);
  height: fit-content;
}

.profile-image {
  border-radius: 50%;
  margin-bottom: 1.5rem;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.profile-title {
  margin: 0.5rem 0;
  font-size: 1.8rem;
}

.profile-subtitle {
  color: var(--secondary);
  margin-bottom: 1.5rem;
}

.social-icons {
  display: flex;
  gap: 1rem;
  margin: 1rem 0;
  justify-content: center;
}

.social-icons a {
  color: var(--secondary);
  transition: color 0.2s;
}

.social-icons a:hover {
  color: var(--primary);
}

.profile-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
  margin-top: 1rem;
}

.profile-buttons .button {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: var(--tertiary);
  color: var(--primary);
  border-radius: var(--radius);
  text-decoration: none;
  transition: all 0.2s;
}

.profile-buttons .button:hover {
  background: var(--primary);
  color: var(--theme);
}

.latest-posts {
  padding: 2rem 0;
}

.latest-posts h2 {
  margin-bottom: 2rem;
  font-size: 2rem;
}

.posts-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.entry-header {
  margin-bottom: 1rem;
}

.entry-header h3 {
  margin: 0;
  font-size: 1.8rem;
  line-height: 1.3;
}

.entry-date {
  display: block;
  margin-top: 0.5rem;
  color: var(--secondary);
  font-size: 0.9rem;
}

.entry-content {
  margin: 1rem 0 1.5rem 0;
  color: var(--secondary);
  font-size: 1rem;
  line-height: 1.6;
}

.entry-footer {
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid var(--border);
}

.entry-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.entry-tags a {
  padding: 0.2rem 0.8rem;
  background: var(--tertiary);
  border-radius: var(--radius);
  color: var(--secondary);
  text-decoration: none;
  font-size: 0.85rem;
  transition: all 0.2s;
}

.entry-tags a:hover {
  background: var(--primary);
  color: var(--theme);
}

.post-entry {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 2rem;
  background: var(--entry);
  border-radius: var(--radius);
  transition: transform 0.2s;
  min-height: 280px;
}

.post-entry:hover {
  transform: translateY(-2px);
}

.entry-link {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* Sponsors Page Styles */
.sponsors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

/* Current sponsors: larger cards with horizontal layout */
.current-sponsors.sponsors-grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.sponsor-card {
  background: var(--entry);
  border: 1px solid rgba(128, 128, 128, 0.03);
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.sponsor-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.sponsor-link {
  display: flex;
  text-decoration: none;
  color: inherit;
  height: 100%;
  padding: 1rem;
  align-items: center;
  gap: 1rem;
}

/* Current sponsors: horizontal layout with larger avatars */
.current-sponsors .sponsor-link {
  flex-direction: row;
}

/* Past sponsors: vertical layout with smaller avatars */
.past-sponsors .sponsor-link {
  flex-direction: column;
  text-align: center;
  padding: 0.75rem;
}

.sponsor-avatar {
  border-radius: 50%;
  object-fit: cover;
  display: block;
  filter: brightness(1);
  transition: filter 0.2s ease;
  margin: 0;
  padding: 0;
  flex-shrink: 0;
}

/* Current sponsors: larger avatars */
.current-sponsors .sponsor-avatar {
  width: 80px;
  height: 80px;
}

/* Past sponsors: smaller avatars */
.past-sponsors .sponsor-avatar {
  width: 60px;
  height: 60px;
  margin-bottom: 0.5rem;
}

.sponsor-card:hover .sponsor-avatar {
  filter: brightness(1.1);
}

.sponsor-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.sponsor-info h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--primary);
  line-height: 1.3;
  font-weight: 600;
}

.sponsor-amount {
  font-size: 0.9rem;
  color: var(--secondary);
  display: block;
}

.current-sponsors .sponsor-card {
  border-color: rgba(128, 128, 128, 0.05);
}

.past-sponsors .sponsor-card {
  opacity: 0.75;
}

.past-sponsors .sponsor-card:hover {
  opacity: 0.95;
}

/* Dark mode adjustments */
[theme="dark"] .sponsor-card {
  border-color: rgba(255, 255, 255, 0.02);
}

[theme="dark"] .current-sponsors .sponsor-card {
  border-color: rgba(255, 255, 255, 0.03);
}

[theme="dark"] .sponsor-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

@media screen and (max-width: 1024px) {
  .sponsors-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  .current-sponsors.sponsors-grid {
    grid-template-columns: 1fr;
  }

  .current-sponsors .sponsor-link {
    flex-direction: row;
  }
}

@media screen and (max-width: 600px) {
  .sponsors-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .current-sponsors .sponsor-link {
    flex-direction: column;
    text-align: center;
  }

  .current-sponsors .sponsor-avatar {
    width: 60px;
    height: 60px;
    margin-bottom: 0.5rem;
  }
}
    gap: 1rem;
  }

  .sponsor-avatar {
    height: 120px;
  }

  .sponsor-info {
    padding: 0.5rem;
  }

  .sponsor-info h3 {
    font-size: 0.85rem;
  }
}

@media screen and (max-width: 480px) {
  .sponsors-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
}

/* Newsletter styles */
.newsletter-container {
  display: flex;
  justify-content: center;
  margin: 3rem auto;
  padding: 0 var(--gap);
}

.newsletter-container .formkit-form {
  max-width: 600px;
  width: 100%;
}

/* Post tags in header */
.post-header .post-tags {
  margin: 1rem 0 0 0;
  padding: 0;
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.post-header .post-tags li {
  margin: 0;
}

.post-header .post-tags a {
  display: inline-block;
  padding: 0.2rem 0.8rem;
  background: var(--tertiary);
  border-radius: var(--radius);
  color: var(--secondary);
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.2s;
}

.post-header .post-tags a:hover {
  background: var(--primary);
  color: var(--theme);
}

/* Share buttons styles */
.share-buttons {
  display: flex;
  flex-wrap: nowrap;
  gap: 0.5rem;
  margin: 2rem 0;
}

.share-btn {
  display: inline-flex;
  align-items: center;
  padding: 0.4rem 0.6rem;
  border-radius: var(--radius);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  color: var(--secondary);
}

.share-btn:hover {
  color: var(--primary);
  transform: translateY(-1px);
}

.share-btn svg {
  width: 18px;
  height: 18px;
}

.share-btn span {
  display: none;
}

/* Platform-specific colors on hover */
.share-btn.bluesky:hover {
  color: #0085ff;
}

.share-btn.mastodon:hover {
  color: #6364FF;
}

.share-btn.linkedin:hover {
  color: #0077b5;
}

.share-btn.reddit:hover {
  color: #ff4500;
}

.share-btn.hackernews:hover {
  color: #ff6600;
}

/* Gradient text effect for site logo */
.logo a {
  background: linear-gradient(45deg, #4a90e2, #9b51e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

.logo a:hover {
  opacity: 0.85;
}

/* Ensure the gradient works in dark mode too */
[theme="dark"] .logo a {
  background: linear-gradient(45deg, #60a5fa, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Newsletter form styles */
.embeddable-buttondown-form {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 1rem;
  width: 100%;
  margin: 2rem auto;
  padding: 1.5rem;
  background: var(--entry);
  border-radius: var(--radius);
  border: 1px solid var(--border);
  align-items: center;
}

.embeddable-buttondown-form label {
  color: var(--primary);
  font-size: 1rem;
  font-weight: 500;
  white-space: nowrap;
}

.embeddable-buttondown-form input[type="email"] {
  padding: 0.8rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  background: var(--theme);
  color: var(--primary);
  font-size: 1rem;
  width: 100%;
  min-width: 200px;
  transition: border-color 0.2s ease;
}

.embeddable-buttondown-form input[type="email"]:focus {
  outline: none;
  border-color: var(--primary);
}

.embeddable-buttondown-form input[type="submit"] {
  padding: 0.8rem 1.5rem;
  background: var(--primary);
  color: var(--theme);
  border: none;
  border-radius: var(--radius);
  font-size: 1rem;
  cursor: pointer;
  transition: opacity 0.2s ease;
  white-space: nowrap;
}

.embeddable-buttondown-form input[type="submit"]:hover {
  opacity: 0.9;
}

.embeddable-buttondown-form p {
  grid-column: 1 / -1;
  margin: 0.5rem 0 0 0;
  font-size: 0.8rem;
  color: var(--secondary);
  text-align: center;
}

.embeddable-buttondown-form p a {
  color: var(--primary);
  text-decoration: none;
  transition: opacity 0.2s ease;
}

.embeddable-buttondown-form p a:hover {
  opacity: 0.8;
}

/* Make the form responsive */
@media screen and (max-width: 768px) {
  .embeddable-buttondown-form {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    padding: 1rem;
  }

  .embeddable-buttondown-form label {
    text-align: center;
  }

  .embeddable-buttondown-form input[type="submit"] {
    width: 100%;
  }
}

/* Homepage Newsletter Section */
.home-newsletter {
  margin-top: 4rem;
  padding-top: 3rem;
  border-top: 1px solid var(--border);
}

.home-newsletter h2 {
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.home-newsletter p {
  color: var(--secondary);
  margin-bottom: 2rem;
}

@media screen and (max-width: 768px) {
  .home-newsletter {
    margin-top: 3rem;
    padding-top: 2rem;
  }

  .home-newsletter h2 {
    font-size: 1.6rem;
  }
}

/* Post Newsletter Section */
.post-newsletter {
  margin: 4rem 0 2rem;
  padding: 3rem;
  background: var(--entry);
  border-radius: var(--radius);
  border: 1px solid var(--border);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.post-newsletter .newsletter-content {
  text-align: center;
  margin-bottom: 2rem;
}

.post-newsletter h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #4a90e2, #9b51e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

[theme="dark"] .post-newsletter h2 {
  background: linear-gradient(45deg, #60a5fa, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.post-newsletter p {
  color: var(--secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.post-newsletter .embeddable-buttondown-form {
  max-width: 800px;
  margin: 0 auto;
}

@media screen and (max-width: 768px) {
  .post-newsletter {
    padding: 2rem 1.5rem;
    margin: 3rem 0 1.5rem;
  }

  .post-newsletter h2 {
    font-size: 1.8rem;
  }

  .post-newsletter p {
    font-size: 1rem;
  }
}
