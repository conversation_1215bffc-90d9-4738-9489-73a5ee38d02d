#!/usr/bin/env node

const { describe, it, beforeEach, afterEach } = require('mocha');
const { expect } = require('chai');
const sinon = require('sinon');
const { GhostAPI } = require('../lib/ghost-api');

describe('GhostAPI', () => {
  let ghostAPI;
  let mockConfig;
  let mockOptions;

  beforeEach(() => {
    // Use valid Ghost API key format for tests
    mockConfig = {
      admin_api_key: '123456789012345678901234:1234567890123456789012345678901234567890123456789012345678901234'
    };
    mockOptions = {
      verbose: false,
      dryRun: false
    };
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('constructor', () => {
    it('should initialize with config and options', () => {
      ghostAPI = new GhostAPI(mockConfig, mockOptions);
      expect(ghostAPI.config).to.deep.equal(mockConfig);
      expect(ghostAPI.options).to.deep.equal(mockOptions);
    });

    it('should use default options when not provided', () => {
      ghostAPI = new GhostAPI(mockConfig);
      expect(ghostAPI.options.verbose).to.be.false;
      expect(ghostAPI.options.dryRun).to.be.false;
    });
  });

  describe('loadConfig', () => {
    it('should throw error when config file does not exist', () => {
      expect(() => GhostAPI.loadConfig('nonexistent.yml')).to.throw('Ghost config file not found');
    });

    it('should throw error when config is missing page_sync integration', () => {
      const fs = require('fs');
      const yaml = require('js-yaml');

      sinon.stub(fs, 'existsSync').returns(true);
      sinon.stub(fs, 'readFileSync').returns('invalid: config');
      sinon.stub(yaml, 'load').returns({ invalid: 'config' });

      expect(() => GhostAPI.loadConfig('test.yml')).to.throw('Missing page_sync integration');
    });

    it('should throw error when admin_api_key is missing', () => {
      const fs = require('fs');
      const yaml = require('js-yaml');

      sinon.stub(fs, 'existsSync').returns(true);
      sinon.stub(fs, 'readFileSync').returns('config');
      sinon.stub(yaml, 'load').returns({
        integrations: {
          page_sync: {}
        }
      });

      expect(() => GhostAPI.loadConfig('test.yml')).to.throw('Missing admin_api_key');
    });
  });

  describe('initGhostAPI', () => {
    it('should throw error for invalid API key format', () => {
      const invalidConfig = { admin_api_key: 'invalid-key' };
      expect(() => new GhostAPI(invalidConfig)).to.throw('Invalid Admin API key format');
    });

    it('should initialize with valid API key format', () => {
      ghostAPI = new GhostAPI(mockConfig);
      expect(ghostAPI.api).to.exist;
    });
  });

  describe('findPosts', () => {
    beforeEach(() => {
      ghostAPI = new GhostAPI(mockConfig, mockOptions);
      // Mock the API
      ghostAPI.api = {
        posts: {
          browse: sinon.stub()
        }
      };
    });

    it('should find posts by title', async () => {
      const mockPosts = [
        { id: 1, title: 'Test Post', slug: 'test-post' }
      ];

      ghostAPI.api.posts.browse.resolves(mockPosts);

      const result = await ghostAPI.findPosts('Test Post');
      expect(result).to.deep.equal(mockPosts);
    });

    it('should return empty array when post not found', async () => {
      ghostAPI.api.posts.browse.resolves([]);

      const result = await ghostAPI.findPosts('Nonexistent Post');
      expect(result).to.deep.equal([]);
    });

    it('should fetch all posts when no title provided', async () => {
      const mockPosts = [
        { id: 1, title: 'Post 1' },
        { id: 2, title: 'Post 2' }
      ];

      ghostAPI.api.posts.browse
        .onFirstCall().resolves(mockPosts)
        .onSecondCall().resolves([]);

      const result = await ghostAPI.findPosts();
      expect(result).to.deep.equal(mockPosts);
    });
  });

  describe('createOrUpdatePost', () => {
    beforeEach(() => {
      ghostAPI = new GhostAPI(mockConfig, mockOptions);
      ghostAPI.api = {
        posts: {
          add: sinon.stub(),
          edit: sinon.stub()
        }
      };
    });

    it('should create new post when no existing post provided', async () => {
      const postData = { title: 'New Post', slug: 'new-post' };
      const createdPost = { id: 1, ...postData };

      ghostAPI.api.posts.add.resolves(createdPost);

      const result = await ghostAPI.createOrUpdatePost(postData);
      expect(result.action).to.equal('created');
      expect(result.post).to.deep.equal(createdPost);
    });

    it('should update existing post', async () => {
      const postData = { title: 'Updated Post', slug: 'updated-post' };
      const existingPost = { id: 1, updated_at: '2023-01-01' };
      const updatedPost = { id: 1, ...postData };

      ghostAPI.api.posts.edit.resolves(updatedPost);

      const result = await ghostAPI.createOrUpdatePost(postData, existingPost);
      expect(result.action).to.equal('updated');
      expect(result.post).to.deep.equal(updatedPost);
    });

    it('should return dry-run result when dryRun is true', async () => {
      ghostAPI.options.dryRun = true;
      const postData = { title: 'Test Post', slug: 'test-post' };

      const result = await ghostAPI.createOrUpdatePost(postData);
      expect(result.action).to.equal('create');
      expect(result.post).to.deep.equal(postData);
    });
  });
});
