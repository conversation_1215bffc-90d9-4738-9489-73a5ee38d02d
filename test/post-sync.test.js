#!/usr/bin/env node

const { describe, it, beforeEach, afterEach } = require('mocha');
const { expect } = require('chai');
const sinon = require('sinon');
const fs = require('fs');
const path = require('path');
const { PostSync } = require('../lib/post-sync');

describe('PostSync', () => {
  let postSync;
  let mockConfig;
  let mockOptions;

  beforeEach(() => {
    // Use valid Ghost API key format for tests
    mockConfig = {
      admin_api_key: '123456789012345678901234:1234567890123456789012345678901234567890123456789012345678901234'
    };
    mockOptions = {
      verbose: false,
      dryRun: true, // Use dry run for tests
      articlesDir: 'test-articles'
    };

    postSync = new PostSync(mockConfig, mockOptions);

    // Mock the GhostAPI
    postSync.ghostAPI = {
      findPosts: sinon.stub(),
      findPostBySlug: sinon.stub(),
      getCurrentUser: sinon.stub(),
      createOrUpdatePost: sinon.stub()
    };
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('constructor', () => {
    it('should initialize with config and options', () => {
      expect(postSync.config).to.deep.equal(mockConfig);
      expect(postSync.options.articlesDir).to.equal('test-articles');
    });
  });

  describe('syncPostsToLocal', () => {
    it('should sync posts from Ghost to local files', async () => {
      const mockPosts = [
        {
          title: 'Test Post',
          slug: 'test-post',
          published_at: '2023-01-01T12:00:00.000Z',
          tags: [{ name: 'test' }],
          html: '<p>Content</p>'
        }
      ];

      postSync.ghostAPI.findPosts.resolves(mockPosts);

      const result = await postSync.syncPostsToLocal();

      expect(result.synced).to.equal(1);
      expect(result.errors).to.equal(0);
    });

    it('should handle no posts found', async () => {
      postSync.ghostAPI.findPosts.resolves([]);

      const result = await postSync.syncPostsToLocal();

      expect(result.synced).to.equal(0);
    });

    it('should sync specific post by title', async () => {
      const mockPosts = [
        {
          title: 'Specific Post',
          slug: 'specific-post',
          published_at: '2023-01-01T12:00:00.000Z',
          tags: [],
          html: '<p>Specific content</p>'
        }
      ];

      postSync.ghostAPI.findPosts.withArgs('Specific Post').resolves(mockPosts);

      const result = await postSync.syncPostsToLocal('Specific Post');

      expect(result.synced).to.equal(1);
      expect(postSync.ghostAPI.findPosts.calledWith('Specific Post')).to.be.true;
    });
  });

  describe('parseArticle', () => {
    it('should parse article with frontmatter and content', () => {
      const content = `---
title: Test Article
date: 2023-01-01T12:00:00.000Z
tags:
  - test
---

# Test Content

This is the article content.`;

      const result = postSync.parseArticle(content);

      expect(result.frontMatter.title).to.equal('Test Article');
      expect(result.frontMatter.tags).to.deep.equal(['test']);
      expect(result.markdownContent).to.include('# Test Content');
      expect(result.htmlContent).to.include('<h1>Test Content</h1>');
    });

    it('should throw error for content without frontmatter', () => {
      const content = 'Just content without frontmatter';

      expect(() => postSync.parseArticle(content)).to.throw('No front matter found');
    });
  });

  describe('findArticles', () => {
    beforeEach(() => {
      sinon.stub(fs, 'readdirSync');
      sinon.stub(fs, 'existsSync');
    });

    it('should find markdown files in articles directory', () => {
      const mockEntries = [
        { name: 'article1.md', isDirectory: () => false },
        { name: 'article2.md', isDirectory: () => false },
        { name: 'not-markdown.txt', isDirectory: () => false }
      ];

      fs.readdirSync.returns(mockEntries);

      const result = postSync.findArticles();

      expect(result).to.have.length(2);
      expect(result[0]).to.include('article1.md');
      expect(result[1]).to.include('article2.md');
    });

    it('should find index.md files in subdirectories', () => {
      const mockEntries = [
        { name: 'subdir', isDirectory: () => true }
      ];

      fs.readdirSync.returns(mockEntries);
      fs.existsSync.returns(true);

      const result = postSync.findArticles();

      expect(result).to.have.length(1);
      expect(result[0]).to.include('index.md');
    });

    it('should skip _index.md files', () => {
      const mockEntries = [
        { name: '_index.md', isDirectory: () => false },
        { name: 'article.md', isDirectory: () => false }
      ];

      fs.readdirSync.returns(mockEntries);

      const result = postSync.findArticles();

      expect(result).to.have.length(1);
      expect(result[0]).to.include('article.md');
    });
  });

  describe('syncArticleToGhost', () => {
    beforeEach(() => {
      sinon.stub(fs, 'readFileSync');
    });

    it('should sync article to Ghost', async () => {
      const articleContent = `---
title: Test Article
slug: test-article
---

# Test Content`;

      fs.readFileSync.returns(articleContent);
      postSync.ghostAPI.getCurrentUser.resolves({ id: 'user-1' });
      postSync.ghostAPI.findPostBySlug.resolves(null);
      postSync.ghostAPI.createOrUpdatePost.resolves({ action: 'created', post: { id: 1 } });

      const result = await postSync.syncArticleToGhost('test-article.md');

      expect(result.action).to.equal('created');
      expect(postSync.ghostAPI.createOrUpdatePost.called).to.be.true;
    });

    it('should throw error for article without title', async () => {
      const articleContent = `---
slug: test-article
---

Content without title`;

      fs.readFileSync.returns(articleContent);

      try {
        await postSync.syncArticleToGhost('test-article.md');
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.include('must have a title');
      }
    });
  });
});
