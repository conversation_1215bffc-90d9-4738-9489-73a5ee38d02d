#!/usr/bin/env node

const { describe, it } = require('mocha');
const { expect } = require('chai');
const { ContentConverter } = require('../lib/content-converter');

describe('ContentConverter', () => {
  describe('htmlToMarkdown', () => {
    it('should convert basic HTML to Markdown', () => {
      const html = '<h1>Title</h1><p>This is a <strong>test</strong> paragraph.</p>';
      const expected = '# Title\n\nThis is a **test** paragraph.';

      const result = ContentConverter.htmlToMarkdown(html);
      expect(result).to.equal(expected);
    });

    it('should handle empty or null input', () => {
      expect(ContentConverter.htmlToMarkdown('')).to.equal('');
      expect(ContentConverter.htmlToMarkdown(null)).to.equal('');
      expect(ContentConverter.htmlToMarkdown(undefined)).to.equal('');
    });

    it('should convert lists correctly', () => {
      const html = '<ul><li>Item 1</li><li>Item 2</li></ul>';
      const expected = '- Item 1\n- Item 2';

      const result = ContentConverter.htmlToMarkdown(html);
      expect(result).to.equal(expected);
    });

    it('should convert links correctly', () => {
      const html = '<a href="https://example.com">Example Link</a>';
      const expected = '[Example Link](https://example.com)';

      const result = ContentConverter.htmlToMarkdown(html);
      expect(result).to.equal(expected);
    });
  });

  describe('createFilename', () => {
    it('should create valid filename from title', () => {
      const title = 'My Awesome Blog Post!';
      const expected = 'my-awesome-blog-post';

      const result = ContentConverter.createFilename(title);
      expect(result).to.equal(expected);
    });

    it('should handle special characters', () => {
      const title = 'Post with @#$% special chars & symbols';
      const expected = 'post-with-special-chars-symbols';

      const result = ContentConverter.createFilename(title);
      expect(result).to.equal(expected);
    });

    it('should handle multiple spaces and hyphens', () => {
      const title = 'Post   with    multiple   spaces';
      const expected = 'post-with-multiple-spaces';

      const result = ContentConverter.createFilename(title);
      expect(result).to.equal(expected);
    });
  });

  describe('slugify', () => {
    it('should create valid slug from text', () => {
      const text = 'My Awesome Blog Post!';
      const expected = 'my-awesome-blog-post';

      const result = ContentConverter.slugify(text);
      expect(result).to.equal(expected);
    });
  });

  describe('decodeHtmlEntities', () => {
    it('should decode common HTML entities', () => {
      const text = 'This &quot;test&quot; has &amp; entities &#39;here&#39;';
      const expected = 'This "test" has & entities \'here\'';

      const result = ContentConverter.decodeHtmlEntities(text);
      expect(result).to.equal(expected);
    });

    it('should handle null or undefined input', () => {
      expect(ContentConverter.decodeHtmlEntities(null)).to.be.null;
      expect(ContentConverter.decodeHtmlEntities(undefined)).to.be.undefined;
    });
  });

  describe('generateExcerpt', () => {
    it('should use frontmatter excerpt if available', () => {
      const frontMatter = { excerpt: 'This is a custom excerpt' };
      const htmlContent = '<p>This is the full content that should be ignored</p>';

      const result = ContentConverter.generateExcerpt(frontMatter, htmlContent);
      expect(result).to.equal('This is a custom excerpt');
    });

    it('should use frontmatter description if no excerpt', () => {
      const frontMatter = { description: 'This is a description' };
      const htmlContent = '<p>This is the full content</p>';

      const result = ContentConverter.generateExcerpt(frontMatter, htmlContent);
      expect(result).to.equal('This is a description');
    });

    it('should extract from content if no frontmatter excerpt', () => {
      const frontMatter = {};
      const htmlContent = '<p>This is the content that should be extracted</p>';

      const result = ContentConverter.generateExcerpt(frontMatter, htmlContent);
      expect(result).to.equal('This is the content that should be extracted');
    });

    it('should truncate long content to 300 characters', () => {
      const frontMatter = {};
      const longContent = '<p>' + 'a'.repeat(400) + '</p>';

      const result = ContentConverter.generateExcerpt(frontMatter, longContent);
      expect(result.length).to.be.at.most(300);
      expect(result).to.include('...');
    });

    it('should decode HTML entities in excerpts', () => {
      const frontMatter = { excerpt: 'This &quot;excerpt&quot; has entities' };

      const result = ContentConverter.generateExcerpt(frontMatter, '');
      expect(result).to.equal('This "excerpt" has entities');
    });
  });

  describe('convertGhostPostToArticle', () => {
    it('should convert Ghost post to article format', () => {
      const ghostPost = {
        title: 'Test Post',
        slug: 'test-post',
        published_at: '2023-01-01T12:00:00.000Z',
        tags: [{ name: 'test' }, { name: 'blog' }],
        html: '<p>This is the content</p>',
        custom_excerpt: 'Test excerpt'
      };

      const result = ContentConverter.convertGhostPostToArticle(ghostPost);

      expect(result).to.include('title: Test Post');
      expect(result).to.include('slug: test-post');
      expect(result).to.include('- test');
      expect(result).to.include('- blog');
      expect(result).to.include('excerpt: Test excerpt');
      expect(result).to.include('This is the content');
    });

    it('should handle posts without tags', () => {
      const ghostPost = {
        title: 'Test Post',
        slug: 'test-post',
        published_at: '2023-01-01T12:00:00.000Z',
        html: '<p>Content</p>'
      };

      const result = ContentConverter.convertGhostPostToArticle(ghostPost);
      expect(result).to.include('tags: []');
    });

    it('should prefer mobiledoc markdown over HTML', () => {
      const ghostPost = {
        title: 'Test Post',
        slug: 'test-post',
        published_at: '2023-01-01T12:00:00.000Z',
        html: '<p>HTML content</p>',
        mobiledoc: JSON.stringify({
          cards: [['markdown', { markdown: '# Markdown content' }]]
        })
      };

      const result = ContentConverter.convertGhostPostToArticle(ghostPost);
      expect(result).to.include('# Markdown content');
      expect(result).not.to.include('HTML content');
    });
  });

  describe('createGhostPostData', () => {
    it('should create Ghost post data from local content', () => {
      const frontMatter = {
        title: 'Test Post',
        date: '2023-01-01T12:00:00.000Z',
        tags: ['test', 'blog']
      };
      const markdownContent = '# Test\n\nThis is content';
      const htmlContent = '<h1>Test</h1><p>This is content</p>';

      const result = ContentConverter.createGhostPostData(
        frontMatter,
        markdownContent,
        htmlContent,
        { status: 'published' }
      );

      expect(result.title).to.equal('Test Post');
      expect(result.status).to.equal('published');
      expect(result.published_at).to.equal('2023-01-01T12:00:00.000Z');
      expect(result.mobiledoc).to.include('markdown');
    });

    it('should use HTML when useHtml option is true', () => {
      const frontMatter = { title: 'Test Post' };
      const markdownContent = '# Test';
      const htmlContent = '<h1>Test</h1>';

      const result = ContentConverter.createGhostPostData(
        frontMatter,
        markdownContent,
        htmlContent,
        { useHtml: true }
      );

      expect(result.html).to.equal('<h1>Test</h1>');
      expect(result.mobiledoc).to.be.null;
    });
  });
});
