# Ghost Sync Setup - Complete Implementation

## Overview

This implementation provides a complete Ghost.io sync solution with:

1. **Refactored Ghost.js Script** - Extended with post syncing capabilities
2. **Reusable Modules** - Extracted Ghost API functionality into reusable modules
3. **Obsidian Plugin** - Custom plugin for seamless Obsidian ↔ Ghost workflow
4. **Comprehensive Tests** - Full test suite for all modules
5. **Easy Installation** - Automated installation script

## Architecture

### Core Modules (`lib/`)

- **`ghost-api.js`** - Ghost Admin API wrapper with high-level methods
- **`content-converter.js`** - Content conversion utilities (HTML ↔ Markdown, frontmatter)
- **`post-sync.js`** - Post synchronization logic between Ghost and local files

### Scripts

- **`scripts/ghost.js`** - Main CLI script (now uses the reusable modules)
- **`install-plugin.sh`** - Automated Obsidian plugin installation

### Obsidian Plugin (`obsidian-ghost-sync/`)

- **`main.ts`** - Plugin implementation with commands and UI
- **`manifest.json`** - Plugin metadata
- **Build system** - TypeScript + esbuild for development

### Tests (`test/`)

- **`ghost-api.test.js`** - Tests for Ghost API wrapper
- **`content-converter.test.js`** - Tests for content conversion utilities
- **`post-sync.test.js`** - Tests for post synchronization logic

## Features Implemented

### ✅ Extended Ghost.js Script

**New Commands:**
```bash
# Sync specific post from Ghost to local
node scripts/ghost.js --sync-posts --title "Post Title"

# Sync all posts from Ghost to local
node scripts/ghost.js --sync-posts

# Dry run to see what would be synced
node scripts/ghost.js --sync-posts --dry-run --verbose
```

**New Options:**
- `--sync-posts` - Enable post syncing from Ghost to local
- `--title <title>` - Specific post title to sync
- `--articles-dir <dir>` - Articles directory (default: `articles`)

### ✅ Obsidian Plugin

**Commands Available:**
- "Sync current post to Ghost" - Publishes current file to Ghost
- "Sync post from Ghost to local" - Downloads specific post by title
- "Sync all posts from Ghost to local" - Downloads all posts

**Features:**
- Ribbon icon for quick sync
- Settings panel for configuration
- Error handling and user feedback
- Leverages existing `ghost.js` script

### ✅ Reusable Modules

**GhostAPI Class:**
- `findPosts(title)` - Find posts by title or get all posts
- `findPostBySlug(slug)` - Find post by slug
- `createOrUpdatePost(data, existing)` - Create or update posts
- `updatePage(data, existing)` - Update pages
- `deletePost(id)` - Delete posts

**ContentConverter Class:**
- `htmlToMarkdown(html)` - Convert HTML to Markdown
- `convertGhostPostToArticle(post)` - Convert Ghost post to local format
- `createGhostPostData(frontMatter, markdown, html)` - Create Ghost post data
- `generateExcerpt(frontMatter, html)` - Generate post excerpts

**PostSync Class:**
- `syncPostsToLocal(title)` - Sync posts from Ghost to local files
- `syncArticleToGhost(path, options)` - Sync local article to Ghost
- `findArticles(dir)` - Find all articles in directory

### ✅ Comprehensive Testing

**Test Coverage:**
- 44 passing tests
- Unit tests for all major functions
- Mocked Ghost API for safe testing
- Content conversion validation
- Error handling verification

## Installation & Usage

### 1. Install Dependencies
```bash
npm install
```

### 2. Install Obsidian Plugin
```bash
./install-plugin.sh
```

### 3. Configure Obsidian Plugin
1. Restart Obsidian
2. Go to Settings → Community Plugins
3. Enable "Ghost Sync" plugin
4. Configure settings:
   - Ghost script path: `scripts/ghost.js`
   - Articles directory: `articles`

### 4. Test the Setup
```bash
npm test
```

## Development Workflow

### Making Changes to the Plugin

1. **Edit the plugin source:**
   ```bash
   # Edit obsidian-ghost-sync/main.ts
   ```

2. **Rebuild and install:**
   ```bash
   npm run build-plugin
   ./install-plugin.sh
   ```

3. **For development with auto-rebuild:**
   ```bash
   npm run dev-plugin
   ```

### Making Changes to Core Modules

1. **Edit modules in `lib/`**
2. **Run tests:**
   ```bash
   npm test
   ```
3. **Test with ghost.js:**
   ```bash
   node scripts/ghost.js --sync-posts --dry-run
   ```

### Available NPM Scripts

```bash
npm test                 # Run all tests
npm run test:watch       # Run tests in watch mode
npm run test:coverage    # Run tests with coverage
npm run build-plugin     # Build Obsidian plugin
npm run dev-plugin       # Build plugin in watch mode
npm run install-plugin   # Install plugin to Obsidian
npm run ghost-sync:posts # Sync posts from Ghost
```

## Usage Examples

### CLI Usage
```bash
# Sync specific post
node scripts/ghost.js --sync-posts --title "Announcing TextParser for Elixir"

# Sync all posts with verbose output
node scripts/ghost.js --sync-posts --verbose

# Dry run to see what would be synced
node scripts/ghost.js --sync-posts --dry-run

# Update posts to Ghost (existing functionality)
node scripts/ghost.js --update-posts --published
```

### Obsidian Plugin Usage

1. **Sync to Ghost:**
   - Open article in `articles/` directory
   - Click sync icon or use Command Palette
   - Article is published to Ghost

2. **Sync from Ghost:**
   - Use Command Palette: "Sync post from Ghost to local"
   - Enter post title
   - Post is downloaded to `articles/` directory

3. **Bulk sync:**
   - Use Command Palette: "Sync all posts from Ghost to local"
   - All posts are downloaded

## File Structure

```
├── lib/                          # Reusable modules
│   ├── ghost-api.js             # Ghost API wrapper
│   ├── content-converter.js     # Content conversion utilities
│   └── post-sync.js             # Post sync logic
├── scripts/
│   └── ghost.js                 # Main CLI script (refactored)
├── obsidian-ghost-sync/         # Obsidian plugin
│   ├── main.ts                  # Plugin source
│   ├── manifest.json            # Plugin metadata
│   └── package.json             # Plugin dependencies
├── test/                        # Test suite
│   ├── ghost-api.test.js
│   ├── content-converter.test.js
│   └── post-sync.test.js
├── install-plugin.sh            # Installation script
└── package.json                 # Main dependencies
```

## Benefits

1. **Code Reusability** - Ghost API logic is now modular and reusable
2. **Easy Testing** - Comprehensive test suite ensures reliability
3. **Developer Experience** - Easy installation and development workflow
4. **User Experience** - Seamless Obsidian integration
5. **Maintainability** - Clean separation of concerns
6. **Extensibility** - Easy to add new features or integrations

The implementation provides exactly what you requested: the ability to sync posts between Ghost and local files, with both CLI and Obsidian plugin interfaces, backed by a robust and tested codebase.
