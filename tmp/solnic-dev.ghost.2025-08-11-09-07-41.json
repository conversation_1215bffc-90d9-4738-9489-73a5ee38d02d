{"db": [{"meta": {"exported_on": 1754903261676, "version": "6.0.0-0-g5b8c97d"}, "data": {"benefits": [], "custom_theme_settings": [{"id": "68969ddc34b12e0001295fb9", "theme": "source", "key": "navigation_layout", "type": "select", "value": "Logo in the middle"}, {"id": "68969ddc34b12e0001295fba", "theme": "source", "key": "site_background_color", "type": "color", "value": "#ffffff"}, {"id": "68969ddc34b12e0001295fbb", "theme": "source", "key": "header_and_footer_color", "type": "select", "value": "Background color"}, {"id": "68969ddc34b12e0001295fbc", "theme": "source", "key": "title_font", "type": "select", "value": "Modern sans-serif"}, {"id": "68969ddc34b12e0001295fbd", "theme": "source", "key": "body_font", "type": "select", "value": "Modern sans-serif"}, {"id": "68969ddc34b12e0001295fbe", "theme": "source", "key": "signup_heading", "type": "text", "value": null}, {"id": "68969ddc34b12e0001295fbf", "theme": "source", "key": "signup_subheading", "type": "text", "value": null}, {"id": "68969ddc34b12e0001295fc0", "theme": "source", "key": "header_style", "type": "select", "value": "Landing"}, {"id": "68969ddc34b12e0001295fc1", "theme": "source", "key": "header_text", "type": "text", "value": null}, {"id": "68969ddc34b12e0001295fc2", "theme": "source", "key": "background_image", "type": "boolean", "value": "true"}, {"id": "68969ddc34b12e0001295fc3", "theme": "source", "key": "show_featured_posts", "type": "boolean", "value": "false"}, {"id": "68969ddc34b12e0001295fc4", "theme": "source", "key": "post_feed_style", "type": "select", "value": "List"}, {"id": "68969ddc34b12e0001295fc5", "theme": "source", "key": "show_images_in_feed", "type": "boolean", "value": "true"}, {"id": "68969ddc34b12e0001295fc6", "theme": "source", "key": "show_author", "type": "boolean", "value": "true"}, {"id": "68969ddc34b12e0001295fc7", "theme": "source", "key": "show_publish_date", "type": "boolean", "value": "true"}, {"id": "68969ddc34b12e0001295fc8", "theme": "source", "key": "show_publication_info_sidebar", "type": "boolean", "value": "false"}, {"id": "68969ddc34b12e0001295fc9", "theme": "source", "key": "show_post_metadata", "type": "boolean", "value": "true"}, {"id": "68969ddc34b12e0001295fca", "theme": "source", "key": "enable_drop_caps_on_posts", "type": "boolean", "value": "false"}, {"id": "68969ddc34b12e0001295fcb", "theme": "source", "key": "show_related_articles", "type": "boolean", "value": "true"}, {"id": "68990d9fcf021a000108c10b", "theme": "attila", "key": "color_scheme", "type": "select", "value": "System"}, {"id": "68990d9fcf021a000108c10c", "theme": "attila", "key": "disqus_shortname", "type": "text", "value": null}, {"id": "68990d9fcf021a000108c10d", "theme": "attila", "key": "darkmode_accent_color", "type": "color", "value": "#ff6633"}, {"id": "68990d9fcf021a000108c10e", "theme": "attila", "key": "post_share", "type": "select", "value": "<PERSON><PERSON><PERSON>"}, {"id": "6899aa9b5f4a9400010d2161", "theme": "solo", "key": "background_color", "type": "color", "value": "#2e1349"}, {"id": "6899aa9b5f4a9400010d2162", "theme": "solo", "key": "navigation_layout", "type": "select", "value": "Logo on the left"}, {"id": "6899aa9b5f4a9400010d2163", "theme": "solo", "key": "typography", "type": "select", "value": "Modern sans-serif"}, {"id": "6899aa9b5f4a9400010d2164", "theme": "solo", "key": "footer_text", "type": "text", "value": "solnic.dev © All Rights Reserved"}, {"id": "6899aa9b5f4a9400010d2165", "theme": "solo", "key": "header_section_layout", "type": "select", "value": "Typographic profile"}, {"id": "6899aa9b5f4a9400010d2166", "theme": "solo", "key": "primary_header", "type": "text", "value": "<PERSON><PERSON><PERSON>'s dev blog"}, {"id": "6899aa9b5f4a9400010d2167", "theme": "solo", "key": "secondary_header", "type": "text", "value": "Subscribe to receive latest articles about Elixir, Ruby, AI-drive development and programming"}, {"id": "6899aa9b5f4a9400010d2168", "theme": "solo", "key": "post_feed_layout", "type": "select", "value": "Classic"}], "newsletters": [{"id": "68969dd8ad492f0008be27e5", "uuid": "e197d5ff-f6ba-4a72-8806-9f68f27243fb", "name": "solnic.dev", "description": null, "feedback_enabled": 0, "slug": "default-newsletter", "sender_name": null, "sender_email": null, "sender_reply_to": "newsletter", "status": "active", "visibility": "members", "subscribe_on_signup": 1, "sort_order": 0, "header_image": null, "show_header_icon": 1, "show_header_title": 1, "show_excerpt": 0, "title_font_category": "sans_serif", "title_alignment": "center", "show_feature_image": 1, "body_font_category": "sans_serif", "footer_content": null, "show_badge": 1, "show_header_name": 0, "show_post_title_section": 1, "show_comment_cta": 1, "show_subscription_details": 0, "show_latest_posts": 0, "background_color": "light", "post_title_color": null, "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T05:10:23.000Z", "button_corners": "rounded", "button_style": "fill", "title_font_weight": "bold", "link_style": "underline", "image_corners": "square", "header_background_color": "transparent", "section_title_color": null, "divider_color": null, "button_color": "accent", "link_color": "accent"}], "offer_redemptions": [], "offers": [], "posts": [{"id": "68969dd9ad492f0008be2861", "uuid": "547bf044-575c-4cd0-a841-8a0596c37753", "title": "About", "slug": "about", "mobiledoc": null, "lexical": "{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"<PERSON> is a seasoned Lead Software Engineer, who specializes in Elixir and Ruby programming languages, AI-driven development, and has over 20 years of demonstrated experience in building software, providing technical guidance, team leadership and Open Source library and framework development.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"horizontalrule\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Socials\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Bsky: \",\"type\":\"extended-text\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"https://bsky.app/profile/solnic.dev\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://bsky.app/profile/solnic.dev\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Mastodon: \",\"type\":\"extended-text\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"https://hachyderm.io/solnic\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://hachyderm.io/solnic\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"LinkedIn: \",\"type\":\"extended-text\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"www.linkedin.com/in/solnic\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"http://www.linkedin.com/in/solnic\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"GitHub: \",\"type\":\"extended-text\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"github.com/solnic\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"http://github.com/solnic\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}", "html": "<p><PERSON> is a seasoned Lead Software Engineer, who specializes in Elixir and Ruby programming languages, AI-driven development, and has over 20 years of demonstrated experience in building software, providing technical guidance, team leadership and Open Source library and framework development.</p><hr><h2 id=\"socials\">Socials</h2><ul><li>Bsky: <a href=\"https://bsky.app/profile/solnic.dev\">https://bsky.app/profile/solnic.dev</a></li><li>Ma<PERSON>don: <a href=\"https://hachyderm.io/solnic\">https://hachyderm.io/solnic</a></li><li>LinkedIn: <a href=\"http://www.linkedin.com/in/solnic\">www.linkedin.com/in/solnic</a></li><li>GitHub: <a href=\"http://github.com/solnic\">github.com/solnic</a></li></ul>", "comment_id": "68969dd9ad492f0008be2861", "plaintext": "<PERSON> is a seasoned Lead Software Engineer, who specializes in Elixir and Ruby programming languages, AI-driven development, and has over 20 years of demonstrated experience in building software, providing technical guidance, team leadership and Open Source library and framework development.\n\n\nSocials\n\n * Bsky: https://bsky.app/profile/solnic.dev\n * Mastodon: https://hachyderm.io/solnic\n * LinkedIn: www.linkedin.com/in/solnic\n * GitHub: github.com/solnic", "feature_image": null, "featured": 0, "type": "page", "status": "published", "locale": null, "visibility": "public", "email_recipient_filter": "all", "created_at": "2025-08-09T01:01:13.000Z", "updated_at": "2025-08-11T07:41:18.000Z", "published_at": "2025-08-09T01:01:13.000Z", "custom_excerpt": null, "codeinjection_head": null, "codeinjection_foot": null, "custom_template": null, "canonical_url": null, "newsletter_id": null, "show_title_and_feature_image": 1}, {"id": "6896e0b842a07b0001ad0347", "uuid": "b10a9ad7-c7f5-4d60-8cac-a6459bb594b9", "title": "Announcing TextParser for Elixir", "slug": "announcing-textparser-for-elixir", "mobiledoc": null, "lexical": "{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"I'm excited to announce the initial release of TextParser, a new Elixir library for extracting and validating structured tokens from text. Whether you need to parse URLs, hashtags, mentions, or custom patterns, TextParser provides a flexible and extensible solution.\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Why TextParser?\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"heading\",\"tag\":\"h2\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"<PERSON><PERSON><PERSON><PERSON> was born from real-world needs at \",\"type\":\"text\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"justcrosspost.app\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://justcrosspost.app\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\", where processing tags, mentions, and URLs for Bluesky required precise handling of text tokens. The library has been designed with several key features in mind:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Accurate Position Tracking\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Each extracted token includes exact byte positions in the original text\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":1,\"version\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Built-in Token Types\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Ready-to-use parsers for URLs, hashtags, and @-mentions\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":2,\"version\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Custom Token Support\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Easy creation of custom token extractors\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":3,\"version\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Validation Rules\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Flexible token validation through pattern matching and custom rules\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":4,\"version\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Unicode Support\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Proper handling of emoji and other Unicode characters\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":5,\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"tag\":\"ul\",\"type\":\"list\",\"listType\":\"bullet\",\"start\":1,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Quick Start\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"heading\",\"tag\":\"h2\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Add TextParser to your project's dependencies:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"code\":\"def deps do\\n  [\\n    {:text_parser, \\\"~> 0.1\\\"}\\n  ]\\nend\\n\",\"language\":\"elixir\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Basic usage is straightforward:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"code\":\"text = \\\"Check out https://elixir-lang.org #elixir\\\"\\nresult = TextParser.parse(text)\\n\\n# Extract URLs\\nurls = TextParser.get(result, TextParser.Tokens.URL)\\n# => [%TextParser.Tokens.URL{value: \\\"https://elixir-lang.org\\\", position: {10, 32}}]\\n\\n# Extract hashtags\\ntags = TextParser.get(result, TextParser.Tokens.Tag)\\n# => [%TextParser.Tokens.Tag{value: \\\"#elixir\\\", position: {33, 40}}]\\n\",\"language\":\"elixir\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Custom Token Types\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"heading\",\"tag\":\"h2\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"One of TextParser's strengths is its extensibility. Here's an example of a custom token for extracting ISO 8601 dates:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"code\":\"defmodule MyParser.Tokens.Date do\\n  use TextParser.Token,\\n    pattern: ~r/(?:^|\\\\s)(\\\\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[12]\\\\d|3[01]))/,\\n    trim_chars: [\\\",\\\", \\\".\\\", \\\"!\\\", \\\"?\\\"]\\n\\n  def is_valid?(date_text) when is_binary(date_text) do\\n    case Date.from_iso8601(date_text) do\\n      {:ok, _date} -> true\\n      _ -> false\\n    end\\n  end\\nend\\n\\n# Usage\\ntext = \\\"Meeting on 2024-01-15, party on 2024-12-31!\\\"\\nresult = TextParser.parse(text, extract: [MyParser.Tokens.Date])\\n\",\"language\":\"elixir\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Custom Validation Rules\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"heading\",\"tag\":\"h2\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Need custom validation? TextParser provides a behaviour that you can use to implement your own validation rules:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"code\":\"defmodule BlueskyParser do\\n  use TextParser\\n\\n  def validate(%TextParser.Tokens.Tag{value: value} = tag) do\\n    if String.length(value) >= 66,\\n      do: {:error, \\\"tag too long\\\"},\\n      else: {:ok, tag}\\n  end\\nend\\n\",\"language\":\"elixir\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"What's Next?\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"heading\",\"tag\":\"h2\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"This initial release provides a solid foundation for text token extraction, but this is just a good start 🙂 Here some things I'm planning to work on next:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Additional built-in token types\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":1,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Integration with NimbleParsec for simpler and more composable extraction rules\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":2,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Integration guides for popular frameworks\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":3,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Removal of a couple of bluesky-specific pieces in Tag handling\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":4,\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"tag\":\"ul\",\"type\":\"list\",\"listType\":\"bullet\",\"start\":1,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Get Started\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"heading\",\"tag\":\"h2\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"GitHub: \",\"type\":\"text\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"https://github.com/solnic/text_parser\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/solnic/text_parser\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":1,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Documentation: \",\"type\":\"text\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"https://hexdocs.pm/text_parser\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://hexdocs.pm/text_parser\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":2,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Issues & Feature Requests: \",\"type\":\"text\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"GitHub Issues\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/solnic/text_parser/issues\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":3,\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"tag\":\"ul\",\"type\":\"list\",\"listType\":\"bullet\",\"start\":1,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Contributions and feedback are welcome! Whether you find a bug, have a feature request, or want to contribute code, please feel free to get involved.\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}", "html": "<p>I'm excited to announce the initial release of TextParser, a new Elixir library for extracting and validating structured tokens from text. Whether you need to parse URLs, hashtags, mentions, or custom patterns, TextParser provides a flexible and extensible solution.</p><h2 id=\"why-textparser\">Why TextParser?</h2><p><PERSON><PERSON><PERSON><PERSON> was born from real-world needs at <a href=\"https://justcrosspost.app\">justcrosspost.app</a>, where processing tags, mentions, and URLs for <PERSON>ky required precise handling of text tokens. The library has been designed with several key features in mind:</p><ul><li><strong>Accurate Position Tracking</strong>: Each extracted token includes exact byte positions in the original text</li><li><strong>Built-in Token Types</strong>: Ready-to-use parsers for URLs, hashtags, and @-mentions</li><li><strong>Custom Token Support</strong>: Easy creation of custom token extractors</li><li><strong>Validation Rules</strong>: Flexible token validation through pattern matching and custom rules</li><li><strong>Unicode Support</strong>: Proper handling of emoji and other Unicode characters</li></ul><h2 id=\"quick-start\">Quick Start</h2><p>Add TextParser to your project's dependencies:</p><pre><code class=\"language-elixir\">def deps do\n  [\n    {:text_parser, \"~&gt; 0.1\"}\n  ]\nend\n</code></pre><p>Basic usage is straightforward:</p><pre><code class=\"language-elixir\">text = \"Check out https://elixir-lang.org #elixir\"\nresult = TextParser.parse(text)\n\n# Extract URLs\nurls = TextParser.get(result, TextParser.Tokens.URL)\n# =&gt; [%TextParser.Tokens.URL{value: \"https://elixir-lang.org\", position: {10, 32}}]\n\n# Extract hashtags\ntags = TextParser.get(result, TextParser.Tokens.Tag)\n# =&gt; [%TextParser.Tokens.Tag{value: \"#elixir\", position: {33, 40}}]\n</code></pre><h2 id=\"custom-token-types\">Custom Token Types</h2><p>One of TextParser's strengths is its extensibility. Here's an example of a custom token for extracting ISO 8601 dates:</p><pre><code class=\"language-elixir\">defmodule MyParser.Tokens.Date do\n  use TextParser.Token,\n    pattern: ~r/(?:^|\\s)(\\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[12]\\d|3[01]))/,\n    trim_chars: [\",\", \".\", \"!\", \"?\"]\n\n  def is_valid?(date_text) when is_binary(date_text) do\n    case Date.from_iso8601(date_text) do\n      {:ok, _date} -&gt; true\n      _ -&gt; false\n    end\n  end\nend\n\n# Usage\ntext = \"Meeting on 2024-01-15, party on 2024-12-31!\"\nresult = TextParser.parse(text, extract: [MyParser.Tokens.Date])\n</code></pre><h2 id=\"custom-validation-rules\">Custom Validation Rules</h2><p>Need custom validation? TextParser provides a behaviour that you can use to implement your own validation rules:</p><pre><code class=\"language-elixir\">defmodule BlueskyParser do\n  use TextParser\n\n  def validate(%TextParser.Tokens.Tag{value: value} = tag) do\n    if String.length(value) &gt;= 66,\n      do: {:error, \"tag too long\"},\n      else: {:ok, tag}\n  end\nend\n</code></pre><h2 id=\"whats-next\">What's Next?</h2><p>This initial release provides a solid foundation for text token extraction, but this is just a good start 🙂 Here some things I'm planning to work on next:</p><ul><li>Additional built-in token types</li><li>Integration with NimbleParsec for simpler and more composable extraction rules</li><li>Integration guides for popular frameworks</li><li>Removal of a couple of bluesky-specific pieces in Tag handling</li></ul><h2 id=\"get-started\">Get Started</h2><ul><li>GitHub: <a href=\"https://github.com/solnic/text_parser\">https://github.com/solnic/text_parser</a></li><li>Documentation: <a href=\"https://hexdocs.pm/text_parser\">https://hexdocs.pm/text_parser</a></li><li>Issues &amp; Feature Requests: <a href=\"https://github.com/solnic/text_parser/issues\">GitHub Issues</a></li></ul><p>Contributions and feedback are welcome! Whether you find a bug, have a feature request, or want to contribute code, please feel free to get involved.</p>", "comment_id": "d010cb6fefaa1a981a1abfe7", "plaintext": "I'm excited to announce the initial release of TextParser, a new Elixir library for extracting and validating structured tokens from text. Whether you need to parse URLs, hashtags, mentions, or custom patterns, TextParser provides a flexible and extensible solution.\n\n\nWhy TextParser?\n\nTextParser was born from real-world needs at justcrosspost.app, where processing tags, mentions, and URLs for <PERSON><PERSON> required precise handling of text tokens. The library has been designed with several key features in mind:\n\n * Accurate Position Tracking: Each extracted token includes exact byte positions in the original text\n * Built-in Token Types: Ready-to-use parsers for URLs, hashtags, and @-mentions\n * Custom Token Support: Easy creation of custom token extractors\n * Validation Rules: Flexible token validation through pattern matching and custom rules\n * Unicode Support: Proper handling of emoji and other Unicode characters\n\n\nQuick Start\n\nAdd TextParser to your project's dependencies:\n\ndef deps do\n  [\n    {:text_parser, \"~> 0.1\"}\n  ]\nend\n\n\nBasic usage is straightforward:\n\ntext = \"Check out https://elixir-lang.org #elixir\"\nresult = TextParser.parse(text)\n\n# Extract URLs\nurls = TextParser.get(result, TextParser.Tokens.URL)\n# => [%TextParser.Tokens.URL{value: \"https://elixir-lang.org\", position: {10, 32}}]\n\n# Extract hashtags\ntags = TextParser.get(result, TextParser.Tokens.Tag)\n# => [%TextParser.Tokens.Tag{value: \"#elixir\", position: {33, 40}}]\n\n\n\nCustom Token Types\n\nOne of TextParser's strengths is its extensibility. Here's an example of a custom token for extracting ISO 8601 dates:\n\ndefmodule MyParser.Tokens.Date do\n  use TextParser.Token,\n    pattern: ~r/(?:^|\\s)(\\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[12]\\d|3[01]))/,\n    trim_chars: [\",\", \".\", \"!\", \"?\"]\n\n  def is_valid?(date_text) when is_binary(date_text) do\n    case Date.from_iso8601(date_text) do\n      {:ok, _date} -> true\n      _ -> false\n    end\n  end\nend\n\n# Usage\ntext = \"Meeting on 2024-01-15, party on 2024-12-31!\"\nresult = TextParser.parse(text, extract: [MyParser.Tokens.Date])\n\n\n\nCustom Validation Rules\n\nNeed custom validation? TextParser provides a behaviour that you can use to implement your own validation rules:\n\ndefmodule BlueskyParser do\n  use TextParser\n\n  def validate(%TextParser.Tokens.Tag{value: value} = tag) do\n    if String.length(value) >= 66,\n      do: {:error, \"tag too long\"},\n      else: {:ok, tag}\n  end\nend\n\n\n\nWhat's Next?\n\nThis initial release provides a solid foundation for text token extraction, but this is just a good start 🙂 Here some things I'm planning to work on next:\n\n * Additional built-in token types\n * Integration with NimbleParsec for simpler and more composable extraction rules\n * Integration guides for popular frameworks\n * Removal of a couple of bluesky-specific pieces in Tag handling\n\n\nGet Started\n\n * GitHub: https://github.com/solnic/text_parser\n * Documentation: https://hexdocs.pm/text_parser\n * Issues & Feature Requests: GitHub Issues\n\nContributions and feedback are welcome! Whether you find a bug, have a feature request, or want to contribute code, please feel free to get involved.", "feature_image": null, "featured": 0, "type": "post", "status": "published", "locale": null, "visibility": "public", "email_recipient_filter": "all", "created_at": "2025-03-14T11:05:21.000Z", "updated_at": "2025-08-09T05:47:14.000Z", "published_at": "2025-03-14T11:05:21.000Z", "custom_excerpt": null, "codeinjection_head": null, "codeinjection_foot": null, "custom_template": null, "canonical_url": null, "newsletter_id": null, "show_title_and_feature_image": 1}, {"id": "6896e0b842a07b0001ad0348", "uuid": "b9a92d35-1e7e-4dcf-9ba3-e4476e192220", "title": "TIL - Capturing logs in Elixir tests", "slug": "til-capturing-logs-in-elixir-tests", "mobiledoc": null, "lexical": "{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Testing logging can be tricky but recently I've learned that Elixir's ExUnit ships with a \",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"CaptureLog\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" module that makes it easy to test logging behavior. I was really happy to see this built-in!\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Let's say you have a function that processes a template and logs errors when something goes wrong. You want to test both the error handling and ensure the right messages are being logged. Here's a simplified version of such a function:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"code\":\"def render_template(%SourceFile{is_template: true} = source_file, vars) do\\n  case Solid.parse(source_file.content) do\\n    {:ok, template} ->\\n      # Template parsing succeeded, render it...\\n\\n    {:error, reason} ->\\n      Logger.error(\\\"Template parsing failed\\\", reason: reason)\\n      {:error, :template_parsing_failed}\\n  end\\nend\\n\",\"language\":\"elixir\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Without \",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"ExUnit.CaptureLog\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\", testing this would result in error messages cluttering your test output, even though those errors are expected as part of the test.\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"ExUnit.CaptureLog\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" allows you to:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Capture log messages during test execution\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":1,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Assert against the content of those messages\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":2,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Keep your test output clean by suppressing expected error logs\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":3,\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"tag\":\"ol\",\"type\":\"list\",\"listType\":\"number\",\"start\":1,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Here's how I used it:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"code\":\"defmodule YourTest do\\n  use ExUnit.Case\\n\\n  import ExUnit.CaptureLog\\n\\n  test \\\"handles invalid template syntax\\\" do\\n    file = %SourceFile{\\n      content: \\\"{{ unclosed tag\\\",\\n      is_template: true\\n    }\\n\\n    # Capture logs during template rendering\\n    log = capture_log(fn ->\\n      assert {:error, \\\"Template parsing failed: \\\" <> _} =\\n        render_template(file, %{})\\n    end)\\n\\n    # Assert against the captured log content\\n    assert log =~ \\\"Template parsing failed\\\"\\n    assert log =~ \\\"expected end of string\\\"\\n    assert log =~ \\\"{{ unclosed tag\\\"\\n  end\\nend\\n\",\"language\":\"elixir\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Breaking Down the Test\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"heading\",\"tag\":\"h2\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Let's see what's happening:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"First, we import the helper function:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":1,\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"tag\":\"ol\",\"type\":\"list\",\"listType\":\"number\",\"start\":1,\"version\":1},{\"type\":\"codeblock\",\"code\":\"import ExUnit.CaptureLog\\n\",\"language\":\"elixir\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We wrap the code that generates logs in a \",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"capture_log\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" function:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":1,\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"tag\":\"ol\",\"type\":\"list\",\"listType\":\"number\",\"start\":1,\"version\":1},{\"type\":\"codeblock\",\"code\":\"log = capture_log(fn ->\\n  # Code that generates logs\\nend)\\n\",\"language\":\"elixir\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"The \",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"capture_log\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" function:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":1,\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"tag\":\"ol\",\"type\":\"list\",\"listType\":\"number\",\"start\":1,\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Takes a function as an argument\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":1,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Executes that function\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":2,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Captures any log output during execution\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":3,\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Returns the captured log as a string\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":4,\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"tag\":\"ul\",\"type\":\"list\",\"listType\":\"bullet\",\"start\":1,\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We can then make assertions about the log content using string matching:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":1,\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"tag\":\"ol\",\"type\":\"list\",\"listType\":\"number\",\"start\":1,\"version\":1},{\"type\":\"codeblock\",\"code\":\"assert log =~ \\\"Template parsing failed\\\"\\n\",\"language\":\"elixir\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Benefits\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"heading\",\"tag\":\"h2\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Using \",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"CaptureLog\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" provides several advantages:\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Clean Test Output\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Error logs don't pollute your test output, making it easier to spot real test failures.\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Explicit Verification\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": You can verify that your error handling is not just returning the right values, but also logging the right information.\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Better Documentation\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": The test clearly shows what log messages are expected when errors occur.\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"ExUnit.CaptureLog\",\"type\":\"text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" is a great tool for testing logging behavior in your Elixir applications. It helps you write more comprehensive tests while keeping your test output clean. Next time you need to test code that generates logs, remember that you can capture and verify those logs as part of your test assertions.\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Today I learned!\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Further Reading\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"heading\",\"tag\":\"h2\",\"version\":1},{\"children\":[{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"ExUnit.CaptureLog documentation\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://hexdocs.pm/ex_unit/ExUnit.CaptureLog.html\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":1,\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Elixir Logger documentation\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://hexdocs.pm/logger/Logger.html\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"value\":2,\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"tag\":\"ul\",\"type\":\"list\",\"listType\":\"bullet\",\"start\":1,\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}", "html": "<p>Testing logging can be tricky but recently I've learned that <PERSON><PERSON><PERSON>'s ExUnit ships with a <code>CaptureLog</code> module that makes it easy to test logging behavior. I was really happy to see this built-in!</p><p>Let's say you have a function that processes a template and logs errors when something goes wrong. You want to test both the error handling and ensure the right messages are being logged. Here's a simplified version of such a function:</p><pre><code class=\"language-elixir\">def render_template(%SourceFile{is_template: true} = source_file, vars) do\n  case Solid.parse(source_file.content) do\n    {:ok, template} -&gt;\n      # Template parsing succeeded, render it...\n\n    {:error, reason} -&gt;\n      Logger.error(\"Template parsing failed\", reason: reason)\n      {:error, :template_parsing_failed}\n  end\nend\n</code></pre><p>Without <code>ExUnit.CaptureLog</code>, testing this would result in error messages cluttering your test output, even though those errors are expected as part of the test.</p><p><code>ExUnit.CaptureLog</code> allows you to:</p><ol><li>Capture log messages during test execution</li><li>Assert against the content of those messages</li><li>Keep your test output clean by suppressing expected error logs</li></ol><p>Here's how I used it:</p><pre><code class=\"language-elixir\">defmodule YourTest do\n  use ExUnit.Case\n\n  import ExUnit.CaptureLog\n\n  test \"handles invalid template syntax\" do\n    file = %SourceFile{\n      content: \"{{ unclosed tag\",\n      is_template: true\n    }\n\n    # Capture logs during template rendering\n    log = capture_log(fn -&gt;\n      assert {:error, \"Template parsing failed: \" &lt;&gt; _} =\n        render_template(file, %{})\n    end)\n\n    # Assert against the captured log content\n    assert log =~ \"Template parsing failed\"\n    assert log =~ \"expected end of string\"\n    assert log =~ \"{{ unclosed tag\"\n  end\nend\n</code></pre><h2 id=\"breaking-down-the-test\">Breaking Down the Test</h2><p>Let's see what's happening:</p><ol><li>First, we import the helper function:</li></ol><pre><code class=\"language-elixir\">import ExUnit.CaptureLog\n</code></pre><ol><li>We wrap the code that generates logs in a <code>capture_log</code> function:</li></ol><pre><code class=\"language-elixir\">log = capture_log(fn -&gt;\n  # Code that generates logs\nend)\n</code></pre><ol><li>The <code>capture_log</code> function:</li></ol><ul><li>Takes a function as an argument</li><li>Executes that function</li><li>Captures any log output during execution</li><li>Returns the captured log as a string</li></ul><ol><li>We can then make assertions about the log content using string matching:</li></ol><pre><code class=\"language-elixir\">assert log =~ \"Template parsing failed\"\n</code></pre><h2 id=\"benefits\">Benefits</h2><p>Using <code>CaptureLog</code> provides several advantages:</p><p><strong>Clean Test Output</strong>: Error logs don't pollute your test output, making it easier to spot real test failures.</p><p><strong>Explicit Verification</strong>: You can verify that your error handling is not just returning the right values, but also logging the right information.</p><p><strong>Better Documentation</strong>: The test clearly shows what log messages are expected when errors occur.</p><p><code>ExUnit.CaptureLog</code> is a great tool for testing logging behavior in your Elixir applications. It helps you write more comprehensive tests while keeping your test output clean. Next time you need to test code that generates logs, remember that you can capture and verify those logs as part of your test assertions.</p><p>Today I learned!</p><h2 id=\"further-reading\">Further Reading</h2><ul><li><a href=\"https://hexdocs.pm/ex_unit/ExUnit.CaptureLog.html\">ExUnit.CaptureLog documentation</a></li><li><a href=\"https://hexdocs.pm/logger/Logger.html\">Elixir Logger documentation</a></li></ul>", "comment_id": "6f14ec5927a78ba03998f736", "plaintext": "Testing logging can be tricky but recently I've learned that <PERSON>xi<PERSON>'s ExUnit ships with a CaptureLog module that makes it easy to test logging behavior. I was really happy to see this built-in!\n\nLet's say you have a function that processes a template and logs errors when something goes wrong. You want to test both the error handling and ensure the right messages are being logged. Here's a simplified version of such a function:\n\ndef render_template(%SourceFile{is_template: true} = source_file, vars) do\n  case Solid.parse(source_file.content) do\n    {:ok, template} ->\n      # Template parsing succeeded, render it...\n\n    {:error, reason} ->\n      Logger.error(\"Template parsing failed\", reason: reason)\n      {:error, :template_parsing_failed}\n  end\nend\n\n\nWithout ExUnit.CaptureLog, testing this would result in error messages cluttering your test output, even though those errors are expected as part of the test.\n\nExUnit.CaptureLog allows you to:\n\n 1. Capture log messages during test execution\n 2. Assert against the content of those messages\n 3. Keep your test output clean by suppressing expected error logs\n\nHere's how I used it:\n\ndefmodule YourTest do\n  use ExUnit.Case\n\n  import ExUnit.CaptureLog\n\n  test \"handles invalid template syntax\" do\n    file = %SourceFile{\n      content: \"{{ unclosed tag\",\n      is_template: true\n    }\n\n    # Capture logs during template rendering\n    log = capture_log(fn ->\n      assert {:error, \"Template parsing failed: \" <> _} =\n        render_template(file, %{})\n    end)\n\n    # Assert against the captured log content\n    assert log =~ \"Template parsing failed\"\n    assert log =~ \"expected end of string\"\n    assert log =~ \"{{ unclosed tag\"\n  end\nend\n\n\n\nBreaking Down the Test\n\nLet's see what's happening:\n\n 1. First, we import the helper function:\n\nimport ExUnit.CaptureLog\n\n\n 1. We wrap the code that generates logs in a capture_log function:\n\nlog = capture_log(fn ->\n  # Code that generates logs\nend)\n\n\n 1. The capture_log function:\n\n * Takes a function as an argument\n * Executes that function\n * Captures any log output during execution\n * Returns the captured log as a string\n\n 1. We can then make assertions about the log content using string matching:\n\nassert log =~ \"Template parsing failed\"\n\n\n\nBenefits\n\nUsing CaptureLog provides several advantages:\n\nClean Test Output: Error logs don't pollute your test output, making it easier to spot real test failures.\n\nExplicit Verification: You can verify that your error handling is not just returning the right values, but also logging the right information.\n\nBetter Documentation: The test clearly shows what log messages are expected when errors occur.\n\nExUnit.CaptureLog is a great tool for testing logging behavior in your Elixir applications. It helps you write more comprehensive tests while keeping your test output clean. Next time you need to test code that generates logs, remember that you can capture and verify those logs as part of your test assertions.\n\nToday I learned!\n\n\nFurther Reading\n\n * ExUnit.CaptureLog documentation\n * Elixir Logger documentation", "feature_image": null, "featured": 0, "type": "post", "status": "published", "locale": null, "visibility": "public", "email_recipient_filter": "all", "created_at": "2025-03-22T08:33:50.000Z", "updated_at": "2025-08-09T05:47:09.000Z", "published_at": "2025-03-22T08:33:50.000Z", "custom_excerpt": null, "codeinjection_head": null, "codeinjection_foot": null, "custom_template": null, "canonical_url": null, "newsletter_id": null, "show_title_and_feature_image": 1}, {"id": "6896e0b842a07b0001ad0349", "uuid": "aa915e6e-71ac-46aa-b8af-91156ea4c69b", "title": "Introducing Drops.Relation: High-Level Relation Abstraction on top of Ecto", "slug": "introducing-drops-relation", "mobiledoc": null, "lexical": "{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"I'm excited to announce the latest addition to the Elixir Drops suite of libraries: \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Drops.Relation\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\". This new library provides a high-level API for defining database relations with automatic schema inference and composable queries, simplifying database interactions when developing applications in Elixir.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Drops.Relation\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" is based on 10 years of my work on the \",\"type\":\"extended-text\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Ruby Object Mapper\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://rom-rb.org\"},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" project and brings the most powerful features of ROM to Elixir.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"What is Drops.Relation?\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Drops.Relation\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" bridges the gap between Ecto and application-level data handling and management. It automatically introspects your database tables, generates Ecto schemas, and provides a convenient query API that feels like working directly with \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Ecto.Repo\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" while adding powerful composition features.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To put it simply\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" it makes you develop applications \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"faster\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" and simplifies maintanance.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Think of it as a smart wrapper around Ecto that eliminates boilerplate while adding sophisticated query composition capabilities, while preserving your full control over your relations and their schemas.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Getting Started\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Add it to your \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"mix.exs\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\":\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"version\":1,\"code\":\"def deps do\\n  [\\n    {:drops_relation, \\\"~> 0.1.0\\\"}\\n  ]\\nend\\n\",\"language\":\"elixir\",\"caption\":\"\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Configure it in your \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"config.exs\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\":\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"version\":1,\"code\":\"config :my_app, :drops,\\n  relation: [\\n    repo: MyApp.Repo\\n  ]\\n\",\"language\":\"elixir\",\"caption\":\"\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Then run the installation task:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"version\":1,\"code\":\"mix drops.relation.install\\n\",\"language\":\"bash\",\"caption\":\"\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"This should create aliases for ecto tasks that will ensure that schemas are refreshed whenever you run migrations.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To test it out, just run migrations:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"version\":1,\"code\":\"mix ecto.migrate\\n\",\"language\":\"\",\"caption\":\"\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"If things go well, you will see this at the end of the output:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"version\":1,\"code\":\"Cache refresh completed\\n\",\"language\":\"\",\"caption\":\"\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"From there, you can start defining your relations with inferred schemas ✨\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Automatic Schema Inference\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"One of \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Drops.Relation\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"'s standout features is automatic schema inference. Instead of manually defining every field, type, and constraint, you can let the library introspect your database:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"version\":1,\"code\":\"defmodule MyApp.Users do\\n  use Drops.Relation, otp_app: :my_app\\n\\n  schema(\\\"users\\\", infer: true)\\nend\\n\",\"language\":\"elixir\",\"caption\":\"\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"The library automatically discovers:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Column names and types\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Primary and foreign keys\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Indexes and constraints\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Default values and nullability\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"You can access the generated schema programmatically:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"version\":1,\"code\":\"schema = MyApp.Users.schema()\\nschema[:email]\\n# %Drops.Relation.Schema.Field{\\n#   name: :email,\\n#   type: :string,\\n#   source: :email,\\n#   meta: %{\\n#     default: nil,\\n#     index: true,\\n#     type: :string,\\n#     primary_key: false,\\n#     foreign_key: false,\\n#     nullable: false\\n#   }\\n# }\\n\",\"language\":\"elixir\",\"caption\":\"\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Familiar Repository API\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Drops.Relation\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" provides all the familiar \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Ecto.Repo\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" functions you're used to but makes them more accessible:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"version\":1,\"code\":\"# Reading data\\nuser = Users.get(1)\\nuser = Users.get_by(email: \\\"<EMAIL>\\\")\\nusers = Users.all()\\nusers = Users.all_by(active: true)\\n\\n# Writing data\\n{:ok, user} = Users.insert(%{name: \\\"John\\\", email: \\\"<EMAIL>\\\"})\\n{:ok, user} = Users.update(user, %{name: \\\"Jane\\\"})\\n{:ok, user} = Users.delete(user)\\n\\n# Aggregations\\ncount = Users.count()\\navg_age = Users.aggregate(:avg, :age)\\n\",\"language\":\"elixir\",\"caption\":\"\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Composable Queries\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Where \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Drops.Relation\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" really shines is in query composition. You can chain query operations together to build complex queries:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"version\":1,\"code\":\"# Basic composition\\nactive_users = Users\\n|> Users.restrict(active: true)\\n|> Users.order(:name)\\n|> Enum.to_list()\\n\\n# Complex restrictions with multiple conditions\\nadmins = Users\\n|> Users.restrict(role: [\\\"admin\\\", \\\"super_admin\\\"])\\n|> Users.restrict(active: true)\\n|> Users.order([{:last_login, :desc}, :name])\\n\\n# Works seamlessly with Enum functions\\nuser_names = Users\\n|> Users.restrict(active: true)\\n|> Enum.map(& &1.name)\\n\",\"language\":\"elixir\",\"caption\":\"\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"In the first release \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Drops.Relation\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" provides 3 high-level query operations:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Drops.Relation.restrict\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" - filters data based on conditions\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Drops.Relation.order\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" - sets ordering\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Drops.Relation.preload\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" - preloads associations\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"More operations will be added in future releases.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Custom Queries with defquery\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"For more complex scenarios, you can define reusable query functions using the \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"defquery\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" macro:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"version\":1,\"code\":\"defmodule MyApp.Users do\\n  use Drops.Relation, otp_app: :my_app\\n\\n  schema(\\\"users\\\", infer: true)\\n\\n  defquery active() do\\n    from(u in relation(), where: u.active == true)\\n  end\\n\\n  defquery by_role(role) when is_binary(role) do\\n    from(u in relation(), where: u.role == ^role)\\n  end\\n\\n  defquery by_role(roles) when is_list(roles) do\\n    from(u in relation(), where: u.role in ^roles)\\n  end\\n\\n  defquery recent(days \\\\\\\\ 7) do\\n    cutoff = DateTime.utc_now() |> DateTime.add(-days, :day)\\n    from(u in relation(), where: u.inserted_at >= ^cutoff)\\n  end\\n\\n  defquery with_posts() do\\n    from(u in relation(),\\n         join: p in assoc(u, :posts),\\n         distinct: u.id)\\n  end\\nend\\n\",\"language\":\"elixir\",\"caption\":\"\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"These custom queries are fully composable with built-in operations:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"version\":1,\"code\":\"# Compose custom queries\\nrecent_admins = Users\\n|> Users.active()\\n|> Users.by_role(\\\"admin\\\")\\n|> Users.recent(30)\\n|> Users.order(:name)\\n|> Enum.to_list()\\n\\n# Mix with restrict operations\\nactive_users_with_email = Users\\n|> Users.active()\\n|> Users.restrict(email: {:not, nil})\\n|> Users.order(:email)\\n\",\"language\":\"elixir\",\"caption\":\"\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Advanced Feature: Boolean Query Logic\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"For complex query logic involving multiple conditions, \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Drops.Relation\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" provides a powerful \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"query\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" macro with boolean operations:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"codeblock\",\"version\":1,\"code\":\"import Drops.Relation.Query\\n\\n# Simple AND operation\\nadult_active_users = Users\\n|> query([u], u.active() and u.adult())\\n|> Enum.to_list()\\n\\n# Complex nested conditions\\ncomplex_query = Users\\n|> query([u],\\n    (u.active() and u.adult()) or\\n    (u.inactive() and u.with_email())\\n  )\\n|> Users.order(:name)\\n|> Enum.to_list()\\n\\n# Mix built-in and custom operations\\nfiltered_users = Users\\n|> query([u],\\n    u.active() and\\n    u.restrict(role: [\\\"admin\\\", \\\"user\\\"])\\n  )\\n|> Enum.to_list()\\n\",\"language\":\"elixir\",\"caption\":\"\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"What's Next?\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Future releases will include:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Enhanced association handling\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Type-safe high-level query handling\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"More composable query operations\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Integration with other Elixir Drops libraries\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Generators for Phoenix\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":5}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Try It!\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Drops.Relation\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" is available on \",\"type\":\"extended-text\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Hex.pm\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://hex.pm/packages/drops_relation\"},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\" and the source code is on \",\"type\":\"extended-text\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"GitHub\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/solnic/drops_relation\"},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\".\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"The library is currently in its early stages, testing and feedback are welcome!\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Give it a try and let me know what you think! I'm always interested in feedback and contributions from the community 💜\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}", "html": "<p>I'm excited to announce the latest addition to the Elixir Drops suite of libraries: <strong>Drops.Relation</strong>. This new library provides a high-level API for defining database relations with automatic schema inference and composable queries, simplifying database interactions when developing applications in Elixir.</p><p><code>Drops.Relation</code> is based on 10 years of my work on the <a href=\"https://rom-rb.org\">Ruby Object Mapper</a> project and brings the most powerful features of ROM to Elixir.</p><h2 id=\"what-is-dropsrelation\">What is Drops.Relation?</h2><p><code>Drops.Relation</code> bridges the gap between Ecto and application-level data handling and management. It automatically introspects your database tables, generates Ecto schemas, and provides a convenient query API that feels like working directly with <code>Ecto.Repo</code> while adding powerful composition features.</p><p><strong>To put it simply</strong> it makes you develop applications <strong>faster</strong> and simplifies maintanance.</p><p>Think of it as a smart wrapper around Ecto that eliminates boilerplate while adding sophisticated query composition capabilities, while preserving your full control over your relations and their schemas.</p><h2 id=\"getting-started\">Getting Started</h2><p>Add it to your <code>mix.exs</code>:</p><pre><code class=\"language-elixir\">def deps do\n  [\n    {:drops_relation, \"~&gt; 0.1.0\"}\n  ]\nend\n</code></pre><p>Configure it in your <code>config.exs</code>:</p><pre><code class=\"language-elixir\">config :my_app, :drops,\n  relation: [\n    repo: MyApp.Repo\n  ]\n</code></pre><p>Then run the installation task:</p><pre><code class=\"language-bash\">mix drops.relation.install\n</code></pre><p>This should create aliases for ecto tasks that will ensure that schemas are refreshed whenever you run migrations.</p><p>To test it out, just run migrations:</p><pre><code>mix ecto.migrate\n</code></pre><p>If things go well, you will see this at the end of the output:</p><pre><code>Cache refresh completed\n</code></pre><p>From there, you can start defining your relations with inferred schemas ✨</p><h2 id=\"automatic-schema-inference\">Automatic Schema Inference</h2><p>One of <code>Drops.Relation</code>'s standout features is automatic schema inference. Instead of manually defining every field, type, and constraint, you can let the library introspect your database:</p><pre><code class=\"language-elixir\">defmodule MyApp.Users do\n  use Drops.Relation, otp_app: :my_app\n\n  schema(\"users\", infer: true)\nend\n</code></pre><p>The library automatically discovers:</p><ul><li>Column names and types</li><li>Primary and foreign keys</li><li>Indexes and constraints</li><li>Default values and nullability</li></ul><p>You can access the generated schema programmatically:</p><pre><code class=\"language-elixir\">schema = MyApp.Users.schema()\nschema[:email]\n# %Drops.Relation.Schema.Field{\n#   name: :email,\n#   type: :string,\n#   source: :email,\n#   meta: %{\n#     default: nil,\n#     index: true,\n#     type: :string,\n#     primary_key: false,\n#     foreign_key: false,\n#     nullable: false\n#   }\n# }\n</code></pre><h2 id=\"familiar-repository-api\">Familiar Repository API</h2><p><code>Drops.Relation</code> provides all the familiar <code>Ecto.Repo</code> functions you're used to but makes them more accessible:</p><pre><code class=\"language-elixir\"># Reading data\nuser = Users.get(1)\nuser = Users.get_by(email: \"<EMAIL>\")\nusers = Users.all()\nusers = Users.all_by(active: true)\n\n# Writing data\n{:ok, user} = Users.insert(%{name: \"John\", email: \"<EMAIL>\"})\n{:ok, user} = Users.update(user, %{name: \"Jane\"})\n{:ok, user} = Users.delete(user)\n\n# Aggregations\ncount = Users.count()\navg_age = Users.aggregate(:avg, :age)\n</code></pre><h2 id=\"composable-queries\">Composable Queries</h2><p>Where <code>Drops.Relation</code> really shines is in query composition. You can chain query operations together to build complex queries:</p><pre><code class=\"language-elixir\"># Basic composition\nactive_users = Users\n|&gt; Users.restrict(active: true)\n|&gt; Users.order(:name)\n|&gt; Enum.to_list()\n\n# Complex restrictions with multiple conditions\nadmins = Users\n|&gt; Users.restrict(role: [\"admin\", \"super_admin\"])\n|&gt; Users.restrict(active: true)\n|&gt; Users.order([{:last_login, :desc}, :name])\n\n# Works seamlessly with Enum functions\nuser_names = Users\n|&gt; Users.restrict(active: true)\n|&gt; Enum.map(&amp; &amp;1.name)\n</code></pre><p>In the first release <code>Drops.Relation</code> provides 3 high-level query operations:</p><ul><li><code>Drops.Relation.restrict</code> - filters data based on conditions</li><li><code>Drops.Relation.order</code> - sets ordering</li><li><code>Drops.Relation.preload</code> - preloads associations</li></ul><p>More operations will be added in future releases.</p><h2 id=\"custom-queries-with-defquery\">Custom Queries with defquery</h2><p>For more complex scenarios, you can define reusable query functions using the <code>defquery</code> macro:</p><pre><code class=\"language-elixir\">defmodule MyApp.Users do\n  use Drops.Relation, otp_app: :my_app\n\n  schema(\"users\", infer: true)\n\n  defquery active() do\n    from(u in relation(), where: u.active == true)\n  end\n\n  defquery by_role(role) when is_binary(role) do\n    from(u in relation(), where: u.role == ^role)\n  end\n\n  defquery by_role(roles) when is_list(roles) do\n    from(u in relation(), where: u.role in ^roles)\n  end\n\n  defquery recent(days \\\\ 7) do\n    cutoff = DateTime.utc_now() |&gt; DateTime.add(-days, :day)\n    from(u in relation(), where: u.inserted_at &gt;= ^cutoff)\n  end\n\n  defquery with_posts() do\n    from(u in relation(),\n         join: p in assoc(u, :posts),\n         distinct: u.id)\n  end\nend\n</code></pre><p>These custom queries are fully composable with built-in operations:</p><pre><code class=\"language-elixir\"># Compose custom queries\nrecent_admins = Users\n|&gt; Users.active()\n|&gt; Users.by_role(\"admin\")\n|&gt; Users.recent(30)\n|&gt; Users.order(:name)\n|&gt; Enum.to_list()\n\n# Mix with restrict operations\nactive_users_with_email = Users\n|&gt; Users.active()\n|&gt; Users.restrict(email: {:not, nil})\n|&gt; Users.order(:email)\n</code></pre><h2 id=\"advanced-feature-boolean-query-logic\">Advanced Feature: Boolean Query Logic</h2><p>For complex query logic involving multiple conditions, <code>Drops.Relation</code> provides a powerful <code>query</code> macro with boolean operations:</p><pre><code class=\"language-elixir\">import Drops.Relation.Query\n\n# Simple AND operation\nadult_active_users = Users\n|&gt; query([u], u.active() and u.adult())\n|&gt; Enum.to_list()\n\n# Complex nested conditions\ncomplex_query = Users\n|&gt; query([u],\n    (u.active() and u.adult()) or\n    (u.inactive() and u.with_email())\n  )\n|&gt; Users.order(:name)\n|&gt; Enum.to_list()\n\n# Mix built-in and custom operations\nfiltered_users = Users\n|&gt; query([u],\n    u.active() and\n    u.restrict(role: [\"admin\", \"user\"])\n  )\n|&gt; Enum.to_list()\n</code></pre><h2 id=\"whats-next\">What's Next?</h2><p>Future releases will include:</p><ul><li>Enhanced association handling</li><li>Type-safe high-level query handling</li><li>More composable query operations</li><li>Integration with other Elixir Drops libraries</li><li>Generators for Phoenix</li></ul><h2 id=\"try-it\">Try It!</h2><p><code>Drops.Relation</code> is available on <a href=\"https://hex.pm/packages/drops_relation\">Hex.pm</a> and the source code is on <a href=\"https://github.com/solnic/drops_relation\">GitHub</a>.</p><p>The library is currently in its early stages, testing and feedback are welcome!</p><p>Give it a try and let me know what you think! I'm always interested in feedback and contributions from the community 💜</p>", "comment_id": "0edbd1b0a7c33a92460006a2", "plaintext": "I'm excited to announce the latest addition to the Elixir Drops suite of libraries: Drops.Relation. This new library provides a high-level API for defining database relations with automatic schema inference and composable queries, simplifying database interactions when developing applications in Elixir.\n\nDrops.Relation is based on 10 years of my work on the Ruby Object Mapper project and brings the most powerful features of ROM to Elixir.\n\n\nWhat is Drops.Relation?\n\nDrops.Relation bridges the gap between Ecto and application-level data handling and management. It automatically introspects your database tables, generates Ecto schemas, and provides a convenient query API that feels like working directly with Ecto.Repo while adding powerful composition features.\n\nTo put it simply it makes you develop applications faster and simplifies maintanance.\n\nThink of it as a smart wrapper around Ecto that eliminates boilerplate while adding sophisticated query composition capabilities, while preserving your full control over your relations and their schemas.\n\n\nGetting Started\n\nAdd it to your mix.exs:\n\ndef deps do\n  [\n    {:drops_relation, \"~> 0.1.0\"}\n  ]\nend\n\n\nConfigure it in your config.exs:\n\nconfig :my_app, :drops,\n  relation: [\n    repo: MyApp.Repo\n  ]\n\n\nThen run the installation task:\n\nmix drops.relation.install\n\n\nThis should create aliases for ecto tasks that will ensure that schemas are refreshed whenever you run migrations.\n\nTo test it out, just run migrations:\n\nmix ecto.migrate\n\n\nIf things go well, you will see this at the end of the output:\n\nCache refresh completed\n\n\nFrom there, you can start defining your relations with inferred schemas ✨\n\n\nAutomatic Schema Inference\n\nOne of Drops.Relation's standout features is automatic schema inference. Instead of manually defining every field, type, and constraint, you can let the library introspect your database:\n\ndefmodule MyApp.Users do\n  use Drops.Relation, otp_app: :my_app\n\n  schema(\"users\", infer: true)\nend\n\n\nThe library automatically discovers:\n\n * Column names and types\n * Primary and foreign keys\n * Indexes and constraints\n * Default values and nullability\n\nYou can access the generated schema programmatically:\n\nschema = MyApp.Users.schema()\nschema[:email]\n# %Drops.Relation.Schema.Field{\n#   name: :email,\n#   type: :string,\n#   source: :email,\n#   meta: %{\n#     default: nil,\n#     index: true,\n#     type: :string,\n#     primary_key: false,\n#     foreign_key: false,\n#     nullable: false\n#   }\n# }\n\n\n\nFamiliar Repository API\n\nDrops.Relation provides all the familiar Ecto.Repo functions you're used to but makes them more accessible:\n\n# Reading data\nuser = Users.get(1)\nuser = Users.get_by(email: \"<EMAIL>\")\nusers = Users.all()\nusers = Users.all_by(active: true)\n\n# Writing data\n{:ok, user} = Users.insert(%{name: \"John\", email: \"<EMAIL>\"})\n{:ok, user} = Users.update(user, %{name: \"Jane\"})\n{:ok, user} = Users.delete(user)\n\n# Aggregations\ncount = Users.count()\navg_age = Users.aggregate(:avg, :age)\n\n\n\nComposable Queries\n\nWhere Drops.Relation really shines is in query composition. You can chain query operations together to build complex queries:\n\n# Basic composition\nactive_users = Users\n|> Users.restrict(active: true)\n|> Users.order(:name)\n|> Enum.to_list()\n\n# Complex restrictions with multiple conditions\nadmins = Users\n|> Users.restrict(role: [\"admin\", \"super_admin\"])\n|> Users.restrict(active: true)\n|> Users.order([{:last_login, :desc}, :name])\n\n# Works seamlessly with Enum functions\nuser_names = Users\n|> Users.restrict(active: true)\n|> Enum.map(& &1.name)\n\n\nIn the first release Drops.Relation provides 3 high-level query operations:\n\n * Drops.Relation.restrict - filters data based on conditions\n * Drops.Relation.order - sets ordering\n * Drops.Relation.preload - preloads associations\n\nMore operations will be added in future releases.\n\n\nCustom Queries with defquery\n\nFor more complex scenarios, you can define reusable query functions using the defquery macro:\n\ndefmodule MyApp.Users do\n  use Drops.Relation, otp_app: :my_app\n\n  schema(\"users\", infer: true)\n\n  defquery active() do\n    from(u in relation(), where: u.active == true)\n  end\n\n  defquery by_role(role) when is_binary(role) do\n    from(u in relation(), where: u.role == ^role)\n  end\n\n  defquery by_role(roles) when is_list(roles) do\n    from(u in relation(), where: u.role in ^roles)\n  end\n\n  defquery recent(days \\\\ 7) do\n    cutoff = DateTime.utc_now() |> DateTime.add(-days, :day)\n    from(u in relation(), where: u.inserted_at >= ^cutoff)\n  end\n\n  defquery with_posts() do\n    from(u in relation(),\n         join: p in assoc(u, :posts),\n         distinct: u.id)\n  end\nend\n\n\nThese custom queries are fully composable with built-in operations:\n\n# Compose custom queries\nrecent_admins = Users\n|> Users.active()\n|> Users.by_role(\"admin\")\n|> Users.recent(30)\n|> Users.order(:name)\n|> Enum.to_list()\n\n# Mix with restrict operations\nactive_users_with_email = Users\n|> Users.active()\n|> Users.restrict(email: {:not, nil})\n|> Users.order(:email)\n\n\n\nAdvanced Feature: Boolean Query Logic\n\nFor complex query logic involving multiple conditions, Drops.Relation provides a powerful query macro with boolean operations:\n\nimport Drops.Relation.Query\n\n# Simple AND operation\nadult_active_users = Users\n|> query([u], u.active() and u.adult())\n|> Enum.to_list()\n\n# Complex nested conditions\ncomplex_query = Users\n|> query([u],\n    (u.active() and u.adult()) or\n    (u.inactive() and u.with_email())\n  )\n|> Users.order(:name)\n|> Enum.to_list()\n\n# Mix built-in and custom operations\nfiltered_users = Users\n|> query([u],\n    u.active() and\n    u.restrict(role: [\"admin\", \"user\"])\n  )\n|> Enum.to_list()\n\n\n\nWhat's Next?\n\nFuture releases will include:\n\n * Enhanced association handling\n * Type-safe high-level query handling\n * More composable query operations\n * Integration with other Elixir Drops libraries\n * Generators for Phoenix\n\n\nTry It!\n\nDrops.Relation is available on Hex.pm and the source code is on GitHub.\n\nThe library is currently in its early stages, testing and feedback are welcome!\n\nGive it a try and let me know what you think! I'm always interested in feedback and contributions from the community 💜", "feature_image": "https://images.unsplash.com/photo-1640158615573-cd28feb1bf4e?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3wxMTc3M3wwfDF8c2VhcmNofDh8fGRhdGF8ZW58MHx8fHwxNzU0NzE4OTMyfDA&ixlib=rb-4.1.0&q=80&w=2000", "featured": 0, "type": "post", "status": "published", "locale": null, "visibility": "public", "email_recipient_filter": "all", "created_at": "2025-07-30T00:00:00.000Z", "updated_at": "2025-08-09T05:55:46.000Z", "published_at": "2025-07-30T00:00:00.000Z", "custom_excerpt": null, "codeinjection_head": null, "codeinjection_foot": null, "custom_template": null, "canonical_url": null, "newsletter_id": null, "show_title_and_feature_image": 1}, {"id": "6896e29942a07b0001ad0378", "uuid": "b738507d-22ae-45db-b587-fae834d1f701", "title": "Introducing Elixir Drops", "slug": "introducing-elixir-drops", "mobiledoc": "{\"version\":\"0.3.1\",\"atoms\":[],\"cards\":[[\"code\",{\"code\":\"defmodule Users.Signup do\\n  use Drops.Contract\\n\\n  schema do\\n    %{\\n      required(:name) => string(:filled?),\\n      optional(:age) => integer()\\n    }\\n  end\\nend\\n\\nUsers.Signup.conform(%{\\n  name: \\\"Jane Doe\\\",\\n  age: 42\\n})\\n# {:ok,\\n#  %{\\n#    name: \\\"Jane Doe\\\",\\n#    age: 42\\n#  }}\\n\\n{:error, errors} = Users.Signup.conform(%{})\\n\\nEnum.map(errors, &to_string/1)\\n# [\\\"name key must be present\\\"]\\n\\n{:error, errors} = Users.Signup.conform(%{\\n  name: \\\"\\\",\\n  age: \\\"42\\\"\\n})\\n\\nEnum.map(errors, &to_string/1)\\n# [\\\"age must be an integer\\\", \\\"name must be filled\\\"]\\n\",\"language\":\"elixir\"}],[\"code\",{\"code\":\"defmodule Users.Signup do\\n  use Drops.Contract\\n\\n  schema do\\n    %{\\n      required(:name) => string(:filled?),\\n      optional(:age) => cast(string(match?: ~r/\\\\d+/)) |> integer()\\n    }\\n  end\\nend\\n\\nUsers.Signup.conform(%{\\n  name: \\\"Jane Doe\\\",\\n  age: \\\"42\\\"\\n})\\n\\n{:error, errors} = Users.Signup.conform(%{\\n  name: \\\"Jane Doe\\\",\\n  age: [\\\"oops\\\"]\\n})\\n\\nEnum.map(errors, &to_string/1)\\n# [\\\"cast error: age must be a string\\\"]\\n\\n{:error, errors} = Users.Signup.conform(%{\\n  name: \\\"Jane Doe\\\",\\n  age: \\\"oops\\\"\\n})\\n\\nEnum.map(errors, &to_string/1)\\n# [\\\"cast error: age must have a valid format\\\"]\\n\",\"language\":\"elixir\"}],[\"code\",{\"code\":\"defmodule Users.Signup do\\n  use Drops.Contract\\n\\n  schema do\\n    %{\\n      required(:name) => string(:filled?),\\n      required(:password) => string(:filled?),\\n      required(:password_confirmation) => string(:filled?)\\n    }\\n  end\\n\\n  rule(:password, %{password: password, password_confirmation: password_confirmation}) do\\n    if password != password_confirmation do\\n      {:error, {[:password_confirmation], \\\"must match password\\\"}}\\n    else\\n      :ok\\n    end\\n  end\\nend\\n\\nUsers.Signup.conform(%{\\n  name: \\\"John\\\",\\n  password: \\\"secret\\\",\\n  password_confirmation: \\\"secret\\\"\\n})\\n# {:ok, %{name: \\\"John\\\", password: \\\"secret\\\", password_confirmation: \\\"secret\\\"}}\\n\\n{:error, errors} = Users.Signup.conform(%{\\n  name: \\\"John\\\",\\n  password: \\\"\\\",\\n  password_confirmation: \\\"secret\\\"\\n})\\n\\nEnum.map(errors, &to_string/1)\\n# [\\\"password must be filled\\\"]\\n\\n{:error, errors} = Users.Signup.conform(%{\\n  name: \\\"John\\\",\\n  password: \\\"foo\\\",\\n  password_confirmation: \\\"bar\\\"\\n})\\n\\nEnum.map(errors, &to_string/1)\\n# [\\\"password_confirmation must match password\\\"]\\n\",\"language\":\"elixir\"}],[\"code\",{\"code\":\"defmodule Users.Signup do\\n  use Drops.Contract\\n\\n  schema(atomize: true) do\\n    %{\\n      required(:name) => string(:filled?),\\n      optional(:age) => integer()\\n    }\\n  end\\nend\\n\\nUsers.Signup.conform(%{\\n  \\\"name\\\" => \\\"Jane Doe\\\",\\n  \\\"age\\\" => 42\\n})\\n# {:ok, %{name: \\\"Jane Doe\\\", age: 42}}\\n\",\"language\":\"elixir\"}]],\"markups\":[[\"a\",[\"href\",\"https://dry-rb.org\"]],[\"strong\"],[\"em\"],[\"code\"],[\"a\",[\"href\",\"https://valued.app\"]],[\"a\",[\"href\",\"https://github.com/solnic/drops\"]],[\"a\",[\"href\",\"https://github.com/users/solnic/projects/2\"]],[\"a\",[\"href\",\"https://github.com/solnic/drops/discussions\"]],[\"a\",[\"href\",\"https://hex.pm/packages/drops/0.1.0\"]],[\"a\",[\"href\",\"https://hexdocs.pm/drops/0.1.0/readme.html\"]]],\"sections\":[[1,\"p\",[[0,[],0,\"A few years ago my Ruby friends asked me if it would be possible to port some of the \"],[0,[0],1,\"dry-rb\"],[0,[],0,\" libraries to Elixir. I remember some early community attempts at porting dry-validation specifically, I did my best to support the effort but back then my Elixir knowledge was too basic and I was too busy with other work.\"]]],[1,\"p\",[[0,[],0,\"Fast forward to today and \"],[0,[1],1,\"I'm happy to announce the very first release of Elixir Drops\"],[0,[],0,\"! 🎉 In this article I'll introduce you to the concepts of the library and show you how you could use it.\"]]],[1,\"h2\",[[0,[],0,\"Contracts with schemas\"]]],[1,\"p\",[[0,[],0,\"One of the core features of Drops is its Contract extension with the Schema DSL. It allows you to define the exact shape of the data that your system relies on and data domain validation rules that are applied to type-safe data that was processed by the schema.\"]]],[1,\"p\",[[0,[],0,\"There are multiple benefits of this approach:\"]]],[1,\"p\",[[0,[],0,\"Casting and validating data using schemas is very precise, producing detailed error messages when things go wrong\"]]],[1,\"p\",[[0,[],0,\"Schemas give you \"],[0,[1],1,\"type safety\"],[0,[],0,\" at the boundaries of your system - you can apply a schema to external input and be 100% sure it's safe to work with\"]]],[1,\"p\",[[0,[],0,\"It's very easy to see the shape of data, making it easier to reason about your system as a whole\"]]],[1,\"p\",[[0,[],0,\"You can restrict and limit larger data structures, reducing them to simpler representations that your system needs\"]]],[1,\"p\",[[0,[],0,\"It's easy to convert \"],[0,[2],1,\"schemas\"],[0,[],0,\" to other representations ie for documentation purposes or export them to JSON Schema format\"]]],[1,\"p\",[[0,[],0,\"Schemas capture both the structure and the types of data, making it easy to reuse them across your codebase\"]]],[1,\"p\",[[0,[],0,\"Your \"],[0,[1],1,\"domain validation rules\"],[0,[],0,\" become simple and focused on their essential logic as they apply to data that meets type requirements enforced by schemas\"]]],[1,\"p\",[[0,[],0,\"This of course sounds very abstract, so here's a simple example of a data contract that defines a schema for a new user:\"]]],[10,0],[1,\"p\",[[0,[],0,\"Let's take a closer look at what we did here:\"]]],[1,\"p\",[[0,[],0,\"The contract defines a schema with two keys:\"]]],[1,\"p\",[[0,[3],1,\"required(:name)\"],[0,[],0,\" means that the input map is expected to have the key \"],[0,[3],1,\":name\"]]],[1,\"p\",[[0,[3],1,\"string(:filled?)\"],[0,[],0,\" means that the \"],[0,[3],1,\":name\"],[0,[],0,\" must be a non-empty string\"]]],[1,\"p\",[[0,[3],1,\"optional(:age)\"],[0,[],0,\" means that the input \"],[0,[2],1,\"could\"],[0,[],0,\" have the key \"],[0,[3],1,\":age\"]]],[1,\"p\",[[0,[3],1,\"integer()\"],[0,[],0,\" means that the \"],[0,[3],1,\":age\"],[0,[],0,\" must be an integer\"]]],[1,\"p\",[[0,[],0,\"Even though this is a very basic example, notice that the library does quite a lot for you - it processes the input map into a new map that includes only the keys that you specified in the schema, it checks both \"],[0,[1],1,\"the keys\"],[0,[],0,\" and \"],[0,[1],1,\"the values\"],[0,[],0,\" according to your specifications. When things go wrong - it gives you nice error messages making it easy to see what went wrong.\"]]],[1,\"h2\",[[0,[],0,\"Type-safe Casting\"]]],[1,\"p\",[[0,[],0,\"One of the unique features of Drops Schemas is type-safe casting. Schemas breaks down the process of casting and validating data into 3 steps:\"]]],[1,\"p\",[[0,[],0,\"Validate \"],[0,[1],1,\"the original input values\"]]],[1,\"p\",[[0,[],0,\"Apply optional casting functions\"]]],[1,\"p\",[[0,[],0,\"Validate \"],[0,[1],1,\"the output values\"]]],[1,\"p\",[[0,[],0,\"It's better to explain this in code though:\"]]],[10,1],[1,\"p\",[[0,[],0,\"Notice that when the age input value cannot be casted according to our specification, we get a nice \\\"cast error\\\" message and we immediately know what went wrong.\"]]],[1,\"h2\",[[0,[],0,\"Domain validation rules\"]]],[1,\"p\",[[0,[],0,\"Contracts allow you to split data validation into schema validation and arbitrary domain validation rules that you can implement. Thanks to this approach we can focus on the essential logic in your rules as we don't have to worry about the types of values that the rules depend on.\"]]],[1,\"p\",[[0,[],0,\"Let me explain this using a simple example:\"]]],[10,2],[1,\"p\",[[0,[],0,\"Here we check whether \"],[0,[3],1,\"password\"],[0,[],0,\" and \"],[0,[3],1,\"password_confirmation\"],[0,[],0,\" match but first we define in our schema that they must be both non-empty strings. Notice that our domain validation rule \"],[0,[1],1,\"is not applied at all\"],[0,[],0,\" if the schema validation does not pass.\"]]],[1,\"p\",[[0,[],0,\"As you can probably imagine, this type of logic could be easily implemented as a higher-level macro, something like \"],[0,[3],1,\"validate_confirmation_of :password\"],[0,[],0,\". This is something that I'll most likely add to Drops in the near future.\"]]],[1,\"h2\",[[0,[],0,\"Safe map atomization\"]]],[1,\"p\",[[0,[],0,\"Another very useful feature is support for atomizing input maps based on your schema definition. This means that the output map will include only the keys that you defined turned into atoms, in the case of string-based maps.\"]]],[1,\"p\",[[0,[],0,\"Here's an example:\"]]],[10,3],[1,\"h2\",[[0,[],0,\"About the first release\"]]],[1,\"p\",[[0,[],0,\"Today I'm releasing v0.1.0 of Drops and even though it's the first release, it already comes with a lot of features! It's already used in our backend system at \"],[0,[4],1,\"valued.app\"],[0,[],0,\", processing and validating millions of JSON payloads regularly.\"]]],[1,\"p\",[[0,[],0,\"It is an early stage of development though and I encourage you to test it out and provide feedback! Here are some useful links where you can learn more:\"]]],[1,\"p\",[[0,[5],1,\"Repo on GitHub\"]]],[1,\"p\",[[0,[6],1,\"Project on GitHub\"]]],[1,\"p\",[[0,[7],1,\"Discussions on GitHub\"]]],[1,\"p\",[[0,[8],1,\"Package on hex.pm\"]]],[1,\"p\",[[0,[9],1,\"Documentation on hexdocs.pm\"]]],[1,\"p\",[[0,[],0,\"Check it out and let me know what you think!\"]]]]}", "lexical": null, "html": "<p>A few years ago my <PERSON> friends asked me if it would be possible to port some of the <a href=\"https://dry-rb.org\">dry-rb</a> libraries to Elixir. I remember some early community attempts at porting dry-validation specifically, I did my best to support the effort but back then my Elixir knowledge was too basic and I was too busy with other work.</p><p>Fast forward to today and <strong>I'm happy to announce the very first release of Elixir Drops</strong>! 🎉 In this article I'll introduce you to the concepts of the library and show you how you could use it.</p><h2 id=\"contracts-with-schemas\">Contracts with schemas</h2><p>One of the core features of Drops is its Contract extension with the Schema DSL. It allows you to define the exact shape of the data that your system relies on and data domain validation rules that are applied to type-safe data that was processed by the schema.</p><p>There are multiple benefits of this approach:</p><p>Casting and validating data using schemas is very precise, producing detailed error messages when things go wrong</p><p>Schemas give you <strong>type safety</strong> at the boundaries of your system - you can apply a schema to external input and be 100% sure it's safe to work with</p><p>It's very easy to see the shape of data, making it easier to reason about your system as a whole</p><p>You can restrict and limit larger data structures, reducing them to simpler representations that your system needs</p><p>It's easy to convert <em>schemas</em> to other representations ie for documentation purposes or export them to JSON Schema format</p><p>Schemas capture both the structure and the types of data, making it easy to reuse them across your codebase</p><p>Your <strong>domain validation rules</strong> become simple and focused on their essential logic as they apply to data that meets type requirements enforced by schemas</p><p>This of course sounds very abstract, so here's a simple example of a data contract that defines a schema for a new user:</p><pre><code class=\"language-elixir\">defmodule Users.Signup do\n  use Drops.Contract\n\n  schema do\n    %{\n      required(:name) =&gt; string(:filled?),\n      optional(:age) =&gt; integer()\n    }\n  end\nend\n\nUsers.Signup.conform(%{\n  name: \"Jane Doe\",\n  age: 42\n})\n# {:ok,\n#  %{\n#    name: \"Jane Doe\",\n#    age: 42\n#  }}\n\n{:error, errors} = Users.Signup.conform(%{})\n\nEnum.map(errors, &amp;to_string/1)\n# [\"name key must be present\"]\n\n{:error, errors} = Users.Signup.conform(%{\n  name: \"\",\n  age: \"42\"\n})\n\nEnum.map(errors, &amp;to_string/1)\n# [\"age must be an integer\", \"name must be filled\"]\n</code></pre><p>Let's take a closer look at what we did here:</p><p>The contract defines a schema with two keys:</p><p><code>required(:name)</code> means that the input map is expected to have the key <code>:name</code></p><p><code>string(:filled?)</code> means that the <code>:name</code> must be a non-empty string</p><p><code>optional(:age)</code> means that the input <em>could</em> have the key <code>:age</code></p><p><code>integer()</code> means that the <code>:age</code> must be an integer</p><p>Even though this is a very basic example, notice that the library does quite a lot for you - it processes the input map into a new map that includes only the keys that you specified in the schema, it checks both <strong>the keys</strong> and <strong>the values</strong> according to your specifications. When things go wrong - it gives you nice error messages making it easy to see what went wrong.</p><h2 id=\"type-safe-casting\">Type-safe Casting</h2><p>One of the unique features of Drops Schemas is type-safe casting. Schemas breaks down the process of casting and validating data into 3 steps:</p><p>Validate <strong>the original input values</strong></p><p>Apply optional casting functions</p><p>Validate <strong>the output values</strong></p><p>It's better to explain this in code though:</p><pre><code class=\"language-elixir\">defmodule Users.Signup do\n  use Drops.Contract\n\n  schema do\n    %{\n      required(:name) =&gt; string(:filled?),\n      optional(:age) =&gt; cast(string(match?: ~r/\\d+/)) |&gt; integer()\n    }\n  end\nend\n\nUsers.Signup.conform(%{\n  name: \"Jane Doe\",\n  age: \"42\"\n})\n\n{:error, errors} = Users.Signup.conform(%{\n  name: \"Jane Doe\",\n  age: [\"oops\"]\n})\n\nEnum.map(errors, &amp;to_string/1)\n# [\"cast error: age must be a string\"]\n\n{:error, errors} = Users.Signup.conform(%{\n  name: \"Jane Doe\",\n  age: \"oops\"\n})\n\nEnum.map(errors, &amp;to_string/1)\n# [\"cast error: age must have a valid format\"]\n</code></pre><p>Notice that when the age input value cannot be casted according to our specification, we get a nice \"cast error\" message and we immediately know what went wrong.</p><h2 id=\"domain-validation-rules\">Domain validation rules</h2><p>Contracts allow you to split data validation into schema validation and arbitrary domain validation rules that you can implement. Thanks to this approach we can focus on the essential logic in your rules as we don't have to worry about the types of values that the rules depend on.</p><p>Let me explain this using a simple example:</p><pre><code class=\"language-elixir\">defmodule Users.Signup do\n  use Drops.Contract\n\n  schema do\n    %{\n      required(:name) =&gt; string(:filled?),\n      required(:password) =&gt; string(:filled?),\n      required(:password_confirmation) =&gt; string(:filled?)\n    }\n  end\n\n  rule(:password, %{password: password, password_confirmation: password_confirmation}) do\n    if password != password_confirmation do\n      {:error, {[:password_confirmation], \"must match password\"}}\n    else\n      :ok\n    end\n  end\nend\n\nUsers.Signup.conform(%{\n  name: \"John\",\n  password: \"secret\",\n  password_confirmation: \"secret\"\n})\n# {:ok, %{name: \"John\", password: \"secret\", password_confirmation: \"secret\"}}\n\n{:error, errors} = Users.Signup.conform(%{\n  name: \"John\",\n  password: \"\",\n  password_confirmation: \"secret\"\n})\n\nEnum.map(errors, &amp;to_string/1)\n# [\"password must be filled\"]\n\n{:error, errors} = Users.Signup.conform(%{\n  name: \"John\",\n  password: \"foo\",\n  password_confirmation: \"bar\"\n})\n\nEnum.map(errors, &amp;to_string/1)\n# [\"password_confirmation must match password\"]\n</code></pre><p>Here we check whether <code>password</code> and <code>password_confirmation</code> match but first we define in our schema that they must be both non-empty strings. Notice that our domain validation rule <strong>is not applied at all</strong> if the schema validation does not pass.</p><p>As you can probably imagine, this type of logic could be easily implemented as a higher-level macro, something like <code>validate_confirmation_of :password</code>. This is something that I'll most likely add to Drops in the near future.</p><h2 id=\"safe-map-atomization\">Safe map atomization</h2><p>Another very useful feature is support for atomizing input maps based on your schema definition. This means that the output map will include only the keys that you defined turned into atoms, in the case of string-based maps.</p><p>Here's an example:</p><pre><code class=\"language-elixir\">defmodule Users.Signup do\n  use Drops.Contract\n\n  schema(atomize: true) do\n    %{\n      required(:name) =&gt; string(:filled?),\n      optional(:age) =&gt; integer()\n    }\n  end\nend\n\nUsers.Signup.conform(%{\n  \"name\" =&gt; \"Jane Doe\",\n  \"age\" =&gt; 42\n})\n# {:ok, %{name: \"Jane Doe\", age: 42}}\n</code></pre><h2 id=\"about-the-first-release\">About the first release</h2><p>Today I'm releasing v0.1.0 of Drops and even though it's the first release, it already comes with a lot of features! It's already used in our backend system at <a href=\"https://valued.app\">valued.app</a>, processing and validating millions of JSON payloads regularly.</p><p>It is an early stage of development though and I encourage you to test it out and provide feedback! Here are some useful links where you can learn more:</p><p><a href=\"https://github.com/solnic/drops\">Repo on GitHub</a></p><p><a href=\"https://github.com/users/solnic/projects/2\">Project on GitHub</a></p><p><a href=\"https://github.com/solnic/drops/discussions\">Discussions on GitHub</a></p><p><a href=\"https://hex.pm/packages/drops/0.1.0\">Package on hex.pm</a></p><p><a href=\"https://hexdocs.pm/drops/0.1.0/readme.html\">Documentation on hexdocs.pm</a></p><p>Check it out and let me know what you think!</p>", "comment_id": "99c3b1c0fb05dafbb43df44b", "plaintext": "A few years ago my <PERSON> friends asked me if it would be possible to port some of the dry-rb libraries to Elixir. I remember some early community attempts at porting dry-validation specifically, I did my best to support the effort but back then my Elixir knowledge was too basic and I was too busy with other work.\n\nFast forward to today and I'm happy to announce the very first release of Elixir Drops! 🎉 In this article I'll introduce you to the concepts of the library and show you how you could use it.\n\n\nContracts with schemas\n\nOne of the core features of Drops is its Contract extension with the Schema DSL. It allows you to define the exact shape of the data that your system relies on and data domain validation rules that are applied to type-safe data that was processed by the schema.\n\nThere are multiple benefits of this approach:\n\nCasting and validating data using schemas is very precise, producing detailed error messages when things go wrong\n\nSchemas give you type safety at the boundaries of your system - you can apply a schema to external input and be 100% sure it's safe to work with\n\nIt's very easy to see the shape of data, making it easier to reason about your system as a whole\n\nYou can restrict and limit larger data structures, reducing them to simpler representations that your system needs\n\nIt's easy to convert schemas to other representations ie for documentation purposes or export them to JSON Schema format\n\nSchemas capture both the structure and the types of data, making it easy to reuse them across your codebase\n\nYour domain validation rules become simple and focused on their essential logic as they apply to data that meets type requirements enforced by schemas\n\nThis of course sounds very abstract, so here's a simple example of a data contract that defines a schema for a new user:\n\ndefmodule Users.Signup do\n  use Drops.Contract\n\n  schema do\n    %{\n      required(:name) => string(:filled?),\n      optional(:age) => integer()\n    }\n  end\nend\n\nUsers.Signup.conform(%{\n  name: \"Jane Doe\",\n  age: 42\n})\n# {:ok,\n#  %{\n#    name: \"Jane Doe\",\n#    age: 42\n#  }}\n\n{:error, errors} = Users.Signup.conform(%{})\n\nEnum.map(errors, &to_string/1)\n# [\"name key must be present\"]\n\n{:error, errors} = Users.Signup.conform(%{\n  name: \"\",\n  age: \"42\"\n})\n\nEnum.map(errors, &to_string/1)\n# [\"age must be an integer\", \"name must be filled\"]\n\n\nLet's take a closer look at what we did here:\n\nThe contract defines a schema with two keys:\n\nrequired(:name) means that the input map is expected to have the key :name\n\nstring(:filled?) means that the :name must be a non-empty string\n\noptional(:age) means that the input could have the key :age\n\ninteger() means that the :age must be an integer\n\nEven though this is a very basic example, notice that the library does quite a lot for you - it processes the input map into a new map that includes only the keys that you specified in the schema, it checks both the keys and the values according to your specifications. When things go wrong - it gives you nice error messages making it easy to see what went wrong.\n\n\nType-safe Casting\n\nOne of the unique features of Drops Schemas is type-safe casting. Schemas breaks down the process of casting and validating data into 3 steps:\n\nValidate the original input values\n\nApply optional casting functions\n\nValidate the output values\n\nIt's better to explain this in code though:\n\ndefmodule Users.Signup do\n  use Drops.Contract\n\n  schema do\n    %{\n      required(:name) => string(:filled?),\n      optional(:age) => cast(string(match?: ~r/\\d+/)) |> integer()\n    }\n  end\nend\n\nUsers.Signup.conform(%{\n  name: \"Jane Doe\",\n  age: \"42\"\n})\n\n{:error, errors} = Users.Signup.conform(%{\n  name: \"Jane Doe\",\n  age: [\"oops\"]\n})\n\nEnum.map(errors, &to_string/1)\n# [\"cast error: age must be a string\"]\n\n{:error, errors} = Users.Signup.conform(%{\n  name: \"Jane Doe\",\n  age: \"oops\"\n})\n\nEnum.map(errors, &to_string/1)\n# [\"cast error: age must have a valid format\"]\n\n\nNotice that when the age input value cannot be casted according to our specification, we get a nice \"cast error\" message and we immediately know what went wrong.\n\n\nDomain validation rules\n\nContracts allow you to split data validation into schema validation and arbitrary domain validation rules that you can implement. Thanks to this approach we can focus on the essential logic in your rules as we don't have to worry about the types of values that the rules depend on.\n\nLet me explain this using a simple example:\n\ndefmodule Users.Signup do\n  use Drops.Contract\n\n  schema do\n    %{\n      required(:name) => string(:filled?),\n      required(:password) => string(:filled?),\n      required(:password_confirmation) => string(:filled?)\n    }\n  end\n\n  rule(:password, %{password: password, password_confirmation: password_confirmation}) do\n    if password != password_confirmation do\n      {:error, {[:password_confirmation], \"must match password\"}}\n    else\n      :ok\n    end\n  end\nend\n\nUsers.Signup.conform(%{\n  name: \"John\",\n  password: \"secret\",\n  password_confirmation: \"secret\"\n})\n# {:ok, %{name: \"John\", password: \"secret\", password_confirmation: \"secret\"}}\n\n{:error, errors} = Users.Signup.conform(%{\n  name: \"John\",\n  password: \"\",\n  password_confirmation: \"secret\"\n})\n\nEnum.map(errors, &to_string/1)\n# [\"password must be filled\"]\n\n{:error, errors} = Users.Signup.conform(%{\n  name: \"John\",\n  password: \"foo\",\n  password_confirmation: \"bar\"\n})\n\nEnum.map(errors, &to_string/1)\n# [\"password_confirmation must match password\"]\n\n\nHere we check whether password and password_confirmation match but first we define in our schema that they must be both non-empty strings. Notice that our domain validation rule is not applied at all if the schema validation does not pass.\n\nAs you can probably imagine, this type of logic could be easily implemented as a higher-level macro, something like validate_confirmation_of :password. This is something that I'll most likely add to Drops in the near future.\n\n\nSafe map atomization\n\nAnother very useful feature is support for atomizing input maps based on your schema definition. This means that the output map will include only the keys that you defined turned into atoms, in the case of string-based maps.\n\nHere's an example:\n\ndefmodule Users.Signup do\n  use Drops.Contract\n\n  schema(atomize: true) do\n    %{\n      required(:name) => string(:filled?),\n      optional(:age) => integer()\n    }\n  end\nend\n\nUsers.Signup.conform(%{\n  \"name\" => \"Jane Doe\",\n  \"age\" => 42\n})\n# {:ok, %{name: \"Jane Doe\", age: 42}}\n\n\n\nAbout the first release\n\nToday I'm releasing v0.1.0 of Drops and even though it's the first release, it already comes with a lot of features! It's already used in our backend system at valued.app, processing and validating millions of JSON payloads regularly.\n\nIt is an early stage of development though and I encourage you to test it out and provide feedback! Here are some useful links where you can learn more:\n\nRepo on GitHub\n\nProject on GitHub\n\nDiscussions on GitHub\n\nPackage on hex.pm\n\nDocumentation on hexdocs.pm\n\nCheck it out and let me know what you think!", "feature_image": null, "featured": 0, "type": "post", "status": "published", "locale": null, "visibility": "public", "email_recipient_filter": "all", "created_at": "2023-10-25T00:00:00.000Z", "updated_at": "2023-10-25T00:00:00.000Z", "published_at": "2023-10-25T00:00:00.000Z", "custom_excerpt": null, "codeinjection_head": null, "codeinjection_foot": null, "custom_template": null, "canonical_url": null, "newsletter_id": null, "show_title_and_feature_image": 1}, {"id": "6896e29942a07b0001ad0379", "uuid": "a7c20d98-5849-49c9-b046-0ede4131b69b", "title": "Elixir Drops 0.2.0 with support for custom types was released!", "slug": "elixir-drops-020-with-support-for-custom-types-was-released", "mobiledoc": "{\"version\":\"0.3.1\",\"atoms\":[],\"cards\":[[\"code\",{\"code\":\"defmodule Types.Age do\\n  use Drops.Type, integer(gteq?: 0)\\nend\\n\\ndefmodule Types.Name do\\n  use Drops.Type, string(:filled?)\\nend\\n\\ndefmodule UserContract do\\n  use Drops.Contract\\n\\n  schema do\\n    %{\\n      required(:name) => Types.Name,\\n      required(:age) => Types.Age\\n    }\\n  end\\nend\\n\\nUserContract.conform(%{name: \\\"<PERSON>\\\", age: 42})\\n# {:ok, %{name: \\\"<PERSON>\\\", age: 42}}\\n\\n{:error, errors} = UserContract.conform(%{name: \\\"Jane\\\", age: -42})\\nEnum.map(errors, &to_string/1)\\n# [\\\"age must be greater than or equal to 0\\\"]\\n\\n{:error, errors} = UserContract.conform(%{name: \\\"<PERSON>\\\", age: \\\"42\\\"})\\nEnum.map(errors, &to_string/1)\\n# [\\\"age must be an integer\\\"]\\n\",\"language\":\"elixir\"}],[\"code\",{\"code\":\"defmodule Types.User do\\n  use Drops.Type, %{\\n    required(:name) => string(:filled?),\\n    required(:age) => integer(gteq?: 0)\\n  }\\nend\\n\\ndefmodule UserContract do\\n  use Drops.Contract\\n\\n  schema do\\n    %{\\n      required(:user) => Types.User\\n    }\\n  end\\nend\\n\\nUserContract.conform(%{user: %{name: \\\"Jane\\\", age: 42}})\\n# {:ok, %{user: %{name: \\\"Jane\\\", age: 42}}}\\n\\n{:error, errors} = UserContract.conform(\\n  %{user: %{name: \\\"Jane\\\", age: -42}}\\n)\\nEnum.map(errors, &to_string/1)\\n# [\\\"user.age must be greater than or equal to 0\\\"]\\n\\n{:error, errors} = UserContract.conform(\\n  %{user: %{name: \\\"Jane\\\", age: \\\"42\\\"}}\\n)\\nEnum.map(errors, &to_string/1)\\n# [\\\"user.age must be an integer\\\"]\\n\",\"language\":\"elixir\"}],[\"code\",{\"code\":\"defmodule Types.Price do\\n  use Drops.Type, union([:integer, :float], gt?: 0)\\nend\\n\\ndefmodule ProductContract do\\n  use Drops.Contract\\n\\n  schema do\\n    %{\\n      required(:price) => Types.Price\\n    }\\n  end\\nend\\n\\nProductContract.conform(%{price: 42})\\n# {:ok, %{price: 42}}\\n\\nProductContract.conform(%{price: 42.3})\\n# {:ok, %{price: 42.3}}\\n\\n{:error, errors} = ProductContract.conform(%{price: -42})\\nEnum.map(errors, &to_string/1)\\n# [\\\"price must be greater than 0\\\"]\\n\\n{:error, errors} = ProductContract.conform(%{price: \\\"42\\\"})\\nEnum.map(errors, &to_string/1)\\n# [\\\"price must be an integer or price must be a float\\\"]\\n\",\"language\":\"elixir\"}]],\"markups\":[[\"strong\"],[\"code\"],[\"a\",[\"href\",\"https://github.com/solnic/drops/blob/main/CHANGELOG.md#v020---2024-02-01\"]],[\"a\",[\"href\",\"https://github.com/solnic/drops/issues\"]]],\"sections\":[[1,\"p\",[[0,[],0,\"Since the previous release in October, my intermittent efforts have been dedicated to developing version 0.2.0 of Elixir Drops, concentrating primarily on enhancing its type system. The main objective has been to introduce the functionality for users to create custom types and to enable their composition.\"]]],[1,\"p\",[[0,[],0,\"I'm thrilled to share that today I've completed the work and released \"],[0,[0],1,\"Elixir Drops version 0.2.0\"],[0,[],0,\"! Let's explore the new capabilities it brings.\"]]],[1,\"h2\",[[0,[],0,\"Defining custom types\"]]],[1,\"p\",[[0,[],0,\"You can encapsulate validation logic in simple custom primitive types. Let's say you want a user with \"],[0,[1],1,\"age\"],[0,[],0,\" and \"],[0,[1],1,\"name\"],[0,[],0,\" attributes, here's how you could define it:\"]]],[10,0],[1,\"h2\",[[0,[],0,\"Custom maps\"]]],[1,\"p\",[[0,[],0,\"Apart from defining custom primitive types, you can also define complex maps and reuse them easily. This is \"],[0,[0],1,\"very handy\"],[0,[],0,\" as it can streamline schema definitions significantly.\"]]],[1,\"p\",[[0,[],0,\"Here's how we could define \"],[0,[1],1,\"Types.User\"],[0,[],0,\" which is defined as a custom map, and then reuse it inside a \"],[0,[1],1,\"UserContract\"],[0,[],0,\" under \"],[0,[1],1,\":user\"],[0,[],0,\" key:\"]]],[10,1],[1,\"h2\",[[0,[],0,\"Custom union types\"]]],[1,\"p\",[[0,[],0,\"You can now create your own union types, and this feature is also utilized by the newly introduced \"],[0,[1],1,\"Drops.Types.Number\"],[0,[],0,\". Consider a scenario where you require a price value that could be an integer or a floating-point number, with the common condition that it has to be more than 0:\"]]],[10,2],[1,\"h2\",[[0,[],0,\"Elixir is 💜\"]]],[1,\"p\",[[0,[],0,\"I must admit, I am consistently impressed by Elixir and its (relatively!) straightforward approach to building even complex library code. Often, when I want to implement something more advanced, my gut feeling tells me it will be a challenge, but it usually turns out to be quite manageable. I also thoroughly enjoy the experience of refactoring things freely and then seeing how everything continues to work as expected. It's a truly amazing feeling!\"]]],[1,\"p\",[[0,[],0,\"When I started working on Drops last year, I wasn't entirely sure what to expect. I had about 1.5 years of Elixir experience, but my work mostly focused on application code. I had to learn how to build a library, how to write and organize tests for it, how to use more advanced macros or callbacks, and all of that was a very smooth ride.\"]]],[1,\"p\",[[0,[],0,\"This topic warrants an article of its own, which I hope to write someday. For now, I'm simply thrilled to continue working on Drops 🙂\"]]],[1,\"h2\",[[0,[],0,\"Greater things are yet to come 🤓\"]]],[1,\"p\",[[0,[],0,\"The new type system is a big stepping stone towards more great features that are scheduled for 0.3.0:\"]]],[1,\"p\",[[0,[],0,\"I18n support with customized error messages\"]]],[1,\"p\",[[0,[],0,\"A better casting implementation based on a common protocol (very similar to how the type system works)\"]]],[1,\"p\",[[0,[],0,\"More built-in casters!\"]]],[1,\"p\",[[0,[],0,\"For more details about the current v0.2.0 please refer to \"],[0,[2],1,\"CHANGELOG.md\"],[0,[],0,\".\"]]],[1,\"p\",[[0,[],0,\"I encourage you to try out Drops and see if it's useful to you. Feel free to provide any feedback and if you encounter any issues, please report them at \"],[0,[3],1,\"GitHub\"],[0,[],0,\".\"]]],[1,\"p\",[[0,[],0,\"Add \"],[0,[1],1,\"{:drops, \\\"~> 0.2\\\"}\"],[0,[],0,\" to your mix.exs and have fun!\"]]]]}", "lexical": null, "html": "<p>Since the previous release in October, my intermittent efforts have been dedicated to developing version 0.2.0 of Elixir Drops, concentrating primarily on enhancing its type system. The main objective has been to introduce the functionality for users to create custom types and to enable their composition.</p><p>I'm thrilled to share that today I've completed the work and released <strong>Elixir Drops version 0.2.0</strong>! Let's explore the new capabilities it brings.</p><h2 id=\"defining-custom-types\">Defining custom types</h2><p>You can encapsulate validation logic in simple custom primitive types. Let's say you want a user with <code>age</code> and <code>name</code> attributes, here's how you could define it:</p><pre><code class=\"language-elixir\">defmodule Types.Age do\n  use Drops.Type, integer(gteq?: 0)\nend\n\ndefmodule Types.Name do\n  use Drops.Type, string(:filled?)\nend\n\ndefmodule UserContract do\n  use Drops.Contract\n\n  schema do\n    %{\n      required(:name) =&gt; Types.Name,\n      required(:age) =&gt; Types.Age\n    }\n  end\nend\n\nUserContract.conform(%{name: \"<PERSON>\", age: 42})\n# {:ok, %{name: \"<PERSON>\", age: 42}}\n\n{:error, errors} = UserContract.conform(%{name: \"<PERSON>\", age: -42})\nEnum.map(errors, &amp;to_string/1)\n# [\"age must be greater than or equal to 0\"]\n\n{:error, errors} = UserContract.conform(%{name: \"Jane\", age: \"42\"})\nEnum.map(errors, &amp;to_string/1)\n# [\"age must be an integer\"]\n</code></pre><h2 id=\"custom-maps\">Custom maps</h2><p>Apart from defining custom primitive types, you can also define complex maps and reuse them easily. This is <strong>very handy</strong> as it can streamline schema definitions significantly.</p><p>Here's how we could define <code>Types.User</code> which is defined as a custom map, and then reuse it inside a <code>UserContract</code> under <code>:user</code> key:</p><pre><code class=\"language-elixir\">defmodule Types.User do\n  use Drops.Type, %{\n    required(:name) =&gt; string(:filled?),\n    required(:age) =&gt; integer(gteq?: 0)\n  }\nend\n\ndefmodule UserContract do\n  use Drops.Contract\n\n  schema do\n    %{\n      required(:user) =&gt; Types.User\n    }\n  end\nend\n\nUserContract.conform(%{user: %{name: \"Jane\", age: 42}})\n# {:ok, %{user: %{name: \"Jane\", age: 42}}}\n\n{:error, errors} = UserContract.conform(\n  %{user: %{name: \"Jane\", age: -42}}\n)\nEnum.map(errors, &amp;to_string/1)\n# [\"user.age must be greater than or equal to 0\"]\n\n{:error, errors} = UserContract.conform(\n  %{user: %{name: \"Jane\", age: \"42\"}}\n)\nEnum.map(errors, &amp;to_string/1)\n# [\"user.age must be an integer\"]\n</code></pre><h2 id=\"custom-union-types\">Custom union types</h2><p>You can now create your own union types, and this feature is also utilized by the newly introduced <code>Drops.Types.Number</code>. Consider a scenario where you require a price value that could be an integer or a floating-point number, with the common condition that it has to be more than 0:</p><pre><code class=\"language-elixir\">defmodule Types.Price do\n  use Drops.Type, union([:integer, :float], gt?: 0)\nend\n\ndefmodule ProductContract do\n  use Drops.Contract\n\n  schema do\n    %{\n      required(:price) =&gt; Types.Price\n    }\n  end\nend\n\nProductContract.conform(%{price: 42})\n# {:ok, %{price: 42}}\n\nProductContract.conform(%{price: 42.3})\n# {:ok, %{price: 42.3}}\n\n{:error, errors} = ProductContract.conform(%{price: -42})\nEnum.map(errors, &amp;to_string/1)\n# [\"price must be greater than 0\"]\n\n{:error, errors} = ProductContract.conform(%{price: \"42\"})\nEnum.map(errors, &amp;to_string/1)\n# [\"price must be an integer or price must be a float\"]\n</code></pre><h2 id=\"elixir-is-%F0%9F%92%9C\">Elixir is 💜</h2><p>I must admit, I am consistently impressed by Elixir and its (relatively!) straightforward approach to building even complex library code. Often, when I want to implement something more advanced, my gut feeling tells me it will be a challenge, but it usually turns out to be quite manageable. I also thoroughly enjoy the experience of refactoring things freely and then seeing how everything continues to work as expected. It's a truly amazing feeling!</p><p>When I started working on Drops last year, I wasn't entirely sure what to expect. I had about 1.5 years of Elixir experience, but my work mostly focused on application code. I had to learn how to build a library, how to write and organize tests for it, how to use more advanced macros or callbacks, and all of that was a very smooth ride.</p><p>This topic warrants an article of its own, which I hope to write someday. For now, I'm simply thrilled to continue working on Drops 🙂</p><h2 id=\"greater-things-are-yet-to-come-%F0%9F%A4%93\">Greater things are yet to come 🤓</h2><p>The new type system is a big stepping stone towards more great features that are scheduled for 0.3.0:</p><p>I18n support with customized error messages</p><p>A better casting implementation based on a common protocol (very similar to how the type system works)</p><p>More built-in casters!</p><p>For more details about the current v0.2.0 please refer to <a href=\"https://github.com/solnic/drops/blob/main/CHANGELOG.md#v020---2024-02-01\">CHANGELOG.md</a>.</p><p>I encourage you to try out Drops and see if it's useful to you. Feel free to provide any feedback and if you encounter any issues, please report them at <a href=\"https://github.com/solnic/drops/issues\">GitHub</a>.</p><p>Add <code>{:drops, \"~&gt; 0.2\"}</code> to your mix.exs and have fun!</p>", "comment_id": "2c01277096a276a9bc804e99", "plaintext": "Since the previous release in October, my intermittent efforts have been dedicated to developing version 0.2.0 of Elixir Drops, concentrating primarily on enhancing its type system. The main objective has been to introduce the functionality for users to create custom types and to enable their composition.\n\nI'm thrilled to share that today I've completed the work and released Elixir Drops version 0.2.0! Let's explore the new capabilities it brings.\n\n\nDefining custom types\n\nYou can encapsulate validation logic in simple custom primitive types. Let's say you want a user with age and name attributes, here's how you could define it:\n\ndefmodule Types.Age do\n  use Drops.Type, integer(gteq?: 0)\nend\n\ndefmodule Types.Name do\n  use Drops.Type, string(:filled?)\nend\n\ndefmodule UserContract do\n  use Drops.Contract\n\n  schema do\n    %{\n      required(:name) => Types.Name,\n      required(:age) => Types.Age\n    }\n  end\nend\n\nUserContract.conform(%{name: \"<PERSON>\", age: 42})\n# {:ok, %{name: \"<PERSON>\", age: 42}}\n\n{:error, errors} = UserContract.conform(%{name: \"<PERSON>\", age: -42})\nEnum.map(errors, &to_string/1)\n# [\"age must be greater than or equal to 0\"]\n\n{:error, errors} = UserContract.conform(%{name: \"<PERSON>\", age: \"42\"})\nEnum.map(errors, &to_string/1)\n# [\"age must be an integer\"]\n\n\n\nCustom maps\n\nApart from defining custom primitive types, you can also define complex maps and reuse them easily. This is very handy as it can streamline schema definitions significantly.\n\nHere's how we could define Types.User which is defined as a custom map, and then reuse it inside a UserContract under :user key:\n\ndefmodule Types.User do\n  use Drops.Type, %{\n    required(:name) => string(:filled?),\n    required(:age) => integer(gteq?: 0)\n  }\nend\n\ndefmodule UserContract do\n  use Drops.Contract\n\n  schema do\n    %{\n      required(:user) => Types.User\n    }\n  end\nend\n\nUserContract.conform(%{user: %{name: \"Jane\", age: 42}})\n# {:ok, %{user: %{name: \"Jane\", age: 42}}}\n\n{:error, errors} = UserContract.conform(\n  %{user: %{name: \"Jane\", age: -42}}\n)\nEnum.map(errors, &to_string/1)\n# [\"user.age must be greater than or equal to 0\"]\n\n{:error, errors} = UserContract.conform(\n  %{user: %{name: \"Jane\", age: \"42\"}}\n)\nEnum.map(errors, &to_string/1)\n# [\"user.age must be an integer\"]\n\n\n\nCustom union types\n\nYou can now create your own union types, and this feature is also utilized by the newly introduced Drops.Types.Number. Consider a scenario where you require a price value that could be an integer or a floating-point number, with the common condition that it has to be more than 0:\n\ndefmodule Types.Price do\n  use Drops.Type, union([:integer, :float], gt?: 0)\nend\n\ndefmodule ProductContract do\n  use Drops.Contract\n\n  schema do\n    %{\n      required(:price) => Types.Price\n    }\n  end\nend\n\nProductContract.conform(%{price: 42})\n# {:ok, %{price: 42}}\n\nProductContract.conform(%{price: 42.3})\n# {:ok, %{price: 42.3}}\n\n{:error, errors} = ProductContract.conform(%{price: -42})\nEnum.map(errors, &to_string/1)\n# [\"price must be greater than 0\"]\n\n{:error, errors} = ProductContract.conform(%{price: \"42\"})\nEnum.map(errors, &to_string/1)\n# [\"price must be an integer or price must be a float\"]\n\n\n\nElixir is 💜\n\nI must admit, I am consistently impressed by Elixir and its (relatively!) straightforward approach to building even complex library code. Often, when I want to implement something more advanced, my gut feeling tells me it will be a challenge, but it usually turns out to be quite manageable. I also thoroughly enjoy the experience of refactoring things freely and then seeing how everything continues to work as expected. It's a truly amazing feeling!\n\nWhen I started working on Drops last year, I wasn't entirely sure what to expect. I had about 1.5 years of Elixir experience, but my work mostly focused on application code. I had to learn how to build a library, how to write and organize tests for it, how to use more advanced macros or callbacks, and all of that was a very smooth ride.\n\nThis topic warrants an article of its own, which I hope to write someday. For now, I'm simply thrilled to continue working on Drops 🙂\n\n\nGreater things are yet to come 🤓\n\nThe new type system is a big stepping stone towards more great features that are scheduled for 0.3.0:\n\nI18n support with customized error messages\n\nA better casting implementation based on a common protocol (very similar to how the type system works)\n\nMore built-in casters!\n\nFor more details about the current v0.2.0 please refer to CHANGELOG.md.\n\nI encourage you to try out Drops and see if it's useful to you. Feel free to provide any feedback and if you encounter any issues, please report them at GitHub.\n\nAdd {:drops, \"~> 0.2\"} to your mix.exs and have fun!", "feature_image": null, "featured": 0, "type": "post", "status": "published", "locale": null, "visibility": "public", "email_recipient_filter": "all", "created_at": "2024-02-01T00:00:00.000Z", "updated_at": "2024-02-01T00:00:00.000Z", "published_at": "2024-02-01T00:00:00.000Z", "custom_excerpt": null, "codeinjection_head": null, "codeinjection_foot": null, "custom_template": null, "canonical_url": null, "newsletter_id": null, "show_title_and_feature_image": 1}, {"id": "6896e29942a07b0001ad037a", "uuid": "4078888f-841d-4bf6-87a5-1aae96f6cde0", "title": "2024 Status update", "slug": "2024-status-update", "mobiledoc": "{\"version\":\"0.3.1\",\"atoms\":[],\"cards\":[],\"markups\":[[\"a\",[\"href\",\"http://valued.app\"]],[\"strong\"],[\"code\"],[\"a\",[\"href\",\"https://solnic.dev/introducing-elixir-drops\"]],[\"a\",[\"href\",\"https://solnic.podia.com/data-oriented-ruby-on-rails\"]],[\"a\",[\"href\",\"https://hanamirb.org\"]],[\"a\",[\"href\",\"https://dry-rb.org\"]],[\"a\",[\"href\",\"https://rom-rb.org\"]],[\"a\",[\"href\",\"https://www.youtube.com/@solnic\"]],[\"a\",[\"href\",\"https://hachyderm.io/@solnic\"]],[\"a\",[\"href\",\"https://x.com/solnic29a\"]],[\"a\",[\"href\",\"https://bsky.app/profile/solnic.bsky.social\"]]],\"sections\":[[1,\"p\",[[0,[],0,\"I'm working on becoming more active online and returning to my projects, so I thought it would be a good time to share what I've been up to lately. Over the past three years, many aspects of my life have changed, from receiving an ADHD diagnosis and moving twice, to landing my first Elixir job. Nowadays, I can confidently say that life is good and I'm doing well. The journey to this point is a story I'd love to share, but I'll save it for another time. Today, I just want to focus on updating you about my work and projects, as exciting developments are underway!\"]]],[1,\"h1\",[[0,[],0,\"💫 Elixir\"]]],[1,\"p\",[[0,[],0,\"In April 2022 I joined \"],[0,[0],1,\"valued.app\"],[0,[],0,\" as the first engineer. This has been my very first job where Elixir is the primary language. I'm very grateful that the founders believed in me and hired me despite the fact I had no real Elixir experience. I've been busting my ass off to become a productive Elixir developer and it feels like I have succeeded. I \"],[0,[1],1,\"know this\"],[0,[],0,\" because whenever I write Ruby, I keep adding \"],[0,[2],1,\"do\"],[0,[],0,\" after a method signature just out of habit 😉\"]]],[1,\"p\",[[0,[],0,\"At valued.app I mostly focus on the backend, but every now and then I get to work with Phoenix and LiveView and I really enjoy it. After about 1.5 year of doing a lot of data-intensive Elixir work, I realized I'm experienced enough to write a library and so \"],[0,[3],1,\"Drops project was born\"],[0,[],0,\". Porting a complex Ruby library to Elixir has been a fascinating process for me, it's another topic I'd love to cover in detail here on this blog or maybe screencasts (what?! yes, read on!).\"]]],[1,\"h1\",[[0,[],0,\"💎 Ruby and Open Source\"]]],[1,\"p\",[[0,[],0,\"Learning Elixir and building a complex system at work was quite demanding, and because of this my Ruby Open Source involvement suffered. I have to admit that I needed to actively work on my \\\"open source shame\\\" (y'all OSS maintainers know what I mean, yeah?) so that I could just let it go for a while and focus on my current priorities.\"]]],[1,\"p\",[[0,[],0,\"Eventually, I realized that this break from Ruby was very healthy for me. I got a lot of perspective and it allowed me to understand better what I should focus on going forward. One of the biggest things I noticed, is how much \"],[0,[1],1,\"functionality\"],[0,[],0,\" we have built over time in Hanami, dry-rb and rom-rb projects. The only problem is that many things were not polished enough and the docs were always lacking. This made me think that if I could go back in time, I would build less and document more and I'm absolutely sure things would turn out better. Sounds so obvious? Yet I needed about a decade to be able to see it clearly. I believe the reasons why I focused so much on building features are complex and personal too (in a nutshell: I needed to keep my brain very busy, and writing docs didn't cut it).\"]]],[1,\"h1\",[[0,[],0,\"✨ Working on a Ruby course\"]]],[1,\"p\",[[0,[],0,\"In 2022 I started working on a Ruby course, initially it was called \\\"Data Oriented Web Development in Ruby\\\" but no matter how hard I tried, I could not memorize the acronym, so I ended up renaming it to \"],[0,[1],1,\"\\\"Data Oriented Ruby on Rails\\\"\"],[0,[],0,\", or in short \\\"DOROR\\\". It's also just a better title.\"]]],[1,\"p\",[[0,[],0,\"It was really hard to work on this course while at the same time learning Elixir, so the progress was very slow. At some point I decided to just put it on hold and see how things will evolve. Luckily, I was able to get back to working on the course in December last year with a fresh mind full of ideas. Roughly 50% of the lessons are done and I plan to finish everything and publish the course before the end of March.\"]]],[1,\"p\",[[0,[],0,\"The course explains the most important learnings from my 17 years of writing Ruby both professionally and as a creator of many Open Source libraries. It explains \\\"crazy\\\" things like objects that don't change, modeling objects like functions, composing functionality with no fuss, dealing with failure in a flexible and elegant way and more. These concepts are explained in the context of a typical Rails application, which should make it much simpler to grok for people who are familiar with Rails.\"]]],[1,\"p\",[[0,[],0,\"There are around 200 people who signed up for the course's newsletter at podia.com where it's going to be launched (which blew my mind to be honest). I suspect that some of them are no longer interested given that I announced the course 1.5 year ago and it's still in the works, but maybe they are, who knows? 🙂\"]]],[1,\"p\",[[0,[],0,\"I'll be sending the first update via course's newsletter soon, so if you're interested, now it's a good time to \"],[0,[4],1,\"sign up and join the waitlist\"],[0,[],0,\"!\"]]],[1,\"h1\",[[0,[],0,\"🌸 Hanami and friends\"]]],[1,\"p\",[[0,[],0,\"I did not manage to do much work on Hanami since we released 2.0. Huge props to Luca Guidi and Tim Riley, and all the contributors, for pushing the project forward. We've got 2.1 release around the corner and I hope to get involved again very soon. I've been analyzing what we've accomplished so far (which includes dry-rb and rom-rb) trying to come up with a plan how to improve and properly document \"],[0,[1],1,\"what we already have\"],[0,[],0,\", instead of chasing the next big feature like I used to do. There are big tasks awaiting, like porting our sites to a new documentation system (\"],[0,[5],1,\"hanamirb.org\"],[0,[],0,\" uses customized Hugo setup and \"],[0,[6],1,\"dry-rb.org\"],[0,[],0,\" and \"],[0,[7],1,\"rom-rb.org\"],[0,[],0,\" are using customized Middleman setup). I also want to focus on basic maintenance and providing learning content.\"]]],[1,\"p\",[[0,[],0,\"My Elixir experience is very helpful here too. Seeing how things work in the Elixir ecosystem is an amazing inspiration for me. And it goes both ways! There are many things that exist in Ruby that I \"],[0,[1],1,\"would love\"],[0,[],0,\" to see in Elixir too.\"]]],[1,\"h1\",[[0,[],0,\"🎥 YouTube channel\"]]],[1,\"p\",[[0,[],0,\"Back in 2021 I used to have a YouTube channel. It grew very quickly hitting 1k subscribers and I got a lot of positive feedback. Unfortunately due to personal reasons I shut it down (actually, I deleted it along with all the screencasts that I recorded except a couple of episodes). Since then I thought about re-establishing this channel many times, especially that I got messages from people asking me if I will ever do it again because they enjoyed my screencasts a lot (which was \"],[0,[1],1,\"amazing\"],[0,[],0,\" and I'm very grateful for this).\"]]],[1,\"p\",[[0,[],0,\"I \"],[0,[1],1,\"believe\"],[0,[],0,\" I'm ready to start screencasting again. However, there are a few adjustments I need to make to ensure that producing the screencasts won't be as time-consuming as before. If I succeed, my channel will be revived, and it's thrilling just to think about it 🙂 One idea I have is to begin with a more laid-back, vlog-like format and simply showcase some projects I'm currently working on. Another aspect that \"],[0,[1],1,\"I enjoy\"],[0,[],0,\" is discussing work, tools, and systems that I use to manage multiple tasks. Reviewing new apps and tools is also a subject that interests me.\"]]],[1,\"p\",[[0,[],0,\"If you're interested, please \"],[0,[8],1,\"subscribe\"],[0,[],0,\" as that's going to be a great source of extra motivation for me!\"]]],[1,\"h1\",[[0,[],0,\"🧠 Mental Health\"]]],[1,\"p\",[[0,[],0,\"I've been meaning to write about mental health for years now. I suppose I just wasn't \"],[0,[1],1,\"truly ready\"],[0,[],0,\" before. There's at least one post I plan to write that will summarize my experience, starting with an ADHD diagnosis and utter chaos, followed by several months of attempting to \\\"fix\\\" myself using medication, and finally achieving peace of mind and significant control over my ADHD. I genuinely feel like \\\"I made it,\\\" and I want to share my story with the world because maybe it will help someone. This was \"],[0,[1],1,\"the hardest thing\"],[0,[],0,\" I've ever done in my life.\"]]],[1,\"p\",[[0,[],0,\"Taking care of my mental health is also the reason why I changed my approach to working on, let's call them \\\"extra projects\\\" 🙂 I'm doing my best to work less but more effectively, and I'm focusing on taking time to relax and recharge my energy levels.\"]]],[1,\"p\",[[0,[],0,\"So far, so good!\"]]],[1,\"h1\",[[0,[],0,\"🙇 Thank you\"]]],[1,\"p\",[[0,[],0,\"I'd like to thank everybody who supported me over the years. It's been a long and a difficult ride for me and I'm just happy that I'm...here.\"]]],[1,\"p\",[[0,[],0,\"If you have any questions or comments, feel free to reach out! You can find me on \"],[0,[9],1,\"Mastodon\"],[0,[],0,\", \"],[0,[10],1,\"X (Twitter)\"],[0,[],0,\" and \"],[0,[11],1,\"Bluesky\"],[0,[],0,\".\"]]]]}", "lexical": null, "html": "<p>I'm working on becoming more active online and returning to my projects, so I thought it would be a good time to share what I've been up to lately. Over the past three years, many aspects of my life have changed, from receiving an ADHD diagnosis and moving twice, to landing my first Elixir job. Nowadays, I can confidently say that life is good and I'm doing well. The journey to this point is a story I'd love to share, but I'll save it for another time. Today, I just want to focus on updating you about my work and projects, as exciting developments are underway!</p><h1 id=\"%F0%9F%92%AB-elixir\">💫 Elixir</h1><p>In April 2022 I joined <a href=\"http://valued.app\">valued.app</a> as the first engineer. This has been my very first job where Elixir is the primary language. I'm very grateful that the founders believed in me and hired me despite the fact I had no real Elixir experience. I've been busting my ass off to become a productive Elixir developer and it feels like I have succeeded. I <strong>know this</strong> because whenever I write Ruby, I keep adding <code>do</code> after a method signature just out of habit 😉</p><p>At valued.app I mostly focus on the backend, but every now and then I get to work with Phoenix and LiveView and I really enjoy it. After about 1.5 year of doing a lot of data-intensive Elixir work, I realized I'm experienced enough to write a library and so <a href=\"https://solnic.dev/introducing-elixir-drops\">Drops project was born</a>. Porting a complex Ruby library to Elixir has been a fascinating process for me, it's another topic I'd love to cover in detail here on this blog or maybe screencasts (what?! yes, read on!).</p><h1 id=\"%F0%9F%92%8E-ruby-and-open-source\">💎 Ruby and Open Source</h1><p>Learning Elixir and building a complex system at work was quite demanding, and because of this my Ruby Open Source involvement suffered. I have to admit that I needed to actively work on my \"open source shame\" (y'all OSS maintainers know what I mean, yeah?) so that I could just let it go for a while and focus on my current priorities.</p><p>Eventually, I realized that this break from Ruby was very healthy for me. I got a lot of perspective and it allowed me to understand better what I should focus on going forward. One of the biggest things I noticed, is how much <strong>functionality</strong> we have built over time in Hanami, dry-rb and rom-rb projects. The only problem is that many things were not polished enough and the docs were always lacking. This made me think that if I could go back in time, I would build less and document more and I'm absolutely sure things would turn out better. Sounds so obvious? Yet I needed about a decade to be able to see it clearly. I believe the reasons why I focused so much on building features are complex and personal too (in a nutshell: I needed to keep my brain very busy, and writing docs didn't cut it).</p><h1 id=\"%E2%9C%A8-working-on-a-ruby-course\">✨ Working on a Ruby course</h1><p>In 2022 I started working on a Ruby course, initially it was called \"Data Oriented Web Development in Ruby\" but no matter how hard I tried, I could not memorize the acronym, so I ended up renaming it to <strong>\"Data Oriented Ruby on Rails\"</strong>, or in short \"DOROR\". It's also just a better title.</p><p>It was really hard to work on this course while at the same time learning Elixir, so the progress was very slow. At some point I decided to just put it on hold and see how things will evolve. Luckily, I was able to get back to working on the course in December last year with a fresh mind full of ideas. Roughly 50% of the lessons are done and I plan to finish everything and publish the course before the end of March.</p><p>The course explains the most important learnings from my 17 years of writing Ruby both professionally and as a creator of many Open Source libraries. It explains \"crazy\" things like objects that don't change, modeling objects like functions, composing functionality with no fuss, dealing with failure in a flexible and elegant way and more. These concepts are explained in the context of a typical Rails application, which should make it much simpler to grok for people who are familiar with Rails.</p><p>There are around 200 people who signed up for the course's newsletter at podia.com where it's going to be launched (which blew my mind to be honest). I suspect that some of them are no longer interested given that I announced the course 1.5 year ago and it's still in the works, but maybe they are, who knows? 🙂</p><p>I'll be sending the first update via course's newsletter soon, so if you're interested, now it's a good time to <a href=\"https://solnic.podia.com/data-oriented-ruby-on-rails\">sign up and join the waitlist</a>!</p><h1 id=\"%F0%9F%8C%B8-hanami-and-friends\">🌸 Hanami and friends</h1><p>I did not manage to do much work on Hanami since we released 2.0. Huge props to Luca Guidi and Tim Riley, and all the contributors, for pushing the project forward. We've got 2.1 release around the corner and I hope to get involved again very soon. I've been analyzing what we've accomplished so far (which includes dry-rb and rom-rb) trying to come up with a plan how to improve and properly document <strong>what we already have</strong>, instead of chasing the next big feature like I used to do. There are big tasks awaiting, like porting our sites to a new documentation system (<a href=\"https://hanamirb.org\">hanamirb.org</a> uses customized Hugo setup and <a href=\"https://dry-rb.org\">dry-rb.org</a> and <a href=\"https://rom-rb.org\">rom-rb.org</a> are using customized Middleman setup). I also want to focus on basic maintenance and providing learning content.</p><p>My Elixir experience is very helpful here too. Seeing how things work in the Elixir ecosystem is an amazing inspiration for me. And it goes both ways! There are many things that exist in Ruby that I <strong>would love</strong> to see in Elixir too.</p><h1 id=\"%F0%9F%8E%A5-youtube-channel\">🎥 YouTube channel</h1><p>Back in 2021 I used to have a YouTube channel. It grew very quickly hitting 1k subscribers and I got a lot of positive feedback. Unfortunately due to personal reasons I shut it down (actually, I deleted it along with all the screencasts that I recorded except a couple of episodes). Since then I thought about re-establishing this channel many times, especially that I got messages from people asking me if I will ever do it again because they enjoyed my screencasts a lot (which was <strong>amazing</strong> and I'm very grateful for this).</p><p>I <strong>believe</strong> I'm ready to start screencasting again. However, there are a few adjustments I need to make to ensure that producing the screencasts won't be as time-consuming as before. If I succeed, my channel will be revived, and it's thrilling just to think about it 🙂 One idea I have is to begin with a more laid-back, vlog-like format and simply showcase some projects I'm currently working on. Another aspect that <strong>I enjoy</strong> is discussing work, tools, and systems that I use to manage multiple tasks. Reviewing new apps and tools is also a subject that interests me.</p><p>If you're interested, please <a href=\"https://www.youtube.com/@solnic\">subscribe</a> as that's going to be a great source of extra motivation for me!</p><h1 id=\"%F0%9F%A7%A0-mental-health\">🧠 Mental Health</h1><p>I've been meaning to write about mental health for years now. I suppose I just wasn't <strong>truly ready</strong> before. There's at least one post I plan to write that will summarize my experience, starting with an ADHD diagnosis and utter chaos, followed by several months of attempting to \"fix\" myself using medication, and finally achieving peace of mind and significant control over my ADHD. I genuinely feel like \"I made it,\" and I want to share my story with the world because maybe it will help someone. This was <strong>the hardest thing</strong> I've ever done in my life.</p><p>Taking care of my mental health is also the reason why I changed my approach to working on, let's call them \"extra projects\" 🙂 I'm doing my best to work less but more effectively, and I'm focusing on taking time to relax and recharge my energy levels.</p><p>So far, so good!</p><h1 id=\"%F0%9F%99%87-thank-you\">🙇 Thank you</h1><p>I'd like to thank everybody who supported me over the years. It's been a long and a difficult ride for me and I'm just happy that I'm...here.</p><p>If you have any questions or comments, feel free to reach out! You can find me on <a href=\"https://hachyderm.io/@solnic\">Mastodon</a>, <a href=\"https://x.com/solnic29a\">X (Twitter)</a> and <a href=\"https://bsky.app/profile/solnic.bsky.social\">Bluesky</a>.</p>", "comment_id": "2ccf63c50385dc43efef07b9", "plaintext": "I'm working on becoming more active online and returning to my projects, so I thought it would be a good time to share what I've been up to lately. Over the past three years, many aspects of my life have changed, from receiving an ADHD diagnosis and moving twice, to landing my first Elixir job. Nowadays, I can confidently say that life is good and I'm doing well. The journey to this point is a story I'd love to share, but I'll save it for another time. Today, I just want to focus on updating you about my work and projects, as exciting developments are underway!\n\n\n💫 Elixir\n\nIn April 2022 I joined valued.app as the first engineer. This has been my very first job where Elixir is the primary language. I'm very grateful that the founders believed in me and hired me despite the fact I had no real Elixir experience. I've been busting my ass off to become a productive Elixir developer and it feels like I have succeeded. I know this because whenever I write Ruby, I keep adding do after a method signature just out of habit 😉\n\nAt valued.app I mostly focus on the backend, but every now and then I get to work with Phoenix and LiveView and I really enjoy it. After about 1.5 year of doing a lot of data-intensive Elixir work, I realized I'm experienced enough to write a library and so Drops project was born. Porting a complex Ruby library to <PERSON><PERSON><PERSON> has been a fascinating process for me, it's another topic I'd love to cover in detail here on this blog or maybe screencasts (what?! yes, read on!).\n\n\n💎 Ruby and Open Source\n\nLearning Elixir and building a complex system at work was quite demanding, and because of this my Ruby Open Source involvement suffered. I have to admit that I needed to actively work on my \"open source shame\" (y'all OSS maintainers know what I mean, yeah?) so that I could just let it go for a while and focus on my current priorities.\n\nEventually, I realized that this break from Ruby was very healthy for me. I got a lot of perspective and it allowed me to understand better what I should focus on going forward. One of the biggest things I noticed, is how much functionality we have built over time in Hanami, dry-rb and rom-rb projects. The only problem is that many things were not polished enough and the docs were always lacking. This made me think that if I could go back in time, I would build less and document more and I'm absolutely sure things would turn out better. Sounds so obvious? Yet I needed about a decade to be able to see it clearly. I believe the reasons why I focused so much on building features are complex and personal too (in a nutshell: I needed to keep my brain very busy, and writing docs didn't cut it).\n\n\n✨ Working on a Ruby course\n\nIn 2022 I started working on a Ruby course, initially it was called \"Data Oriented Web Development in Ruby\" but no matter how hard I tried, I could not memorize the acronym, so I ended up renaming it to \"Data Oriented Ruby on Rails\", or in short \"DOROR\". It's also just a better title.\n\nIt was really hard to work on this course while at the same time learning Elixir, so the progress was very slow. At some point I decided to just put it on hold and see how things will evolve. Luckily, I was able to get back to working on the course in December last year with a fresh mind full of ideas. Roughly 50% of the lessons are done and I plan to finish everything and publish the course before the end of March.\n\nThe course explains the most important learnings from my 17 years of writing Ruby both professionally and as a creator of many Open Source libraries. It explains \"crazy\" things like objects that don't change, modeling objects like functions, composing functionality with no fuss, dealing with failure in a flexible and elegant way and more. These concepts are explained in the context of a typical Rails application, which should make it much simpler to grok for people who are familiar with Rails.\n\nThere are around 200 people who signed up for the course's newsletter at podia.com where it's going to be launched (which blew my mind to be honest). I suspect that some of them are no longer interested given that I announced the course 1.5 year ago and it's still in the works, but maybe they are, who knows? 🙂\n\nI'll be sending the first update via course's newsletter soon, so if you're interested, now it's a good time to sign up and join the waitlist!\n\n\n🌸 Hanami and friends\n\nI did not manage to do much work on Hanami since we released 2.0. Huge props to Luca Guidi and Tim Riley, and all the contributors, for pushing the project forward. We've got 2.1 release around the corner and I hope to get involved again very soon. I've been analyzing what we've accomplished so far (which includes dry-rb and rom-rb) trying to come up with a plan how to improve and properly document what we already have, instead of chasing the next big feature like I used to do. There are big tasks awaiting, like porting our sites to a new documentation system (hanamirb.org uses customized Hugo setup and dry-rb.org and rom-rb.org are using customized Middleman setup). I also want to focus on basic maintenance and providing learning content.\n\nMy Elixir experience is very helpful here too. Seeing how things work in the Elixir ecosystem is an amazing inspiration for me. And it goes both ways! There are many things that exist in Ruby that I would love to see in Elixir too.\n\n\n🎥 YouTube channel\n\nBack in 2021 I used to have a YouTube channel. It grew very quickly hitting 1k subscribers and I got a lot of positive feedback. Unfortunately due to personal reasons I shut it down (actually, I deleted it along with all the screencasts that I recorded except a couple of episodes). Since then I thought about re-establishing this channel many times, especially that I got messages from people asking me if I will ever do it again because they enjoyed my screencasts a lot (which was amazing and I'm very grateful for this).\n\nI believe I'm ready to start screencasting again. However, there are a few adjustments I need to make to ensure that producing the screencasts won't be as time-consuming as before. If I succeed, my channel will be revived, and it's thrilling just to think about it 🙂 One idea I have is to begin with a more laid-back, vlog-like format and simply showcase some projects I'm currently working on. Another aspect that I enjoy is discussing work, tools, and systems that I use to manage multiple tasks. Reviewing new apps and tools is also a subject that interests me.\n\nIf you're interested, please subscribe as that's going to be a great source of extra motivation for me!\n\n\n🧠 Mental Health\n\nI've been meaning to write about mental health for years now. I suppose I just wasn't truly ready before. There's at least one post I plan to write that will summarize my experience, starting with an ADHD diagnosis and utter chaos, followed by several months of attempting to \"fix\" myself using medication, and finally achieving peace of mind and significant control over my ADHD. I genuinely feel like \"I made it,\" and I want to share my story with the world because maybe it will help someone. This was the hardest thing I've ever done in my life.\n\nTaking care of my mental health is also the reason why I changed my approach to working on, let's call them \"extra projects\" 🙂 I'm doing my best to work less but more effectively, and I'm focusing on taking time to relax and recharge my energy levels.\n\nSo far, so good!\n\n\n🙇 Thank you\n\nI'd like to thank everybody who supported me over the years. It's been a long and a difficult ride for me and I'm just happy that I'm...here.\n\nIf you have any questions or comments, feel free to reach out! You can find me on Mastodon, X (Twitter) and Bluesky.", "feature_image": null, "featured": 0, "type": "post", "status": "published", "locale": null, "visibility": "public", "email_recipient_filter": "all", "created_at": "2024-02-13T00:00:00.000Z", "updated_at": "2024-02-13T00:00:00.000Z", "published_at": "2024-02-13T00:00:00.000Z", "custom_excerpt": null, "codeinjection_head": null, "codeinjection_foot": null, "custom_template": null, "canonical_url": null, "newsletter_id": null, "show_title_and_feature_image": 1}, {"id": "6896e29942a07b0001ad037b", "uuid": "cb84464c-54fc-4473-999c-ea0cd262f3b0", "title": "Speed Up Your Elixir Testing with Custom Tasks and Key Bindings in Visual Studio Code", "slug": "speed-up-your-elixir-testing-with-custom-tasks-and-key-bindings-in-visual-studio-code", "mobiledoc": "{\"version\":\"0.3.1\",\"atoms\":[],\"cards\":[[\"code\",{\"code\":\"{\\n  \\\"version\\\": \\\"2.0.0\\\",\\n  \\\"options\\\": {\\n    \\\"shell\\\": {\\n      \\\"executable\\\": \\\"/bin/bash\\\",\\n      \\\"args\\\": [\\n        \\\"-l\\\",\\n        \\\"-c\\\"\\n      ]\\n    },\\n    \\\"cwd\\\": \\\"${fileWorkspaceFolder}\\\"\\n  },\\n  \\\"tasks\\\": [\\n    {\\n      \\\"label\\\": \\\"test all\\\",\\n      \\\"type\\\": \\\"shell\\\",\\n      \\\"command\\\": \\\"mix test\\\",\\n      \\\"problemMatcher\\\": [],\\n      \\\"group\\\": \\\"test\\\",\\n      \\\"presentation\\\": {\\n        \\\"focus\\\": true\\n      }\\n    },\\n    {\\n      \\\"label\\\": \\\"test current file\\\",\\n      \\\"type\\\": \\\"shell\\\",\\n      \\\"command\\\": \\\"mix test ${relativeFile}\\\",\\n      \\\"problemMatcher\\\": [],\\n      \\\"group\\\": \\\"test\\\",\\n      \\\"presentation\\\": {\\n        \\\"focus\\\": true\\n      }\\n    },\\n    {\\n      \\\"label\\\": \\\"test current line\\\",\\n      \\\"type\\\": \\\"shell\\\",\\n      \\\"command\\\": \\\"mix test ${relativeFile}:${lineNumber}\\\",\\n      \\\"presentation\\\": {\\n        \\\"focus\\\": true\\n      }\\n    },\\n    {\\n      \\\"label\\\": \\\"debug current line\\\",\\n      \\\"type\\\": \\\"shell\\\",\\n      \\\"command\\\": \\\"iex --dbg pry -S mix test --timeout 999999999 ${relativeFile}:${lineNumber}\\\",\\n      \\\"presentation\\\": {\\n        \\\"focus\\\": true\\n      }\\n    }\\n  ]\\n}\\n\",\"language\":\"json\"}],[\"code\",{\"code\":\"{\\n    \\\"key\\\": \\\"ctrl+t a\\\",\\n    \\\"command\\\": \\\"workbench.action.tasks.runTask\\\",\\n    \\\"args\\\": \\\"test all\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+t f\\\",\\n    \\\"command\\\": \\\"workbench.action.tasks.runTask\\\",\\n    \\\"args\\\": \\\"test current file\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+t l\\\",\\n    \\\"command\\\": \\\"workbench.action.tasks.runTask\\\",\\n    \\\"args\\\": \\\"test current line\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+t d\\\",\\n    \\\"command\\\": \\\"workbench.action.tasks.runTask\\\",\\n    \\\"args\\\": \\\"debug current line\\\"\\n  }\\n\",\"language\":\"json\"}]],\"markups\":[[\"code\"],[\"em\"],[\"a\",[\"href\",\"https://cdn.hashnode.com/res/hashnode/image/upload/v1709108577181/1e82db91-8a60-4e9f-900e-52726149ea20.png\"]]],\"sections\":[[1,\"p\",[[0,[],0,\"Testing is an integral part of software development that ensures your code works as expected. However, running tests can sometimes be a slow and cumbersome process, especially when you're looking to quickly iterate on your code. I use a \\\"secret\\\" method that allows me to quickly run tests using custom tasks and key bindings in Visual Studio Code (VS Code). This approach is much faster and lighter than other solutions I've tried.\"]]],[1,\"h1\",[[0,[],0,\"Custom Tasks in VS Code\"]]],[1,\"p\",[[0,[],0,\"The first step in speeding up your test runs is to set up custom tasks in VS Code. These tasks can be configured to run specific test commands, such as \\\"test current line\\\" or \\\"debug current line.\\\". All you need to do is to create \"],[0,[0],1,\".vscode/tasks.json\"],[0,[],0,\" file in your project's root directory and add custom tasks. Here's what I have:\"]]],[10,0],[1,\"h1\",[[0,[],0,\"Key Bindings for Efficiency\"]]],[1,\"p\",[[0,[],0,\"Once you have your custom tasks set up, the next step is to bind them to specific keys for quick access. This can be done by editing the \"],[0,[0],1,\"keybindings.json\"],[0,[],0,\" file in VS Code. To do this, open command palette and type \\\"keyboard shortcuts\\\" and select \\\"Open Keyboard Shortcuts (JSON)\\\" option. This will open up \"],[0,[0],1,\"keybindings.json\"],[0,[],0,\" file. Then, you can add shortcuts to quickly run your custom tasks. Here's how I did that:\"]]],[10,1],[1,\"p\",[[0,[],0,\"Now, with a simple keystroke, you can run all tests or tests in the current file or specific tests that can be found under the current line.\"]]],[1,\"h1\",[[0,[],0,\"Debugging with \"],[0,[0],1,\"dbg\"]]],[1,\"p\",[[0,[],0,\"If you need to debug a specific line, simply insert a \"],[0,[0],1,\"dbg\"],[0,[],0,\" breakpoint in your code. Then, use the \"],[0,[0],1,\"ctrl+t d\"],[0,[],0,\" key binding to start an Elixir debug session for that line. I often use \"],[0,[0],1,\"debug current line\"],[0,[],0,\" because it makes experimenting easy, allowing me to see what works and what doesn't. \"],[0,[1],1,\"It's more than just a debugging tool for me.\"]]],[1,\"p\",[[0,[],0,\"This is how a debugging session may look like:\"]]],[1,\"p\",[[0,[],0,\"![](\"],[0,[2],1,\"https://cdn.hashnode.com/res/hashnode/image/upload/v1709108577181/1e82db91-8a60-4e9f-900e-52726149ea20.png\"],[0,[],0,\" align=\\\"center\\\")\"]]],[1,\"h1\",[[0,[],0,\"Happy testing!\"]]],[1,\"p\",[[0,[],0,\"By creating custom tasks and key bindings in VS Code for your Elixir tests, you can greatly speed up your development process. This approach is quick, straightforward, and helps you stay focused on coding and improving your work. While there are many extensions offering similar features, I appreciate the simplicity and complete control that custom tasks provide. I also find it helpful to always have the test output visible in the terminal pane.\"]]],[1,\"p\",[[0,[],0,\"I recommend trying out this method! 🤓\"]]]]}", "lexical": null, "html": "<p>Testing is an integral part of software development that ensures your code works as expected. However, running tests can sometimes be a slow and cumbersome process, especially when you're looking to quickly iterate on your code. I use a \"secret\" method that allows me to quickly run tests using custom tasks and key bindings in Visual Studio Code (VS Code). This approach is much faster and lighter than other solutions I've tried.</p><h1 id=\"custom-tasks-in-vs-code\">Custom Tasks in VS Code</h1><p>The first step in speeding up your test runs is to set up custom tasks in VS Code. These tasks can be configured to run specific test commands, such as \"test current line\" or \"debug current line.\". All you need to do is to create <code>.vscode/tasks.json</code> file in your project's root directory and add custom tasks. Here's what I have:</p><pre><code class=\"language-json\">{\n  \"version\": \"2.0.0\",\n  \"options\": {\n    \"shell\": {\n      \"executable\": \"/bin/bash\",\n      \"args\": [\n        \"-l\",\n        \"-c\"\n      ]\n    },\n    \"cwd\": \"${fileWorkspaceFolder}\"\n  },\n  \"tasks\": [\n    {\n      \"label\": \"test all\",\n      \"type\": \"shell\",\n      \"command\": \"mix test\",\n      \"problemMatcher\": [],\n      \"group\": \"test\",\n      \"presentation\": {\n        \"focus\": true\n      }\n    },\n    {\n      \"label\": \"test current file\",\n      \"type\": \"shell\",\n      \"command\": \"mix test ${relativeFile}\",\n      \"problemMatcher\": [],\n      \"group\": \"test\",\n      \"presentation\": {\n        \"focus\": true\n      }\n    },\n    {\n      \"label\": \"test current line\",\n      \"type\": \"shell\",\n      \"command\": \"mix test ${relativeFile}:${lineNumber}\",\n      \"presentation\": {\n        \"focus\": true\n      }\n    },\n    {\n      \"label\": \"debug current line\",\n      \"type\": \"shell\",\n      \"command\": \"iex --dbg pry -S mix test --timeout 999999999 ${relativeFile}:${lineNumber}\",\n      \"presentation\": {\n        \"focus\": true\n      }\n    }\n  ]\n}\n</code></pre><h1 id=\"key-bindings-for-efficiency\">Key Bindings for Efficiency</h1><p>Once you have your custom tasks set up, the next step is to bind them to specific keys for quick access. This can be done by editing the <code>keybindings.json</code> file in VS Code. To do this, open command palette and type \"keyboard shortcuts\" and select \"Open Keyboard Shortcuts (JSON)\" option. This will open up <code>keybindings.json</code> file. Then, you can add shortcuts to quickly run your custom tasks. Here's how I did that:</p><pre><code class=\"language-json\">{\n    \"key\": \"ctrl+t a\",\n    \"command\": \"workbench.action.tasks.runTask\",\n    \"args\": \"test all\"\n  },\n  {\n    \"key\": \"ctrl+t f\",\n    \"command\": \"workbench.action.tasks.runTask\",\n    \"args\": \"test current file\"\n  },\n  {\n    \"key\": \"ctrl+t l\",\n    \"command\": \"workbench.action.tasks.runTask\",\n    \"args\": \"test current line\"\n  },\n  {\n    \"key\": \"ctrl+t d\",\n    \"command\": \"workbench.action.tasks.runTask\",\n    \"args\": \"debug current line\"\n  }\n</code></pre><p>Now, with a simple keystroke, you can run all tests or tests in the current file or specific tests that can be found under the current line.</p><h1 id=\"debugging-with-dbg\">Debugging with <code>dbg</code></h1><p>If you need to debug a specific line, simply insert a <code>dbg</code> breakpoint in your code. Then, use the <code>ctrl+t d</code> key binding to start an Elixir debug session for that line. I often use <code>debug current line</code> because it makes experimenting easy, allowing me to see what works and what doesn't. <em>It's more than just a debugging tool for me.</em></p><p>This is how a debugging session may look like:</p><p>![](<a href=\"https://cdn.hashnode.com/res/hashnode/image/upload/v1709108577181/1e82db91-8a60-4e9f-900e-52726149ea20.png\">https://cdn.hashnode.com/res/hashnode/image/upload/v1709108577181/1e82db91-8a60-4e9f-900e-52726149ea20.png</a> align=\"center\")</p><h1 id=\"happy-testing\">Happy testing!</h1><p>By creating custom tasks and key bindings in VS Code for your Elixir tests, you can greatly speed up your development process. This approach is quick, straightforward, and helps you stay focused on coding and improving your work. While there are many extensions offering similar features, I appreciate the simplicity and complete control that custom tasks provide. I also find it helpful to always have the test output visible in the terminal pane.</p><p>I recommend trying out this method! 🤓</p>", "comment_id": "b5f885a6fc7074d7a68c2f8e", "plaintext": "Testing is an integral part of software development that ensures your code works as expected. However, running tests can sometimes be a slow and cumbersome process, especially when you're looking to quickly iterate on your code. I use a \"secret\" method that allows me to quickly run tests using custom tasks and key bindings in Visual Studio Code (VS Code). This approach is much faster and lighter than other solutions I've tried.\n\n\nCustom Tasks in VS Code\n\nThe first step in speeding up your test runs is to set up custom tasks in VS Code. These tasks can be configured to run specific test commands, such as \"test current line\" or \"debug current line.\". All you need to do is to create .vscode/tasks.json file in your project's root directory and add custom tasks. Here's what I have:\n\n{\n  \"version\": \"2.0.0\",\n  \"options\": {\n    \"shell\": {\n      \"executable\": \"/bin/bash\",\n      \"args\": [\n        \"-l\",\n        \"-c\"\n      ]\n    },\n    \"cwd\": \"${fileWorkspaceFolder}\"\n  },\n  \"tasks\": [\n    {\n      \"label\": \"test all\",\n      \"type\": \"shell\",\n      \"command\": \"mix test\",\n      \"problemMatcher\": [],\n      \"group\": \"test\",\n      \"presentation\": {\n        \"focus\": true\n      }\n    },\n    {\n      \"label\": \"test current file\",\n      \"type\": \"shell\",\n      \"command\": \"mix test ${relativeFile}\",\n      \"problemMatcher\": [],\n      \"group\": \"test\",\n      \"presentation\": {\n        \"focus\": true\n      }\n    },\n    {\n      \"label\": \"test current line\",\n      \"type\": \"shell\",\n      \"command\": \"mix test ${relativeFile}:${lineNumber}\",\n      \"presentation\": {\n        \"focus\": true\n      }\n    },\n    {\n      \"label\": \"debug current line\",\n      \"type\": \"shell\",\n      \"command\": \"iex --dbg pry -S mix test --timeout 999999999 ${relativeFile}:${lineNumber}\",\n      \"presentation\": {\n        \"focus\": true\n      }\n    }\n  ]\n}\n\n\n\nKey Bindings for Efficiency\n\nOnce you have your custom tasks set up, the next step is to bind them to specific keys for quick access. This can be done by editing the keybindings.json file in VS Code. To do this, open command palette and type \"keyboard shortcuts\" and select \"Open Keyboard Shortcuts (JSON)\" option. This will open up keybindings.json file. Then, you can add shortcuts to quickly run your custom tasks. Here's how I did that:\n\n{\n    \"key\": \"ctrl+t a\",\n    \"command\": \"workbench.action.tasks.runTask\",\n    \"args\": \"test all\"\n  },\n  {\n    \"key\": \"ctrl+t f\",\n    \"command\": \"workbench.action.tasks.runTask\",\n    \"args\": \"test current file\"\n  },\n  {\n    \"key\": \"ctrl+t l\",\n    \"command\": \"workbench.action.tasks.runTask\",\n    \"args\": \"test current line\"\n  },\n  {\n    \"key\": \"ctrl+t d\",\n    \"command\": \"workbench.action.tasks.runTask\",\n    \"args\": \"debug current line\"\n  }\n\n\nNow, with a simple keystroke, you can run all tests or tests in the current file or specific tests that can be found under the current line.\n\n\nDebugging with dbg\n\nIf you need to debug a specific line, simply insert a dbg breakpoint in your code. Then, use the ctrl+t d key binding to start an Elixir debug session for that line. I often use debug current line because it makes experimenting easy, allowing me to see what works and what doesn't. It's more than just a debugging tool for me.\n\nThis is how a debugging session may look like:\n\n![](https://cdn.hashnode.com/res/hashnode/image/upload/v1709108577181/1e82db91-8a60-4e9f-900e-52726149ea20.png align=\"center\")\n\n\nHappy testing!\n\nBy creating custom tasks and key bindings in VS Code for your Elixir tests, you can greatly speed up your development process. This approach is quick, straightforward, and helps you stay focused on coding and improving your work. While there are many extensions offering similar features, I appreciate the simplicity and complete control that custom tasks provide. I also find it helpful to always have the test output visible in the terminal pane.\n\nI recommend trying out this method! 🤓", "feature_image": null, "featured": 0, "type": "post", "status": "published", "locale": null, "visibility": "public", "email_recipient_filter": "all", "created_at": "2024-02-28T00:00:00.000Z", "updated_at": "2024-02-28T00:00:00.000Z", "published_at": "2024-02-28T00:00:00.000Z", "custom_excerpt": null, "codeinjection_head": null, "codeinjection_foot": null, "custom_template": null, "canonical_url": null, "newsletter_id": null, "show_title_and_feature_image": 1}, {"id": "6896e29942a07b0001ad037c", "uuid": "a26e3688-ac8b-4022-968a-94ea502c3a95", "title": "Retiring from the core teams", "slug": "retiring-from-the-core-teams", "mobiledoc": "{\"version\":\"0.3.1\",\"atoms\":[],\"cards\":[],\"markups\":[[\"a\",[\"href\",\"https://hanamirb.org/blog/2024/12/12/a-new-chapter-for-dry-rb-rom/\"]],[\"a\",[\"href\",\"https://lunarlogic.com\"]],[\"a\",[\"href\",\"https://karafka.io\"]],[\"em\"],[\"a\",[\"href\",\"https://github.com/aboutsource\"]],[\"a\",[\"href\",\"https://github.com/panSarin\"]],[\"a\",[\"href\",\"https://github.com/graudeejs\"]],[\"a\",[\"href\",\"https://github.com/avo-hq\"]],[\"a\",[\"href\",\"https://github.com/tak1n\"]],[\"a\",[\"href\",\"https://github.com/blafri\"]],[\"a\",[\"href\",\"https://github.com/g3d\"]],[\"a\",[\"href\",\"https://github.com/cabourn\"]],[\"a\",[\"href\",\"https://github.com/caius\"]],[\"a\",[\"href\",\"https://github.com/shioyama\"]],[\"a\",[\"href\",\"https://github.com/cultureamp-sponsorships\"]],[\"a\",[\"href\",\"https://github.com/davidpelaez\"]],[\"a\",[\"href\",\"https://github.com/eimantas\"]],[\"a\",[\"href\",\"https://github.com/swistaczek\"]],[\"a\",[\"href\",\"https://github.com/francois\"]],[\"a\",[\"href\",\"https://github.com/GabrielMalakias\"]],[\"a\",[\"href\",\"https://github.com/edgarjs\"]],[\"a\",[\"href\",\"https://github.com/mintuhouse\"]],[\"a\",[\"href\",\"https://github.com/hsbt\"]],[\"a\",[\"href\",\"https://github.com/i2chris\"]],[\"a\",[\"href\",\"https://github.com/opti\"]],[\"a\",[\"href\",\"https://github.com/Morozzzko\"]],[\"a\",[\"href\",\"https://github.com/yuszuv\"]],[\"a\",[\"href\",\"https://github.com/janko\"]],[\"a\",[\"href\",\"https://github.com/jasoncharnes\"]],[\"a\",[\"href\",\"https://github.com/skarlcf\"]],[\"a\",[\"href\",\"https://github.com/karafka\"]],[\"a\",[\"href\",\"https://github.com/kaspermeyer\"]],[\"a\",[\"href\",\"https://github.com/kzaitsev\"]],[\"a\",[\"href\",\"https://github.com/k-rudy\"]],[\"a\",[\"href\",\"https://github.com/mcls\"]],[\"a\",[\"href\",\"https://github.com/mensfeld\"]],[\"a\",[\"href\",\"https://github.com/marcoroth\"]],[\"a\",[\"href\",\"https://github.com/d4rky-pl\"]],[\"a\",[\"href\",\"https://github.com/aleksandra-stolyar\"]],[\"a\",[\"href\",\"https://github.com/oleksii-leonov\"]],[\"a\",[\"href\",\"https://github.com/peterberkenbosch\"]],[\"a\",[\"href\",\"https://github.com/prowlycom\"]],[\"a\",[\"href\",\"https://github.com/milushov\"]],[\"a\",[\"href\",\"https://github.com/woarewe\"]],[\"a\",[\"href\",\"https://github.com/ruslantolstov\"]],[\"a\",[\"href\",\"https://github.com/radar\"]],[\"a\",[\"href\",\"https://github.com/gadimbaylisahil\"]],[\"a\",[\"href\",\"https://github.com/scoutapm-sponsorships\"]],[\"a\",[\"href\",\"https://github.com/cllns\"]],[\"a\",[\"href\",\"https://github.com/swilgosz\"]],[\"a\",[\"href\",\"https://github.com/getsentry\"]],[\"a\",[\"href\",\"https://github.com/svoop\"]],[\"a\",[\"href\",\"https://github.com/htcarr3\"]],[\"a\",[\"href\",\"https://github.com/thomasklemm\"]],[\"a\",[\"href\",\"https://github.com/timgluz\"]],[\"a\",[\"href\",\"https://github.com/tomdonarski\"]],[\"a\",[\"href\",\"https://github.com/v-kolesnikov\"]],[\"a\",[\"href\",\"https://github.com/cylon-v\"]],[\"a\",[\"href\",\"https://github.com/wilsonsilva\"]],[\"a\",[\"href\",\"https://github.com/letmein\"]],[\"a\",[\"href\",\"https://github.com/ziyan-junaideen\"]],[\"strong\"],[\"a\",[\"href\",\"https://bsky.app/profile/solnic.dev\"]],[\"a\",[\"href\",\"https://hachyderm.io/@solnic\"]],[\"a\",[\"href\",\"https://www.linkedin.com/in/solnic/\"]],[\"a\",[\"href\",\"https://x.com/solnic_dev\"]]],\"sections\":[[1,\"p\",[[0,[],0,\"Today \"],[0,[0],1,\"we’re announcing on the official Hanami blog\"],[0,[],0,\" that I am retiring from the core teams. It’s a decision I’ve made after about 2 years of contemplating how I want to move forward with my involvement with the Ruby community and its Open Source ecosystem.\"]]],[1,\"p\",[[0,[],0,\"As you can imagine, this decision has not been easy to make. I have a long history of working with Ruby and being an active member of its Open Source community. I will do my best to tell you why I made this decision, but first and foremost, this post is about the people who changed my life and a way of saying thank you. Let's start with that!\"]]],[1,\"p\",[[0,[],0,\"This is in somewhat chronological order:\"]]],[1,\"p\",[[0,[],0,\"Paul Klipp - thank you for believing in me back in 2007 when you hired me as a Ruby on Rails developer, even though the only thing I had written in Ruby was a sample app for my Bachelor thesis. I spent an amazing 3.5 years working at \"],[0,[1],1,\"Lunar Logic\"],[0,[],0,\" - the longest time I’ve ever worked for a single company, and I’ve grown a lot there.\"]]],[1,\"p\",[[0,[],0,\"All the people from Lunar Logic (called Lunar Logic Polska back then) - folks, you have no idea what it meant to work with you. I’m grateful that I’m still in touch with some of you.\"]]],[1,\"p\",[[0,[],0,\"Dan Kubb - thank you for being my mentor and inviting me to the DataMapper core team. This is when everything really started for me, and it’s had a lasting effect on pretty much everything I’ve done later on.\"]]],[1,\"p\",[[0,[],0,\"Luca Guidi - 11 years ago we had a call, and you told me about Lotus (now Hanami). Time flies! Like you said, it’s been a wild ride. Thank you so much for your hard work, trust, support, and patience. Ruby has been lucky to have you, and I wish you all the best in the next chapter of your life.\"]]],[1,\"p\",[[0,[],0,\"Andy Holland - thank you for starting dry-rb! It grew so much over time, but it all started with you and dry-container + dry-configurable! It was so great to meet you in person during Brighton Ruby, and I hope you’re doing well.\"]]],[1,\"p\",[[0,[],0,\"Nikita Shilnikov - thank you for joining me and working so hard on rom-rb and dry-rb. I suspect many people have no clue about the impact your work has had on these projects.\"]]],[1,\"p\",[[0,[],0,\"Maciej Mensfeld - thank you for the time we spent together sharing offices, all the support and for being an amazing KRUG organizer! I wish you all the best with your \"],[0,[2],1,\"Karafka\"],[0,[],0,\" project!\"]]],[1,\"h2\",[[0,[],0,\"Thank you Tim!\"]]],[1,\"p\",[[0,[],0,\"Special thanks go to Tim Riley - remember that email you sent to me 9 years ago about Transflow and a gem you were building inspired by it? This is where everything started for us. I'm so unbelievably proud of what we've accomplished together, and I truly wish I could continue working on Hanami with you.\"]]],[1,\"p\",[[0,[],0,\"I wish you, the entire team, and all the contributors all the best. I'll support you however I can, and I'm very happy to see how Hanami has evolved under your leadership.\"]]],[1,\"h2\",[[0,[],0,\"Community\"]]],[1,\"p\",[[0,[],0,\"I wasn’t sure if I wanted to thank specific people out of fear that I’d miss someone, but I decided to do it anyway. However, because of this, I also want to thank the entire community and \"],[0,[3],1,\"literally everybody\"],[0,[],0,\" who worked with me, supported me, all the people who loved my work, and those who hated it (for real) - all of you have made an impact on my life, career, and even specific skills that I developed, and I am grateful for this.\"]]],[1,\"p\",[[0,[],0,\"Over the years, I’ve worked with hundreds of people through my work on OSS, and it’s an experience that shaped my personality and helped me grow. This is priceless.\"]]],[1,\"p\",[[0,[],0,\"I will genuinely miss y’all, but at the same time, I am sure that I’m doing what’s best for me.\"]]],[1,\"h2\",[[0,[],0,\"GitHub Sponsors\"]]],[1,\"p\",[[0,[],0,\"Special thanks go to all my past and present GitHub Sponsors, in alphabetical order:\"]]],[1,\"p\",[[0,[4],1,\"about source GmbH\"]]],[1,\"p\",[[0,[5],1,\"Adam Piotrowski\"]]],[1,\"p\",[[0,[6],1,\"Aldis Berjoza\"]]],[1,\"p\",[[0,[7],1,\"Avo\"]]],[1,\"p\",[[0,[8],1,\"Benjamin Klotz\"]]],[1,\"p\",[[0,[9],1,\"blafri\"]]],[1,\"p\",[[0,[10],1,\"Bohdan V.\"]]],[1,\"p\",[[0,[11],1,\"Cabourn\"]]],[1,\"p\",[[0,[12],1,\"Caius Durling\"]]],[1,\"p\",[[0,[13],1,\"Chris Salzberg\"]]],[1,\"p\",[[0,[14],1,\"Culture Amp GitHub Sponsorships\"]]],[1,\"p\",[[0,[15],1,\"David Pelaez\"]]],[1,\"p\",[[0,[16],1,\"Eimantas\"]]],[1,\"p\",[[0,[17],1,\"Ernest Bursa\"]]],[1,\"p\",[[0,[18],1,\"François Beausoleil\"]]],[1,\"p\",[[0,[19],1,\"Gabriel Malaquias\"]]],[1,\"p\",[[0,[20],1,\"Gar 🦖\"]]],[1,\"p\",[[0,[21],1,\"Hasan Kumar\"]]],[1,\"p\",[[0,[22],1,\"Hiroshi SHIBATA\"]]],[1,\"p\",[[0,[23],1,\"i2chris\"]]],[1,\"p\",[[0,[24],1,\"Igor Pstyga\"]]],[1,\"p\",[[0,[25],1,\"Igor S. Morozov\"]]],[1,\"p\",[[0,[26],1,\"jan\"]]],[1,\"p\",[[0,[27],1,\"Janko Marohnić\"]]],[1,\"p\",[[0,[28],1,\"Jason Charnes\"]]],[1,\"p\",[[0,[29],1,\"Kamil Skrzypiński\"]]],[1,\"p\",[[0,[30],1,\"Karafka\"]]],[1,\"p\",[[0,[31],1,\"Kasper Meyer\"]]],[1,\"p\",[[0,[32],1,\"Kirill Zaitsev\"]]],[1,\"p\",[[0,[33],1,\"Konstantin Rudy\"]]],[1,\"p\",[[0,[34],1,\"Maarten Claes\"]]],[1,\"p\",[[0,[35],1,\"Maciej Mensfeld\"]]],[1,\"p\",[[0,[36],1,\"Marco Roth\"]]],[1,\"p\",[[0,[37],1,\"Michał Matyas\"]]],[1,\"p\",[[0,[38],1,\"Oleksandra Tomilina\"]]],[1,\"p\",[[0,[39],1,\"Oleksii Leonov\"]]],[1,\"p\",[[0,[40],1,\"Peter Berkenbosch\"]]],[1,\"p\",[[0,[41],1,\"Prowly\"]]],[1,\"p\",[[0,[42],1,\"roma\"]]],[1,\"p\",[[0,[43],1,\"Rostislav Zhuravsky\"]]],[1,\"p\",[[0,[44],1,\"Ruslan Tolstov\"]]],[1,\"p\",[[0,[45],1,\"Ryan Bigg\"]]],[1,\"p\",[[0,[46],1,\"Sahil Gadimbayli\"]]],[1,\"p\",[[0,[47],1,\"Scout Monitoring Sponsorships\"]]],[1,\"p\",[[0,[48],1,\"Sean Collins\"]]],[1,\"p\",[[0,[49],1,\"Seb Wilgosz\"]]],[1,\"p\",[[0,[50],1,\"Sentry\"]]],[1,\"p\",[[0,[51],1,\"Sven Schwyn\"]]],[1,\"p\",[[0,[52],1,\"Thomas Carr\"]]],[1,\"p\",[[0,[53],1,\"Thomas Klemm\"]]],[1,\"p\",[[0,[54],1,\"Timo Sulg\"]]],[1,\"p\",[[0,[55],1,\"Tom Donarski\"]]],[1,\"p\",[[0,[56],1,\"Vasily Kolesnikov\"]]],[1,\"p\",[[0,[57],1,\"Vladimir Kalinkin\"]]],[1,\"p\",[[0,[58],1,\"Wilson Silva\"]]],[1,\"p\",[[0,[59],1,\"Yuriy Kharchenko\"]]],[1,\"p\",[[0,[60],1,\"Ziyan Junaideen\"]]],[1,\"p\",[[0,[],0,\"I will be sending an update to my current sponsors, to make sure people are aware of the change of my OSS focus.\"]]],[1,\"h2\",[[0,[],0,\"Moving on\"]]],[1,\"p\",[[0,[],0,\"Explaining why I’ve made this decision is going to be hard. I’m a bit nervous because I don’t want this to sound like I no longer believe in what I had built and contributed to, because I still believe that the FP/OO experiment I started back in 2013/2014 panned out well.\"]]],[1,\"p\",[[0,[],0,\"The reason for the decision is more related to how much time I have for Ruby and what my priorities are. One of my priorities is to make my life simpler and more balanced so that I can be a better partner and father. I've found that being active in the Ruby community can be too draining for me, just because of the number of projects I've been involved with, and other non-technical reasons.\"]]],[1,\"p\",[[0,[],0,\"I realized that despite my best intentions, I have not managed to contribute in any significant way since around 2021. Coincidentally, this is the year when I started working with Elixir full-time. This realization gave me a lot to think about, and finally, I decided to pull the plug earlier this year.\"]]],[1,\"p\",[[0,[],0,\"Working with Elixir has been very refreshing and has given me energy that I hadn't felt when working with Ruby for quite some time. Sure, working on Hanami gets pretty close to this, but it almost always came with this weird pressure. After some time, I just couldn't handle it well.\"]]],[1,\"p\",[[0,[],0,\"In Elixir, things are just simpler for me - I don’t need to work on ROM; there’s Ecto. I don’t need to work on Hanami; there’s Phoenix. I don’t need to have constant “battles” about the functional way of writing code and immutable data structures being The Way™, because, well 👉🏻 Elixir.\"]]],[1,\"p\",[[0,[],0,\"It’s harmony, and it’s beautiful.\"]]],[1,\"p\",[[0,[],0,\"This is not news to me either. I was fully aware of this for years, but when I started working with Elixir, that's when I really felt it. This experience made me believe even more strongly that \"],[0,[61],1,\"Hanami has the potential to grow into a truly groundbreaking Ruby framework\"],[0,[],0,\", and I think it’s headed this way. The foundational technology is in place; now it’s only a matter of growing the framework into a delightful DX, and it has already achieved that to a large degree, in my opinion.\"]]],[1,\"p\",[[0,[],0,\"I’m still rooting for Hanami, but I simply do not have the time and emotional capacity to be an active member of the core teams. I prioritize my well-being and my family over my work ambitions.\"]]],[1,\"h2\",[[0,[],0,\"What exactly is happening now\"]]],[1,\"p\",[[0,[],0,\"As you could read in the official announcement, I’m no longer a Hanami/dry-rb/rom-rb core team member. What this means in practice is that I won’t be doing any coding work or be active in other ways within the Hanami community. I will, however, support Hanami by providing information and guidance whenever it’s needed, as I’ll still be in touch with Tim and the team.\"]]],[1,\"p\",[[0,[61],1,\"This does not mean I’m removing myself completely from the Ruby community.\"],[0,[],0,\" I’m currently working as an SDK maintainer at Sentry, specifically for Elixir \"],[0,[61],1,\"and\"],[0,[],0,\" Ruby, which means there’s a chance we’ll bump into each other every now and then.\"]]],[1,\"p\",[[0,[],0,\"I hope this makes sense to you, and if you have any questions, do not hesitate to ask. You can find me on social media:\"]]],[1,\"p\",[[0,[62],1,\"Bsky\"]]],[1,\"p\",[[0,[63],1,\"Mastodon\"]]],[1,\"p\",[[0,[64],1,\"LinkedIn\"]]],[1,\"p\",[[0,[65],1,\"X\"]]],[1,\"p\",[[0,[],0,\"Thanks for reading, and I wish you a great day 🙂\"]]]]}", "lexical": null, "html": "<p>Today <a href=\"https://hanamirb.org/blog/2024/12/12/a-new-chapter-for-dry-rb-rom/\">we’re announcing on the official Hanami blog</a> that I am retiring from the core teams. It’s a decision I’ve made after about 2 years of contemplating how I want to move forward with my involvement with the Ruby community and its Open Source ecosystem.</p><p>As you can imagine, this decision has not been easy to make. I have a long history of working with Ruby and being an active member of its Open Source community. I will do my best to tell you why I made this decision, but first and foremost, this post is about the people who changed my life and a way of saying thank you. Let's start with that!</p><p>This is in somewhat chronological order:</p><p><PERSON> - thank you for believing in me back in 2007 when you hired me as a Ruby on Rails developer, even though the only thing I had written in Ruby was a sample app for my Bachelor thesis. I spent an amazing 3.5 years working at <a href=\"https://lunarlogic.com\">Lunar Logic</a> - the longest time I’ve ever worked for a single company, and I’ve grown a lot there.</p><p>All the people from Lunar Logic (called Lunar Logic Polska back then) - folks, you have no idea what it meant to work with you. I’m grateful that I’m still in touch with some of you.</p><p><PERSON> - thank you for being my mentor and inviting me to the DataMapper core team. This is when everything really started for me, and it’s had a lasting effect on pretty much everything I’ve done later on.</p><p><PERSON> Guidi - 11 years ago we had a call, and you told me about <PERSON> (now <PERSON>ami). Time flies! Like you said, it’s been a wild ride. Thank you so much for your hard work, trust, support, and patience. <PERSON> has been lucky to have you, and I wish you all the best in the next chapter of your life.</p><p>Andy Holland - thank you for starting dry-rb! It grew so much over time, but it all started with you and dry-container + dry-configurable! It was so great to meet you in person during Brighton Ruby, and I hope you’re doing well.</p><p>Nikita Shilnikov - thank you for joining me and working so hard on rom-rb and dry-rb. I suspect many people have no clue about the impact your work has had on these projects.</p><p>Maciej Mensfeld - thank you for the time we spent together sharing offices, all the support and for being an amazing KRUG organizer! I wish you all the best with your <a href=\"https://karafka.io\">Karafka</a> project!</p><h2 id=\"thank-you-tim\">Thank you Tim!</h2><p>Special thanks go to Tim Riley - remember that email you sent to me 9 years ago about Transflow and a gem you were building inspired by it? This is where everything started for us. I'm so unbelievably proud of what we've accomplished together, and I truly wish I could continue working on Hanami with you.</p><p>I wish you, the entire team, and all the contributors all the best. I'll support you however I can, and I'm very happy to see how Hanami has evolved under your leadership.</p><h2 id=\"community\">Community</h2><p>I wasn’t sure if I wanted to thank specific people out of fear that I’d miss someone, but I decided to do it anyway. However, because of this, I also want to thank the entire community and <em>literally everybody</em> who worked with me, supported me, all the people who loved my work, and those who hated it (for real) - all of you have made an impact on my life, career, and even specific skills that I developed, and I am grateful for this.</p><p>Over the years, I’ve worked with hundreds of people through my work on OSS, and it’s an experience that shaped my personality and helped me grow. This is priceless.</p><p>I will genuinely miss y’all, but at the same time, I am sure that I’m doing what’s best for me.</p><h2 id=\"github-sponsors\">GitHub Sponsors</h2><p>Special thanks go to all my past and present GitHub Sponsors, in alphabetical order:</p><p><a href=\"https://github.com/aboutsource\">about source GmbH</a></p><p><a href=\"https://github.com/panSarin\">Adam Piotrowski</a></p><p><a href=\"https://github.com/graudeejs\">Aldis Berjoza</a></p><p><a href=\"https://github.com/avo-hq\">Avo</a></p><p><a href=\"https://github.com/tak1n\">Benjamin Klotz</a></p><p><a href=\"https://github.com/blafri\">blafri</a></p><p><a href=\"https://github.com/g3d\">Bohdan V.</a></p><p><a href=\"https://github.com/cabourn\">Cabourn</a></p><p><a href=\"https://github.com/caius\">Caius Durling</a></p><p><a href=\"https://github.com/shioyama\">Chris Salzberg</a></p><p><a href=\"https://github.com/cultureamp-sponsorships\">Culture Amp GitHub Sponsorships</a></p><p><a href=\"https://github.com/davidpelaez\">David Pelaez</a></p><p><a href=\"https://github.com/eimantas\">Eimantas</a></p><p><a href=\"https://github.com/swistaczek\">Ernest Bursa</a></p><p><a href=\"https://github.com/francois\">François Beausoleil</a></p><p><a href=\"https://github.com/GabrielMalakias\">Gabriel Malaquias</a></p><p><a href=\"https://github.com/edgarjs\">Gar 🦖</a></p><p><a href=\"https://github.com/mintuhouse\">Hasan Kumar</a></p><p><a href=\"https://github.com/hsbt\">Hiroshi SHIBATA</a></p><p><a href=\"https://github.com/i2chris\">i2chris</a></p><p><a href=\"https://github.com/opti\">Igor Pstyga</a></p><p><a href=\"https://github.com/Morozzzko\">Igor S. Morozov</a></p><p><a href=\"https://github.com/yuszuv\">jan</a></p><p><a href=\"https://github.com/janko\">Janko Marohnić</a></p><p><a href=\"https://github.com/jasoncharnes\">Jason Charnes</a></p><p><a href=\"https://github.com/skarlcf\">Kamil Skrzypiński</a></p><p><a href=\"https://github.com/karafka\">Karafka</a></p><p><a href=\"https://github.com/kaspermeyer\">Kasper Meyer</a></p><p><a href=\"https://github.com/kzaitsev\">Kirill Zaitsev</a></p><p><a href=\"https://github.com/k-rudy\">Konstantin Rudy</a></p><p><a href=\"https://github.com/mcls\">Maarten Claes</a></p><p><a href=\"https://github.com/mensfeld\">Maciej Mensfeld</a></p><p><a href=\"https://github.com/marcoroth\">Marco Roth</a></p><p><a href=\"https://github.com/d4rky-pl\">Michał Matyas</a></p><p><a href=\"https://github.com/aleksandra-stolyar\">Oleksandra Tomilina</a></p><p><a href=\"https://github.com/oleksii-leonov\">Oleksii Leonov</a></p><p><a href=\"https://github.com/peterberkenbosch\">Peter Berkenbosch</a></p><p><a href=\"https://github.com/prowlycom\">Prowly</a></p><p><a href=\"https://github.com/milushov\">roma</a></p><p><a href=\"https://github.com/woarewe\">Rostislav Zhuravsky</a></p><p><a href=\"https://github.com/ruslantolstov\">Ruslan Tolstov</a></p><p><a href=\"https://github.com/radar\">Ryan Bigg</a></p><p><a href=\"https://github.com/gadimbaylisahil\">Sahil Gadimbayli</a></p><p><a href=\"https://github.com/scoutapm-sponsorships\">Scout Monitoring Sponsorships</a></p><p><a href=\"https://github.com/cllns\">Sean Collins</a></p><p><a href=\"https://github.com/swilgosz\">Seb Wilgosz</a></p><p><a href=\"https://github.com/getsentry\">Sentry</a></p><p><a href=\"https://github.com/svoop\">Sven Schwyn</a></p><p><a href=\"https://github.com/htcarr3\">Thomas Carr</a></p><p><a href=\"https://github.com/thomasklemm\">Thomas Klemm</a></p><p><a href=\"https://github.com/timgluz\">Timo Sulg</a></p><p><a href=\"https://github.com/tomdonarski\">Tom Donarski</a></p><p><a href=\"https://github.com/v-kolesnikov\">Vasily Kolesnikov</a></p><p><a href=\"https://github.com/cylon-v\">Vladimir Kalinkin</a></p><p><a href=\"https://github.com/wilsonsilva\">Wilson Silva</a></p><p><a href=\"https://github.com/letmein\">Yuriy Kharchenko</a></p><p><a href=\"https://github.com/ziyan-junaideen\">Ziyan Junaideen</a></p><p>I will be sending an update to my current sponsors, to make sure people are aware of the change of my OSS focus.</p><h2 id=\"moving-on\">Moving on</h2><p>Explaining why I’ve made this decision is going to be hard. I’m a bit nervous because I don’t want this to sound like I no longer believe in what I had built and contributed to, because I still believe that the FP/OO experiment I started back in 2013/2014 panned out well.</p><p>The reason for the decision is more related to how much time I have for Ruby and what my priorities are. One of my priorities is to make my life simpler and more balanced so that I can be a better partner and father. I've found that being active in the Ruby community can be too draining for me, just because of the number of projects I've been involved with, and other non-technical reasons.</p><p>I realized that despite my best intentions, I have not managed to contribute in any significant way since around 2021. Coincidentally, this is the year when I started working with Elixir full-time. This realization gave me a lot to think about, and finally, I decided to pull the plug earlier this year.</p><p>Working with Elixir has been very refreshing and has given me energy that I hadn't felt when working with Ruby for quite some time. Sure, working on Hanami gets pretty close to this, but it almost always came with this weird pressure. After some time, I just couldn't handle it well.</p><p>In Elixir, things are just simpler for me - I don’t need to work on ROM; there’s Ecto. I don’t need to work on Hanami; there’s Phoenix. I don’t need to have constant “battles” about the functional way of writing code and immutable data structures being The Way™, because, well 👉🏻 Elixir.</p><p>It’s harmony, and it’s beautiful.</p><p>This is not news to me either. I was fully aware of this for years, but when I started working with Elixir, that's when I really felt it. This experience made me believe even more strongly that <strong>Hanami has the potential to grow into a truly groundbreaking Ruby framework</strong>, and I think it’s headed this way. The foundational technology is in place; now it’s only a matter of growing the framework into a delightful DX, and it has already achieved that to a large degree, in my opinion.</p><p>I’m still rooting for Hanami, but I simply do not have the time and emotional capacity to be an active member of the core teams. I prioritize my well-being and my family over my work ambitions.</p><h2 id=\"what-exactly-is-happening-now\">What exactly is happening now</h2><p>As you could read in the official announcement, I’m no longer a Hanami/dry-rb/rom-rb core team member. What this means in practice is that I won’t be doing any coding work or be active in other ways within the Hanami community. I will, however, support Hanami by providing information and guidance whenever it’s needed, as I’ll still be in touch with Tim and the team.</p><p><strong>This does not mean I’m removing myself completely from the Ruby community.</strong> I’m currently working as an SDK maintainer at Sentry, specifically for Elixir <strong>and</strong> Ruby, which means there’s a chance we’ll bump into each other every now and then.</p><p>I hope this makes sense to you, and if you have any questions, do not hesitate to ask. You can find me on social media:</p><p><a href=\"https://bsky.app/profile/solnic.dev\">Bsky</a></p><p><a href=\"https://hachyderm.io/@solnic\">Mastodon</a></p><p><a href=\"https://www.linkedin.com/in/solnic/\">LinkedIn</a></p><p><a href=\"https://x.com/solnic_dev\">X</a></p><p>Thanks for reading, and I wish you a great day 🙂</p>", "comment_id": "d79accb02051c650b9a685f9", "plaintext": "Today we’re announcing on the official Han<PERSON> blog that I am retiring from the core teams. It’s a decision I’ve made after about 2 years of contemplating how I want to move forward with my involvement with the Ruby community and its Open Source ecosystem.\n\nAs you can imagine, this decision has not been easy to make. I have a long history of working with <PERSON> and being an active member of its Open Source community. I will do my best to tell you why I made this decision, but first and foremost, this post is about the people who changed my life and a way of saying thank you. Let's start with that!\n\nThis is in somewhat chronological order:\n\n<PERSON> - thank you for believing in me back in 2007 when you hired me as a Ruby on Rails developer, even though the only thing I had written in <PERSON> was a sample app for my Bachelor thesis. I spent an amazing 3.5 years working at Lunar Logic - the longest time I’ve ever worked for a single company, and I’ve grown a lot there.\n\nAll the people from Lunar Logic (called Lunar Logic Polska back then) - folks, you have no idea what it meant to work with you. I’m grateful that I’m still in touch with some of you.\n\n<PERSON> - thank you for being my mentor and inviting me to the DataMapper core team. This is when everything really started for me, and it’s had a lasting effect on pretty much everything I’ve done later on.\n\n<PERSON> - 11 years ago we had a call, and you told me about <PERSON> (now Hanami). Time flies! Like you said, it’s been a wild ride. Thank you so much for your hard work, trust, support, and patience. <PERSON> has been lucky to have you, and I wish you all the best in the next chapter of your life.\n\n<PERSON> <PERSON> - thank you for starting dry-rb! It grew so much over time, but it all started with you and dry-container + dry-configurable! It was so great to meet you in person during Brighton <PERSON>, and I hope you’re doing well.\n\nNikita Shilnikov - thank you for joining me and working so hard on rom-rb and dry-rb. I suspect many people have no clue about the impact your work has had on these projects.\n\nMaciej Mensfeld - thank you for the time we spent together sharing offices, all the support and for being an amazing KRUG organizer! I wish you all the best with your Karafka project!\n\n\nThank you Tim!\n\nSpecial thanks go to Tim Riley - remember that email you sent to me 9 years ago about Transflow and a gem you were building inspired by it? This is where everything started for us. I'm so unbelievably proud of what we've accomplished together, and I truly wish I could continue working on Hanami with you.\n\nI wish you, the entire team, and all the contributors all the best. I'll support you however I can, and I'm very happy to see how Hanami has evolved under your leadership.\n\n\nCommunity\n\nI wasn’t sure if I wanted to thank specific people out of fear that I’d miss someone, but I decided to do it anyway. However, because of this, I also want to thank the entire community and literally everybody who worked with me, supported me, all the people who loved my work, and those who hated it (for real) - all of you have made an impact on my life, career, and even specific skills that I developed, and I am grateful for this.\n\nOver the years, I’ve worked with hundreds of people through my work on OSS, and it’s an experience that shaped my personality and helped me grow. This is priceless.\n\nI will genuinely miss y’all, but at the same time, I am sure that I’m doing what’s best for me.\n\n\nGitHub Sponsors\n\nSpecial thanks go to all my past and present GitHub Sponsors, in alphabetical order:\n\nabout source GmbH\n\nAdam Piotrowski\n\nAldis Berjoza\n\nAvo\n\nBenjamin Klotz\n\nblafri\n\nBohdan V.\n\nCabourn\n\nCaius Durling\n\nChris Salzberg\n\nCulture Amp GitHub Sponsorships\n\nDavid Pelaez\n\nEimantas\n\nErnest Bursa\n\nFrançois Beausoleil\n\nGabriel Malaquias\n\nGar 🦖\n\nHasan Kumar\n\nHiroshi SHIBATA\n\ni2chris\n\nIgor Pstyga\n\nIgor S. Morozov\n\njan\n\nJanko Marohnić\n\nJason Charnes\n\nKamil Skrzypiński\n\nKarafka\n\nKasper Meyer\n\nKirill Zaitsev\n\nKonstantin Rudy\n\nMaarten Claes\n\nMaciej Mensfeld\n\nMarco Roth\n\nMichał Matyas\n\nOleksandra Tomilina\n\nOleksii Leonov\n\nPeter Berkenbosch\n\nProwly\n\nroma\n\nRostislav Zhuravsky\n\nRuslan Tolstov\n\nRyan Bigg\n\nSahil Gadimbayli\n\nScout Monitoring Sponsorships\n\nSean Collins\n\nSeb Wilgosz\n\nSentry\n\nSven Schwyn\n\nThomas Carr\n\nThomas Klemm\n\nTimo Sulg\n\nTom Donarski\n\nVasily Kolesnikov\n\nVladimir Kalinkin\n\nWilson Silva\n\nYuriy Kharchenko\n\nZiyan Junaideen\n\nI will be sending an update to my current sponsors, to make sure people are aware of the change of my OSS focus.\n\n\nMoving on\n\nExplaining why I’ve made this decision is going to be hard. I’m a bit nervous because I don’t want this to sound like I no longer believe in what I had built and contributed to, because I still believe that the FP/OO experiment I started back in 2013/2014 panned out well.\n\nThe reason for the decision is more related to how much time I have for Ruby and what my priorities are. One of my priorities is to make my life simpler and more balanced so that I can be a better partner and father. I've found that being active in the Ruby community can be too draining for me, just because of the number of projects I've been involved with, and other non-technical reasons.\n\nI realized that despite my best intentions, I have not managed to contribute in any significant way since around 2021. Coincidentally, this is the year when I started working with Elixir full-time. This realization gave me a lot to think about, and finally, I decided to pull the plug earlier this year.\n\nWorking with Elixir has been very refreshing and has given me energy that I hadn't felt when working with Ruby for quite some time. Sure, working on Hanami gets pretty close to this, but it almost always came with this weird pressure. After some time, I just couldn't handle it well.\n\nIn Elixir, things are just simpler for me - I don’t need to work on ROM; there’s Ecto. I don’t need to work on Hanami; there’s Phoenix. I don’t need to have constant “battles” about the functional way of writing code and immutable data structures being The Way™, because, well 👉🏻 Elixir.\n\nIt’s harmony, and it’s beautiful.\n\nThis is not news to me either. I was fully aware of this for years, but when I started working with Elixir, that's when I really felt it. This experience made me believe even more strongly that Hanami has the potential to grow into a truly groundbreaking Ruby framework, and I think it’s headed this way. The foundational technology is in place; now it’s only a matter of growing the framework into a delightful DX, and it has already achieved that to a large degree, in my opinion.\n\nI’m still rooting for Hanami, but I simply do not have the time and emotional capacity to be an active member of the core teams. I prioritize my well-being and my family over my work ambitions.\n\n\nWhat exactly is happening now\n\nAs you could read in the official announcement, I’m no longer a Hanami/dry-rb/rom-rb core team member. What this means in practice is that I won’t be doing any coding work or be active in other ways within the Hanami community. I will, however, support Hanami by providing information and guidance whenever it’s needed, as I’ll still be in touch with Tim and the team.\n\nThis does not mean I’m removing myself completely from the Ruby community. I’m currently working as an SDK maintainer at Sentry, specifically for Elixir and Ruby, which means there’s a chance we’ll bump into each other every now and then.\n\nI hope this makes sense to you, and if you have any questions, do not hesitate to ask. You can find me on social media:\n\nBsky\n\nMastodon\n\nLinkedIn\n\nX\n\nThanks for reading, and I wish you a great day 🙂", "feature_image": null, "featured": 0, "type": "post", "status": "published", "locale": null, "visibility": "public", "email_recipient_filter": "all", "created_at": "2024-12-12T00:00:00.000Z", "updated_at": "2024-12-12T00:00:00.000Z", "published_at": "2024-12-12T00:00:00.000Z", "custom_excerpt": null, "codeinjection_head": null, "codeinjection_foot": null, "custom_template": null, "canonical_url": null, "newsletter_id": null, "show_title_and_feature_image": 1}, {"id": "6899af78064a4f000166c454", "uuid": "914e0dc9-4c5d-4229-967c-c4f462ca1d8a", "title": "GitHub Sponsors", "slug": "github-sponsors", "mobiledoc": null, "lexical": "{\"root\":{\"children\":[{\"type\":\"horizontalrule\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"title: GitHub Sponsors\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Current Sponsors\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"I'm incredibly grateful for the support from my current GitHub Sponsors. Your contributions help me dedicate more time to open source work and make it sustainable. Thank you! 🙏\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":2,\"mode\":\"normal\",\"style\":\"\",\"text\":\"No current sponsors at the moment.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Past Sponsors\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Thank you to all my past sponsors for their support! 🙏\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/1396951?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Sentry\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Sentry\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/getsentry\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/66615189?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Avo\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Avo\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/avo-hq\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/7255?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Sven Schwyn\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Sven Schwyn\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/svoop\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/392754?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Maciej Mensfeld\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Maciej Mensfeld\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/mensfeld\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/284395?v=4\",\"width\":320,\"height\":320,\"title\":\"\",\"alt\":\"Michał Matyas\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Michał Matyas\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/d4rky-pl\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/40342677?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Tom Donarski\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Tom Donarski\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/tomdonarski\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/13313336?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Karafka\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Karafka\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/karafka\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/1032148?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Konstantin Rudy\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Konstantin Rudy\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/k-rudy\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/762159?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Ziyan Junaideen\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Ziyan Junaideen\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/ziyan-junaideen\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/915951?v=4\",\"width\":194,\"height\":194,\"title\":\"\",\"alt\":\"about source GmbH\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"about source GmbH\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/aboutsource\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/12301?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Hiroshi SHIBATA\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Hiroshi SHIBATA\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/hsbt\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/9919?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"GitHub\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"GitHub\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/github\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/102731792?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Prowly\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Prowly\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/prowlycom\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/696?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Caius Durling\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Caius Durling\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/caius\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/769950?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Hasan Kumar\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Hasan Kumar\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/mintuhouse\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/11366263?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Cabourn\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Cabourn\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/cabourn\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/89184835?v=4\",\"width\":300,\"height\":300,\"title\":\"\",\"alt\":\"Culture Amp GitHub Sponsorships\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Culture Amp GitHub Sponsorships\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/cultureamp-sponsorships\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/1100176?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Thomas Klemm\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Thomas Klemm\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/thomasklemm\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/9591402?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Thomas Carr\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Thomas Carr\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/htcarr3\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/645203?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Wilson Silva\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Wilson Silva\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/wilsonsilva\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/859210?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Bohdan V.\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Bohdan V.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/g3d\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/444348?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Yuriy Kharchenko\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Yuriy Kharchenko\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/letmein\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/8088317?v=4\",\"width\":437,\"height\":437,\"title\":\"\",\"alt\":\"Seb Wilgosz\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Seb Wilgosz\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/swilgosz\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/551543?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Eimantas\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Eimantas\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/eimantas\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/17785?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Igor Pstyga\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Igor Pstyga\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/opti\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/6411752?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Marco Roth\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Marco Roth\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/marcoroth\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/5194287?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Gabriel Malaquias\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Gabriel Malaquias\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/GabrielMalakias\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/424326?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"roma\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"roma\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/milushov\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/4575064?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Vladimir Kalinkin\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Vladimir Kalinkin\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/cylon-v\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/109615?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Kamil Skrzypiński\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Kamil Skrzypiński\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/skarlcf\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/474327?v=4\",\"width\":317,\"height\":317,\"title\":\"\",\"alt\":\"Adam Piotrowski\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Adam Piotrowski\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/panSarin\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/71095532?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Scout Monitoring Sponsorships\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Scout Monitoring Sponsorships\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/scoutapm-sponsorships\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/714734?v=4\",\"width\":354,\"height\":354,\"title\":\"\",\"alt\":\"Maarten Claes\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Maarten Claes\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/mcls\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/26220782?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Rostislav Zhuravsky\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Rostislav Zhuravsky\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/woarewe\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/1020124?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Aldis Berjoza\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Aldis Berjoza\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/graudeejs\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/13238?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Ernest Bursa\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Ernest Bursa\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/swistaczek\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/16888495?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Sahil Gadimbayli\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Sahil Gadimbayli\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/gadimbaylisahil\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/241582?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Oleksii Leonov\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Oleksii Leonov\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/oleksii-leonov\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/2687?v=4\",\"width\":400,\"height\":400,\"title\":\"\",\"alt\":\"Ryan Bigg\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Ryan Bigg\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/radar\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/4252?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Peter Berkenbosch\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Peter Berkenbosch\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/peterberkenbosch\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/675705?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Benjamin Klotz\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Benjamin Klotz\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/tak1n\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/683590?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Kirill Zaitsev\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Kirill Zaitsev\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/kzaitsev\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/887264?v=4\",\"width\":392,\"height\":392,\"title\":\"\",\"alt\":\"Igor S. Morozov\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Igor S. Morozov\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/Morozzzko\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/2664467?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Ruslan Tolstov\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Ruslan Tolstov\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/ruslantolstov\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/1223889?v=4\",\"width\":320,\"height\":320,\"title\":\"\",\"alt\":\"Timo Sulg\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Timo Sulg\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/timgluz\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/1167370?v=4\",\"width\":248,\"height\":248,\"title\":\"\",\"alt\":\"jan\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"jan\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/yuszuv\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/6506296?v=4\",\"width\":454,\"height\":454,\"title\":\"\",\"alt\":\"Vasily Kolesnikov\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Vasily Kolesnikov\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/v-kolesnikov\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/561827?v=4\",\"width\":200,\"height\":200,\"title\":\"\",\"alt\":\"Chris Salzberg\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Chris Salzberg\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/shioyama\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/3144140?v=4\",\"width\":460,\"height\":460,\"title\":\"\",\"alt\":\"Jason Charnes\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Jason Charnes\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/jasoncharnes\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/18333420?v=4\",\"width\":313,\"height\":313,\"title\":\"\",\"alt\":\"Kasper Meyer\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Kasper Meyer\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/kaspermeyer\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"image\",\"version\":1,\"src\":\"https://avatars.githubusercontent.com/u/795488?v=4\",\"width\":413,\"height\":413,\"title\":\"\",\"alt\":\"Janko Marohnić\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Janko Marohnić\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"version\":1,\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://github.com/janko\"}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}", "html": "<hr><h2 id=\"title-github-sponsors\">title: GitHub Sponsors</h2><h2 id=\"current-sponsors\">Current Sponsors</h2><p>I'm incredibly grateful for the support from my current GitHub Sponsors. Your contributions help me dedicate more time to open source work and make it sustainable. Thank you! 🙏</p><p><em>No current sponsors at the moment.</em></p><h2 id=\"past-sponsors\">Past Sponsors</h2><p>Thank you to all my past sponsors for their support! 🙏</p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/1396951?v=4\" class=\"kg-image\" alt=\"Sentry\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/getsentry\"><strong>Sentry</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/66615189?v=4\" class=\"kg-image\" alt=\"Avo\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/avo-hq\"><strong>Avo</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/7255?v=4\" class=\"kg-image\" alt=\"Sven Schwyn\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/svoop\"><strong>Sven Schwyn</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/392754?v=4\" class=\"kg-image\" alt=\"Maciej Mensfeld\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/mensfeld\"><strong>Maciej Mensfeld</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/284395?v=4\" class=\"kg-image\" alt=\"Michał Matyas\" loading=\"lazy\" width=\"320\" height=\"320\"></figure><p><a href=\"https://github.com/d4rky-pl\"><strong>Michał Matyas</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/40342677?v=4\" class=\"kg-image\" alt=\"Tom Donarski\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/tomdonarski\"><strong>Tom Donarski</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/13313336?v=4\" class=\"kg-image\" alt=\"Karafka\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/karafka\"><strong>Karafka</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/1032148?v=4\" class=\"kg-image\" alt=\"Konstantin Rudy\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/k-rudy\"><strong>Konstantin Rudy</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/762159?v=4\" class=\"kg-image\" alt=\"Ziyan Junaideen\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/ziyan-junaideen\"><strong>Ziyan Junaideen</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/915951?v=4\" class=\"kg-image\" alt=\"about source GmbH\" loading=\"lazy\" width=\"194\" height=\"194\"></figure><p><a href=\"https://github.com/aboutsource\"><strong>about source GmbH</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/12301?v=4\" class=\"kg-image\" alt=\"Hiroshi SHIBATA\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/hsbt\"><strong>Hiroshi SHIBATA</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/9919?v=4\" class=\"kg-image\" alt=\"GitHub\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/github\"><strong>GitHub</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/102731792?v=4\" class=\"kg-image\" alt=\"Prowly\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/prowlycom\"><strong>Prowly</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/696?v=4\" class=\"kg-image\" alt=\"Caius Durling\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/caius\"><strong>Caius Durling</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/769950?v=4\" class=\"kg-image\" alt=\"Hasan Kumar\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/mintuhouse\"><strong>Hasan Kumar</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/11366263?v=4\" class=\"kg-image\" alt=\"Cabourn\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/cabourn\"><strong>Cabourn</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/89184835?v=4\" class=\"kg-image\" alt=\"Culture Amp GitHub Sponsorships\" loading=\"lazy\" width=\"300\" height=\"300\"></figure><p><a href=\"https://github.com/cultureamp-sponsorships\"><strong>Culture Amp GitHub Sponsorships</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/1100176?v=4\" class=\"kg-image\" alt=\"Thomas Klemm\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/thomasklemm\"><strong>Thomas Klemm</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/9591402?v=4\" class=\"kg-image\" alt=\"Thomas Carr\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/htcarr3\"><strong>Thomas Carr</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/645203?v=4\" class=\"kg-image\" alt=\"Wilson Silva\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/wilsonsilva\"><strong>Wilson Silva</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/859210?v=4\" class=\"kg-image\" alt=\"Bohdan V.\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/g3d\"><strong>Bohdan V.</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/444348?v=4\" class=\"kg-image\" alt=\"Yuriy Kharchenko\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/letmein\"><strong>Yuriy Kharchenko</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/8088317?v=4\" class=\"kg-image\" alt=\"Seb Wilgosz\" loading=\"lazy\" width=\"437\" height=\"437\"></figure><p><a href=\"https://github.com/swilgosz\"><strong>Seb Wilgosz</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/551543?v=4\" class=\"kg-image\" alt=\"Eimantas\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/eimantas\"><strong>Eimantas</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/17785?v=4\" class=\"kg-image\" alt=\"Igor Pstyga\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/opti\"><strong>Igor Pstyga</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/6411752?v=4\" class=\"kg-image\" alt=\"Marco Roth\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/marcoroth\"><strong>Marco Roth</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/5194287?v=4\" class=\"kg-image\" alt=\"Gabriel Malaquias\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/GabrielMalakias\"><strong>Gabriel Malaquias</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/424326?v=4\" class=\"kg-image\" alt=\"roma\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/milushov\"><strong>roma</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/4575064?v=4\" class=\"kg-image\" alt=\"Vladimir Kalinkin\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/cylon-v\"><strong>Vladimir Kalinkin</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/109615?v=4\" class=\"kg-image\" alt=\"Kamil Skrzypiński\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/skarlcf\"><strong>Kamil Skrzypiński</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/474327?v=4\" class=\"kg-image\" alt=\"Adam Piotrowski\" loading=\"lazy\" width=\"317\" height=\"317\"></figure><p><a href=\"https://github.com/panSarin\"><strong>Adam Piotrowski</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/71095532?v=4\" class=\"kg-image\" alt=\"Scout Monitoring Sponsorships\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/scoutapm-sponsorships\"><strong>Scout Monitoring Sponsorships</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/714734?v=4\" class=\"kg-image\" alt=\"Maarten Claes\" loading=\"lazy\" width=\"354\" height=\"354\"></figure><p><a href=\"https://github.com/mcls\"><strong>Maarten Claes</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/26220782?v=4\" class=\"kg-image\" alt=\"Rostislav Zhuravsky\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/woarewe\"><strong>Rostislav Zhuravsky</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/1020124?v=4\" class=\"kg-image\" alt=\"Aldis Berjoza\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/graudeejs\"><strong>Aldis Berjoza</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/13238?v=4\" class=\"kg-image\" alt=\"Ernest Bursa\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/swistaczek\"><strong>Ernest Bursa</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/16888495?v=4\" class=\"kg-image\" alt=\"Sahil Gadimbayli\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/gadimbaylisahil\"><strong>Sahil Gadimbayli</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/241582?v=4\" class=\"kg-image\" alt=\"Oleksii Leonov\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/oleksii-leonov\"><strong>Oleksii Leonov</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/2687?v=4\" class=\"kg-image\" alt=\"Ryan Bigg\" loading=\"lazy\" width=\"400\" height=\"400\"></figure><p><a href=\"https://github.com/radar\"><strong>Ryan Bigg</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/4252?v=4\" class=\"kg-image\" alt=\"Peter Berkenbosch\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/peterberkenbosch\"><strong>Peter Berkenbosch</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/675705?v=4\" class=\"kg-image\" alt=\"Benjamin Klotz\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/tak1n\"><strong>Benjamin Klotz</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/683590?v=4\" class=\"kg-image\" alt=\"Kirill Zaitsev\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/kzaitsev\"><strong>Kirill Zaitsev</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/887264?v=4\" class=\"kg-image\" alt=\"Igor S. Morozov\" loading=\"lazy\" width=\"392\" height=\"392\"></figure><p><a href=\"https://github.com/Morozzzko\"><strong>Igor S. Morozov</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/2664467?v=4\" class=\"kg-image\" alt=\"Ruslan Tolstov\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/ruslantolstov\"><strong>Ruslan Tolstov</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/1223889?v=4\" class=\"kg-image\" alt=\"Timo Sulg\" loading=\"lazy\" width=\"320\" height=\"320\"></figure><p><a href=\"https://github.com/timgluz\"><strong>Timo Sulg</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/1167370?v=4\" class=\"kg-image\" alt=\"jan\" loading=\"lazy\" width=\"248\" height=\"248\"></figure><p><a href=\"https://github.com/yuszuv\"><strong>jan</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/6506296?v=4\" class=\"kg-image\" alt=\"Vasily Kolesnikov\" loading=\"lazy\" width=\"454\" height=\"454\"></figure><p><a href=\"https://github.com/v-kolesnikov\"><strong>Vasily Kolesnikov</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/561827?v=4\" class=\"kg-image\" alt=\"Chris Salzberg\" loading=\"lazy\" width=\"200\" height=\"200\"></figure><p><a href=\"https://github.com/shioyama\"><strong>Chris Salzberg</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/3144140?v=4\" class=\"kg-image\" alt=\"Jason Charnes\" loading=\"lazy\" width=\"460\" height=\"460\"></figure><p><a href=\"https://github.com/jasoncharnes\"><strong>Jason Charnes</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/18333420?v=4\" class=\"kg-image\" alt=\"Kasper Meyer\" loading=\"lazy\" width=\"313\" height=\"313\"></figure><p><a href=\"https://github.com/kaspermeyer\"><strong>Kasper Meyer</strong></a></p><figure class=\"kg-card kg-image-card\"><img src=\"https://avatars.githubusercontent.com/u/795488?v=4\" class=\"kg-image\" alt=\"Janko Marohnić\" loading=\"lazy\" width=\"413\" height=\"413\"></figure><p><a href=\"https://github.com/janko\"><strong>Janko Marohnić</strong></a></p>", "comment_id": "6899af78064a4f000166c454", "plaintext": "title: GitHub Sponsors\n\n\nCurrent Sponsors\n\nI'm incredibly grateful for the support from my current GitHub Sponsors. Your contributions help me dedicate more time to open source work and make it sustainable. Thank you! 🙏\n\nNo current sponsors at the moment.\n\n\nPast Sponsors\n\nThank you to all my past sponsors for their support! 🙏\n\nSentry\n\nAvo\n\nSven Schwyn\n\nMaciej <PERSON>ł <PERSON>\n\nTom Donarski\n\nKarafka\n\nKonstantin <PERSON>\n\nabout source GmbH\n\nHiroshi SHIBATA\n\nGitHub\n\nProwly\n\nCaius Durling\n\nHasan Kumar\n\nCabourn\n\nCulture Amp GitHub Sponsorships\n\nThomas <PERSON>\n\nThomas Carr\n\nWilson Silva\n\nBohdan V.\n\n<PERSON><PERSON>\n\nSeb Wilgosz\n\nEimantas\n\nIgor <PERSON>\n\nGabriel Malaquias\n\nroma\n\nVladimir <PERSON>\n\nAdam <PERSON>ki\n\nScout Monitoring Sponsorships\n\nMaarten Cla<PERSON>rjoza\n\nErnest Bursa\n\nSahil Gadimbayli\n\nOleksii Leonov\n\nRyan <PERSON>g\n\n<PERSON> Berkenbosch\n\nBenjamin Klotz\n\n<PERSON> Zaitsev\n\nIgor S. Morozov\n\nRuslan Tolstov\n\nTimo Sulg\n\njan\n\n<PERSON><PERSON>\n\nJason <PERSON><PERSON><PERSON>\n\n<PERSON><PERSON>", "feature_image": null, "featured": 0, "type": "page", "status": "draft", "locale": null, "visibility": "public", "email_recipient_filter": "all", "created_at": "2025-08-11T08:53:12.000Z", "updated_at": "2025-08-11T08:58:46.000Z", "published_at": null, "custom_excerpt": null, "codeinjection_head": null, "codeinjection_foot": null, "custom_template": null, "canonical_url": null, "newsletter_id": null, "show_title_and_feature_image": 1}], "posts_authors": [{"id": "68969dd9ad492f0008be2862", "post_id": "68969dd9ad492f0008be2861", "author_id": "68969dd8ad492f0008be27d4", "sort_order": 0}, {"id": "6896e0b842a07b0001ad034e", "post_id": "6896e0b842a07b0001ad0347", "author_id": "68969dd8ad492f0008be27d4", "sort_order": 0}, {"id": "6896e0b842a07b0001ad0355", "post_id": "6896e0b842a07b0001ad0348", "author_id": "68969dd8ad492f0008be27d4", "sort_order": 0}, {"id": "6896e0b842a07b0001ad035e", "post_id": "6896e0b842a07b0001ad0349", "author_id": "68969dd8ad492f0008be27d4", "sort_order": 0}, {"id": "6896e29942a07b0001ad0383", "post_id": "6896e29942a07b0001ad0378", "author_id": "68969dd8ad492f0008be27d4", "sort_order": 0}, {"id": "6896e29942a07b0001ad038a", "post_id": "6896e29942a07b0001ad0379", "author_id": "68969dd8ad492f0008be27d4", "sort_order": 0}, {"id": "6896e29942a07b0001ad038d", "post_id": "6896e29942a07b0001ad037a", "author_id": "68969dd8ad492f0008be27d4", "sort_order": 0}, {"id": "6896e29942a07b0001ad0393", "post_id": "6896e29942a07b0001ad037b", "author_id": "68969dd8ad492f0008be27d4", "sort_order": 0}, {"id": "6896e29942a07b0001ad039b", "post_id": "6896e29942a07b0001ad037c", "author_id": "68969dd8ad492f0008be27d4", "sort_order": 0}, {"id": "6899af79064a4f000166c455", "post_id": "6899af78064a4f000166c454", "author_id": "68969dd8ad492f0008be27d4", "sort_order": 0}], "posts_meta": [{"id": "6896e2e242a07b0001ad039d", "post_id": "6896e0b842a07b0001ad0349", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "email_subject": null, "frontmatter": null, "feature_image_alt": null, "feature_image_caption": "<span style=\"white-space: pre-wrap;\">Photo by </span><a href=\"https://unsplash.com/@purzlbaum?utm_source=ghost&amp;utm_medium=referral&amp;utm_campaign=api-credit\"><span style=\"white-space: pre-wrap;\"><PERSON></span></a><span style=\"white-space: pre-wrap;\"> / </span><a href=\"https://unsplash.com/?utm_source=ghost&amp;utm_medium=referral&amp;utm_campaign=api-credit\"><span style=\"white-space: pre-wrap;\">Unsplash</span></a>", "email_only": 0}], "posts_products": [{"id": "6896e0cc42a07b0001ad035f", "post_id": "6896e0b842a07b0001ad0349", "product_id": "68969dd8ad492f0008be27e4", "sort_order": 0}, {"id": "6896e0dd42a07b0001ad0361", "post_id": "6896e0b842a07b0001ad0348", "product_id": "68969dd8ad492f0008be27e4", "sort_order": 0}, {"id": "6896e0e242a07b0001ad0363", "post_id": "6896e0b842a07b0001ad0347", "product_id": "68969dd8ad492f0008be27e4", "sort_order": 0}, {"id": "68999dae2af22c0001e9a84a", "post_id": "68969dd9ad492f0008be2861", "product_id": "68969dd8ad492f0008be27e4", "sort_order": 0}, {"id": "6899af7c064a4f000166c458", "post_id": "6899af78064a4f000166c454", "product_id": "68969dd8ad492f0008be27e4", "sort_order": 0}], "posts_tags": [{"id": "6896e0b842a07b0001ad034a", "post_id": "6896e0b842a07b0001ad0347", "tag_id": "6896e0b842a07b0001ad033b", "sort_order": 0}, {"id": "6896e0b842a07b0001ad034b", "post_id": "6896e0b842a07b0001ad0347", "tag_id": "6896e0b842a07b0001ad033c", "sort_order": 1}, {"id": "6896e0b842a07b0001ad034c", "post_id": "6896e0b842a07b0001ad0347", "tag_id": "6896e0b842a07b0001ad033d", "sort_order": 2}, {"id": "6896e0b842a07b0001ad034d", "post_id": "6896e0b842a07b0001ad0347", "tag_id": "6896e0b842a07b0001ad0346", "sort_order": 3}, {"id": "6896e0b842a07b0001ad034f", "post_id": "6896e0b842a07b0001ad0348", "tag_id": "6896e0b842a07b0001ad033b", "sort_order": 0}, {"id": "6896e0b842a07b0001ad0350", "post_id": "6896e0b842a07b0001ad0348", "tag_id": "6896e0b842a07b0001ad033e", "sort_order": 1}, {"id": "6896e0b842a07b0001ad0351", "post_id": "6896e0b842a07b0001ad0348", "tag_id": "6896e0b842a07b0001ad033f", "sort_order": 2}, {"id": "6896e0b842a07b0001ad0352", "post_id": "6896e0b842a07b0001ad0348", "tag_id": "6896e0b842a07b0001ad0340", "sort_order": 3}, {"id": "6896e0b842a07b0001ad0353", "post_id": "6896e0b842a07b0001ad0348", "tag_id": "6896e0b842a07b0001ad0341", "sort_order": 4}, {"id": "6896e0b842a07b0001ad0354", "post_id": "6896e0b842a07b0001ad0348", "tag_id": "6896e0b842a07b0001ad0346", "sort_order": 5}, {"id": "6896e0b842a07b0001ad0356", "post_id": "6896e0b842a07b0001ad0349", "tag_id": "6896e0b842a07b0001ad033b", "sort_order": 0}, {"id": "6896e0b842a07b0001ad0357", "post_id": "6896e0b842a07b0001ad0349", "tag_id": "6896e0b842a07b0001ad0342", "sort_order": 1}, {"id": "6896e0b842a07b0001ad0358", "post_id": "6896e0b842a07b0001ad0349", "tag_id": "6896e0b842a07b0001ad0343", "sort_order": 2}, {"id": "6896e0b842a07b0001ad0359", "post_id": "6896e0b842a07b0001ad0349", "tag_id": "6896e0b842a07b0001ad0344", "sort_order": 3}, {"id": "6896e0b842a07b0001ad035a", "post_id": "6896e0b842a07b0001ad0349", "tag_id": "6896e0b842a07b0001ad033c", "sort_order": 4}, {"id": "6896e0b842a07b0001ad035b", "post_id": "6896e0b842a07b0001ad0349", "tag_id": "6896e0b842a07b0001ad033d", "sort_order": 5}, {"id": "6896e0b842a07b0001ad035c", "post_id": "6896e0b842a07b0001ad0349", "tag_id": "6896e0b842a07b0001ad0345", "sort_order": 6}, {"id": "6896e29942a07b0001ad037d", "post_id": "6896e29942a07b0001ad0378", "tag_id": "6896e29942a07b0001ad0369", "sort_order": 0}, {"id": "6896e29942a07b0001ad037e", "post_id": "6896e29942a07b0001ad0378", "tag_id": "6896e0b842a07b0001ad033c", "sort_order": 1}, {"id": "6896e29942a07b0001ad037f", "post_id": "6896e29942a07b0001ad0378", "tag_id": "6896e29942a07b0001ad036b", "sort_order": 2}, {"id": "6896e29942a07b0001ad0380", "post_id": "6896e29942a07b0001ad0378", "tag_id": "6896e0b842a07b0001ad033b", "sort_order": 3}, {"id": "6896e29942a07b0001ad0381", "post_id": "6896e29942a07b0001ad0378", "tag_id": "6896e29942a07b0001ad036d", "sort_order": 4}, {"id": "6896e29942a07b0001ad0382", "post_id": "6896e29942a07b0001ad0378", "tag_id": "6896e29942a07b0001ad0377", "sort_order": 5}, {"id": "6896e29942a07b0001ad0384", "post_id": "6896e29942a07b0001ad0379", "tag_id": "6896e29942a07b0001ad0369", "sort_order": 0}, {"id": "6896e29942a07b0001ad0385", "post_id": "6896e29942a07b0001ad0379", "tag_id": "6896e0b842a07b0001ad033c", "sort_order": 1}, {"id": "6896e29942a07b0001ad0386", "post_id": "6896e29942a07b0001ad0379", "tag_id": "6896e29942a07b0001ad036b", "sort_order": 2}, {"id": "6896e29942a07b0001ad0387", "post_id": "6896e29942a07b0001ad0379", "tag_id": "6896e0b842a07b0001ad033b", "sort_order": 3}, {"id": "6896e29942a07b0001ad0388", "post_id": "6896e29942a07b0001ad0379", "tag_id": "6896e29942a07b0001ad036d", "sort_order": 4}, {"id": "6896e29942a07b0001ad0389", "post_id": "6896e29942a07b0001ad0379", "tag_id": "6896e29942a07b0001ad0377", "sort_order": 5}, {"id": "6896e29942a07b0001ad038b", "post_id": "6896e29942a07b0001ad037a", "tag_id": "6896e29942a07b0001ad036e", "sort_order": 0}, {"id": "6896e29942a07b0001ad038c", "post_id": "6896e29942a07b0001ad037a", "tag_id": "6896e29942a07b0001ad0377", "sort_order": 1}, {"id": "6896e29942a07b0001ad038e", "post_id": "6896e29942a07b0001ad037b", "tag_id": "6896e29942a07b0001ad036f", "sort_order": 0}, {"id": "6896e29942a07b0001ad038f", "post_id": "6896e29942a07b0001ad037b", "tag_id": "6896e0b842a07b0001ad033b", "sort_order": 1}, {"id": "6896e29942a07b0001ad0390", "post_id": "6896e29942a07b0001ad037b", "tag_id": "6896e0b842a07b0001ad033e", "sort_order": 2}, {"id": "6896e29942a07b0001ad0391", "post_id": "6896e29942a07b0001ad037b", "tag_id": "6896e29942a07b0001ad0371", "sort_order": 3}, {"id": "6896e29942a07b0001ad0392", "post_id": "6896e29942a07b0001ad037b", "tag_id": "6896e29942a07b0001ad0377", "sort_order": 4}, {"id": "6896e29942a07b0001ad0394", "post_id": "6896e29942a07b0001ad037c", "tag_id": "6896e29942a07b0001ad0372", "sort_order": 0}, {"id": "6896e29942a07b0001ad0395", "post_id": "6896e29942a07b0001ad037c", "tag_id": "6896e0b842a07b0001ad033c", "sort_order": 1}, {"id": "6896e29942a07b0001ad0396", "post_id": "6896e29942a07b0001ad037c", "tag_id": "6896e0b842a07b0001ad033d", "sort_order": 2}, {"id": "6896e29942a07b0001ad0397", "post_id": "6896e29942a07b0001ad037c", "tag_id": "6896e29942a07b0001ad0374", "sort_order": 3}, {"id": "6896e29942a07b0001ad0398", "post_id": "6896e29942a07b0001ad037c", "tag_id": "6896e29942a07b0001ad0375", "sort_order": 4}, {"id": "6896e29942a07b0001ad0399", "post_id": "6896e29942a07b0001ad037c", "tag_id": "6896e29942a07b0001ad0376", "sort_order": 5}, {"id": "6896e29942a07b0001ad039a", "post_id": "6896e29942a07b0001ad037c", "tag_id": "6896e29942a07b0001ad0377", "sort_order": 6}, {"id": "68990c75cf021a000108c0ff", "post_id": "6896e0b842a07b0001ad0347", "tag_id": "68990c75cf021a000108c0fe", "sort_order": 0}, {"id": "68990c75cf021a000108c100", "post_id": "6896e0b842a07b0001ad0348", "tag_id": "68990c75cf021a000108c0fe", "sort_order": 0}, {"id": "68990c75cf021a000108c101", "post_id": "6896e0b842a07b0001ad0349", "tag_id": "68990c75cf021a000108c0fe", "sort_order": 0}, {"id": "68990c75cf021a000108c102", "post_id": "6896e29942a07b0001ad0378", "tag_id": "68990c75cf021a000108c0fe", "sort_order": 0}, {"id": "68990c75cf021a000108c103", "post_id": "6896e29942a07b0001ad0379", "tag_id": "68990c75cf021a000108c0fe", "sort_order": 0}, {"id": "68990c75cf021a000108c104", "post_id": "6896e29942a07b0001ad037a", "tag_id": "68990c75cf021a000108c0fe", "sort_order": 0}, {"id": "68990c75cf021a000108c105", "post_id": "6896e29942a07b0001ad037b", "tag_id": "68990c75cf021a000108c0fe", "sort_order": 0}, {"id": "68990c75cf021a000108c106", "post_id": "6896e29942a07b0001ad037c", "tag_id": "68990c75cf021a000108c0fe", "sort_order": 0}], "products": [{"id": "68969dd8ad492f0008be27e3", "name": "Free", "slug": "free", "active": 1, "welcome_page_url": null, "visibility": "public", "trial_days": 0, "description": null, "type": "free", "currency": null, "monthly_price": null, "yearly_price": null, "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T01:01:12.000Z", "monthly_price_id": null, "yearly_price_id": null}, {"id": "68969dd8ad492f0008be27e4", "name": "solnic.dev", "slug": "default-product", "active": 1, "welcome_page_url": null, "visibility": "public", "trial_days": 0, "description": null, "type": "paid", "currency": "USD", "monthly_price": 500, "yearly_price": 5000, "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T05:10:23.000Z", "monthly_price_id": null, "yearly_price_id": null}], "products_benefits": [], "roles": [{"id": "68969dd8ad492f0008be27d5", "name": "Administrator", "description": "Administrators", "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T01:01:12.000Z"}, {"id": "68969dd8ad492f0008be27d6", "name": "Editor", "description": "Editors", "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T01:01:12.000Z"}, {"id": "68969dd8ad492f0008be27d7", "name": "Author", "description": "Authors", "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T01:01:12.000Z"}, {"id": "68969dd8ad492f0008be27d8", "name": "Contributor", "description": "Contributors", "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T01:01:12.000Z"}, {"id": "68969dd8ad492f0008be27d9", "name": "Owner", "description": "Blog Owner", "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T01:01:12.000Z"}, {"id": "68969dd8ad492f0008be27da", "name": "Admin Integration", "description": "External Apps", "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T01:01:12.000Z"}, {"id": "68969dd8ad492f0008be27db", "name": "Ghost Explore Integration", "description": "Internal Integration for the Ghost Explore directory", "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T01:01:12.000Z"}, {"id": "68969dd8ad492f0008be27dc", "name": "Self-Serve Migration Integration", "description": "Internal Integration for the Self-Serve migration tool", "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T01:01:12.000Z"}, {"id": "68969dd8ad492f0008be27dd", "name": "DB Backup Integration", "description": "Internal DB Backup Client", "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T01:01:12.000Z"}, {"id": "68969dd8ad492f0008be27de", "name": "Scheduler Integration", "description": "Internal Scheduler Client", "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T01:01:12.000Z"}, {"id": "68969dd8ad492f0008be27df", "name": "Super Editor", "description": "Super Editors", "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T01:01:12.000Z"}], "roles_users": [{"id": "68969dd8ad492f0008be27e0", "role_id": "68969dd8ad492f0008be27d9", "user_id": "68969dd8ad492f0008be27d4"}], "settings": [{"id": "68969ddb34b12e0001295f48", "group": "core", "key": "last_mentions_report_email_timestamp", "value": null, "type": "number", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f49", "group": "core", "key": "db_hash", "value": "2c328c0b-fe5e-4776-8225-2d515f90afc4", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f4a", "group": "core", "key": "routes_hash", "value": "3d180d52c663d173a6be791ef411ed01", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T01:01:16.000Z"}, {"id": "68969ddb34b12e0001295f4b", "group": "core", "key": "next_update_check", "value": "1754849288", "type": "number", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T18:08:07.000Z"}, {"id": "68969ddb34b12e0001295f4c", "group": "core", "key": "notifications", "value": "[]", "type": "array", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f4d", "group": "core", "key": "version_notifications", "value": "[]", "type": "array", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f4e", "group": "core", "key": "admin_session_secret", "value": "6c6fcc36903be616636867607f7caa880683915a53fb9229bf2cbd35fed4a9e4", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f4f", "group": "core", "key": "theme_session_secret", "value": "8079deb6ff6894d63d54b8890b7f71de3dbed52cf4f79c9c73af768152ef115b", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f50", "group": "core", "key": "ghost_public_key", "value": "-----BEGIN RSA PUBLIC KEY-----\nMIGJAoGBAK+WT/MppnFTsrTG8SOhVk/sKf/z0uBcSGQgtm9qTYaEvPVhOJ1Z3M2bq9TBzhcW\nttW2bhkA72spi03d+qDgpm0knKMqpA2SeVbPaNcqkl8HLEf+jsPkR/dLOMNPJnrTE7lVHl+e\njIJMdJeRlHn/b1eOoTkM39ulXb/GE9SP5lA3AgMBAAE=\n-----END RSA PUBLIC KEY-----\n", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f51", "group": "core", "key": "ghost_private_key", "value": "-----B<PERSON><PERSON> RSA PRIVATE KEY-----\nMIICXQIBAAKBgQCvlk/zKaZxU7K0xvEjoVZP7Cn/89LgXEhkILZvak2GhLz1YTidWdzNm6vU\nwc4XFrbVtm4ZAO9rKYtN3fqg4KZtJJyjKqQNknlWz2jXKpJfByxH/o7D5Ef3SzjDTyZ60xO5\nVR5fnoyCTHSXkZR5/29XjqE5DN/bpV2/xhPUj+ZQNwIDAQABAoGAZuMLIg8ryogi1S8lqZ0m\nOFrIGYTD8lfgy1sny41ctBAfsSEwzXMUoR4wx9BwdLMIm530UY5svDxxtkT2UUKSAbXK/Mw1\nuYESCDXRI+evRCMrdySKOTHhMQJeMnHrP1s7CQ5zxZAWAnPHXIPq6yzZcGuTU44E74Osonw8\nfNUtG6ECQQDXuntBJCBb1HHZAfXOXoIaeT8v45S/KmPM8XLPWx9ID0PyxBMutrLutbz/MMGO\nWcY7EHlaIXO5Z/+QS+l/7wyPAkEA0F2Ba2H/Ks7NvPFJzXSe7Mgw86vFxsVtmtel7xjnsUr1\nqTJk1icYdQaKm4NI7djUPHYuw3JAxeTv8s36OUwl2QJBAKpOY+OHO6yDSOWCsKUc4yU3NvT9\ne353XpZ6vqKIU2e19XR/6ozkWtosy2nC+esBYIdceBD5e7yRJ6ao50vCUm0CQHjPHjCSmw3I\n5gS8qZvUGCkVUTyWxY8f/RKgIfdlK0DiQ1kcfNalyEphxo+wKOKuqy97HlX3spKK0Iyo3IHI\nItECQQCESUasP2S5N4HlQGUoOUYcsBsWrQvuyNEQ47JAv/qzxRY6TiMMix8VB2VDyDxD8au1\ncy89PCLtxipxTZJUT+rY\n-----END RSA PRIVATE KEY-----\n", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f52", "group": "core", "key": "members_public_key", "value": "-----BEGIN RSA PUBLIC KEY-----\nMIGJAoGBAJy0+jSQYwkxCdtLL3mBGiAnMcKRTdlHGR/xXamffJsSTCAG9CBrVr7sIWZSap3N\nBTCFNAxkicS1JQRXbH2JfHwG02AnK3BJ/z91hY3iLWAyw+tmHv4+moIjT6NBbJPZGifH1O3H\nYe3sKeVMSY16Ql5w6cJAlGbzbXMiJuLvmlh7AgMBAAE=\n-----END RSA PUBLIC KEY-----\n", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f53", "group": "core", "key": "members_private_key", "value": "-----BEGIN RSA PRIVATE KEY-----\nMIICXAIBAAKBgQCctPo0kGMJMQnbSy95gRogJzHCkU3ZRxkf8V2pn3ybEkwgBvQga1a+7CFm\nUmqdzQUwhTQMZInEtSUEV2x9iXx8BtNgJytwSf8/dYWN4i1gMsPrZh7+PpqCI0+jQWyT2Ron\nx9Ttx2Ht7CnlTEmNekJecOnCQJRm821zIibi75pYewIDAQABAoGARGXls5217GQgFjxp21HT\nUmdpG7CKVyTmPX//TrMTEyhYW75NPf073pd4OI4OhRPgiP4K6sHTNC2Qm6WA8AZbc6Q5bfnj\n+Q7y7FuEg2UZ2byVye6exGEfXDUJbwdmxMsIsyOuOo5+PiCe2gDHwPg9uUvPtTBrqo0gooTO\n3+P/w5kCQQDJosB0B3ggFIriPNsl6qCor3rcCX4R6ANLAG9GqJaWTwbElYONlr5N/RgMPCGM\nxaZ2oYNvk4whTTFbcJt0YmjFAkEAxvUpYnz7PjkuP6JhumWkyRRavBr4BKRFlSWPfVBDA9RW\nH91oMlvEsM7aexgnfbOwxwmUwdXTbi0u44sy+GFQPwJAeov++cX8XUr+6edgrde+0ybrvcXL\nKGjhb3kRKFmYRDw9eccBEFG40b9sGSuGnH8X3+vj7TE4rpiy0cdLw913nQJBAAnrAMQjHNcu\nPIr5ikujc4J+nENSSNjaY4ma1LtXe++97wkpnV8dSgHSXfvS6W0Pvxk89ghvo1mTsQmwz7qo\nBzsCQGolGP6q4Q04H5XH10mGL8YRC/mpOaS9uYrz5pmcYFpHSUP4GiYwk0rPIfmIycPtRZ2u\nYW+iTlWC9UhCSuDPgtI=\n-----END RSA PRIVATE KEY-----\n", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f54", "group": "core", "key": "members_email_auth_secret", "value": "593f38cf991522c7e4a3272e39c50687af65f5f84d933a362fd56d2498e4e5f90a4a4ed58cb3b311f3c41697bcf1530eba96533227bc0bdc29a66d45a7c43b0a", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f57", "group": "core", "key": "site_uuid", "value": "ac275cca-46d2-4d35-8057-04abbcea478d", "type": "string", "flags": "PUBLIC,RO", "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f58", "group": "site", "key": "title", "value": "solnic.dev", "type": "string", "flags": "PUBLIC", "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T05:10:23.000Z"}, {"id": "68969ddb34b12e0001295f59", "group": "site", "key": "description", "value": "<PERSON><PERSON><PERSON>・<PERSON>・Programming・Open Source・AI", "type": "string", "flags": "PUBLIC", "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-10T21:32:07.000Z"}, {"id": "68969ddb34b12e0001295f5a", "group": "site", "key": "heading_font", "value": "", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f5b", "group": "site", "key": "body_font", "value": "Fira Mono", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T05:11:39.000Z"}, {"id": "68969ddb34b12e0001295f5c", "group": "site", "key": "logo", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-11T08:48:45.000Z"}, {"id": "68969ddb34b12e0001295f5d", "group": "site", "key": "cover_image", "value": "https://static.ghost.org/v5.0.0/images/publication-cover.jpg", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f5e", "group": "site", "key": "icon", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-11T08:48:45.000Z"}, {"id": "68969ddb34b12e0001295f5f", "group": "site", "key": "accent_color", "value": "#d61462", "type": "string", "flags": "PUBLIC", "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-11T08:49:39.000Z"}, {"id": "68969ddb34b12e0001295f60", "group": "site", "key": "locale", "value": "en", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f61", "group": "site", "key": "timezone", "value": "Europe/Warsaw", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T05:19:17.000Z"}, {"id": "68969ddb34b12e0001295f62", "group": "site", "key": "codeinjection_head", "value": "<script src=\"https://cdn.jsdelivr.net/npm/prismjs/prism.min.js\" defer></script>\n<script src=\"https://cdn.jsdelivr.net/npm/prismjs/plugins/autoloader/prism-autoloader.min.js\" defer></script>\n<link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/prismjs/themes/prism.min.css\">", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T05:18:09.000Z"}, {"id": "68969ddb34b12e0001295f63", "group": "site", "key": "codeinjection_foot", "value": "", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f64", "group": "site", "key": "facebook", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-10T21:13:27.000Z"}, {"id": "68969ddb34b12e0001295f65", "group": "site", "key": "twitter", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-10T21:13:27.000Z"}, {"id": "68969ddb34b12e0001295f66", "group": "site", "key": "navigation", "value": "[{\"label\":\"Home\",\"url\":\"/\"},{\"label\":\"About\",\"url\":\"/about/\"}]", "type": "array", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f67", "group": "site", "key": "secondary_navigation", "value": "[{\"label\":\"Sign up\",\"url\":\"#/portal/\"}]", "type": "array", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f68", "group": "site", "key": "meta_title", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f69", "group": "site", "key": "meta_description", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f6a", "group": "site", "key": "og_image", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f6b", "group": "site", "key": "og_title", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f6c", "group": "site", "key": "og_description", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f6d", "group": "site", "key": "twitter_image", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f6e", "group": "site", "key": "twitter_title", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f6f", "group": "site", "key": "twitter_description", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f70", "group": "theme", "key": "active_theme", "value": "solo", "type": "string", "flags": "RO", "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-11T08:32:27.000Z"}, {"id": "68969ddb34b12e0001295f71", "group": "private", "key": "is_private", "value": "false", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f72", "group": "private", "key": "password", "value": "", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f73", "group": "private", "key": "public_hash", "value": "e1398e3935dc9e5da6217fefb72e4c", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f74", "group": "members", "key": "default_content_visibility", "value": "public", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f75", "group": "members", "key": "default_content_visibility_tiers", "value": "[]", "type": "array", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f76", "group": "members", "key": "members_signup_access", "value": "all", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f77", "group": "members", "key": "members_support_address", "value": "nor<PERSON>ly", "type": "string", "flags": "PUBLIC,RO", "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f7a", "group": "members", "key": "stripe_plans", "value": "[]", "type": "array", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f7d", "group": "members", "key": "stripe_connect_livemode", "value": null, "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f7e", "group": "members", "key": "stripe_connect_display_name", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f80", "group": "members", "key": "members_monthly_price_id", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f81", "group": "members", "key": "members_yearly_price_id", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f82", "group": "members", "key": "members_track_sources", "value": "true", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f83", "group": "members", "key": "blocked_email_domains", "value": "[]", "type": "array", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f84", "group": "portal", "key": "portal_name", "value": "true", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f85", "group": "portal", "key": "portal_button", "value": "false", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f86", "group": "portal", "key": "portal_plans", "value": "[\"free\"]", "type": "array", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f87", "group": "portal", "key": "portal_default_plan", "value": "yearly", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f88", "group": "portal", "key": "portal_products", "value": "[]", "type": "array", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f89", "group": "portal", "key": "portal_button_style", "value": "icon-and-text", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f8a", "group": "portal", "key": "portal_button_icon", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f8b", "group": "portal", "key": "portal_button_signup_text", "value": "Subscribe", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f8c", "group": "portal", "key": "portal_signup_terms_html", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f8d", "group": "portal", "key": "portal_signup_checkbox_required", "value": "false", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f8e", "group": "email", "key": "mailgun_domain", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f8f", "group": "email", "key": "mailgun_api_key", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f90", "group": "email", "key": "mailgun_base_url", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f91", "group": "email", "key": "email_track_opens", "value": "true", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f92", "group": "email", "key": "email_track_clicks", "value": "true", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f94", "group": "firstpromoter", "key": "firstpromoter", "value": "false", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f95", "group": "firstpromoter", "key": "firstpromoter_id", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f96", "group": "labs", "key": "labs", "value": "{}", "type": "object", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f97", "group": "slack", "key": "slack_url", "value": "", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f98", "group": "slack", "key": "slack_username", "value": "Ghost", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f99", "group": "unsplash", "key": "unsplash", "value": "true", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f9a", "group": "views", "key": "shared_views", "value": "[]", "type": "array", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f9b", "group": "editor", "key": "editor_default_email_recipients", "value": "visibility", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f9c", "group": "editor", "key": "editor_default_email_recipients_filter", "value": "all", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f9d", "group": "announcement", "key": "announcement_content", "value": null, "type": "string", "flags": "PUBLIC", "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f9e", "group": "announcement", "key": "announcement_visibility", "value": "[]", "type": "array", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295f9f", "group": "announcement", "key": "announcement_background", "value": "dark", "type": "string", "flags": "PUBLIC", "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295fa0", "group": "comments", "key": "comments_enabled", "value": "off", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295fa1", "group": "analytics", "key": "outbound_link_tagging", "value": "true", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295fa2", "group": "analytics", "key": "web_analytics", "value": "true", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295fa3", "group": "pintura", "key": "pintura", "value": "true", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295fa4", "group": "pintura", "key": "pintura_js_url", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295fa5", "group": "pintura", "key": "pintura_css_url", "value": null, "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295fa6", "group": "donations", "key": "donations_currency", "value": "USD", "type": "string", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295fa7", "group": "donations", "key": "donations_suggested_amount", "value": "500", "type": "number", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295fa8", "group": "recommendations", "key": "recommendations_enabled", "value": "false", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295fa9", "group": "security", "key": "require_email_mfa", "value": "0", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295faa", "group": "social_web", "key": "social_web", "value": "true", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295fab", "group": "explore", "key": "explore_ping", "value": "true", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}, {"id": "68969ddb34b12e0001295fac", "group": "explore", "key": "explore_ping_growth", "value": "false", "type": "boolean", "flags": null, "created_at": "2025-08-09T02:01:15.000Z", "updated_at": "2025-08-09T02:01:15.000Z"}], "snippets": [], "stripe_prices": [], "stripe_products": [], "tags": [{"id": "68969dd8ad492f0008be27e6", "name": "News", "slug": "news", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-09T01:01:12.000Z"}, {"id": "6896e0b842a07b0001ad033b", "name": "elixir", "slug": "elixir", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:45:41.000Z", "updated_at": "2025-08-09T05:45:41.000Z"}, {"id": "6896e0b842a07b0001ad033c", "name": "opensource", "slug": "opensource", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:45:41.000Z", "updated_at": "2025-08-09T05:45:41.000Z"}, {"id": "6896e0b842a07b0001ad033d", "name": "announcement", "slug": "announcement", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:45:41.000Z", "updated_at": "2025-08-09T05:45:41.000Z"}, {"id": "6896e0b842a07b0001ad033e", "name": "testing", "slug": "testing", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:45:41.000Z", "updated_at": "2025-08-09T05:45:41.000Z"}, {"id": "6896e0b842a07b0001ad033f", "name": "exunit", "slug": "exunit", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:45:41.000Z", "updated_at": "2025-08-09T05:45:41.000Z"}, {"id": "6896e0b842a07b0001ad0340", "name": "logging", "slug": "logging", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:45:41.000Z", "updated_at": "2025-08-09T05:45:41.000Z"}, {"id": "6896e0b842a07b0001ad0341", "name": "til", "slug": "til", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:45:41.000Z", "updated_at": "2025-08-09T05:45:41.000Z"}, {"id": "6896e0b842a07b0001ad0342", "name": "ecto", "slug": "ecto", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:45:41.000Z", "updated_at": "2025-08-09T05:45:41.000Z"}, {"id": "6896e0b842a07b0001ad0343", "name": "postgresql", "slug": "postgresql", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:45:41.000Z", "updated_at": "2025-08-09T05:45:41.000Z"}, {"id": "6896e0b842a07b0001ad0344", "name": "sqlite", "slug": "sqlite", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:45:41.000Z", "updated_at": "2025-08-09T05:45:41.000Z"}, {"id": "6896e0b842a07b0001ad0345", "name": "blog", "slug": "blog", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:45:41.000Z", "updated_at": "2025-08-09T05:45:41.000Z"}, {"id": "6896e0b842a07b0001ad0346", "name": "#Import 2025-08-09 07:46", "slug": "hash-import-2025-08-09-07-46", "description": null, "feature_image": null, "parent_id": null, "visibility": "internal", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:46:32.000Z", "updated_at": "2025-08-09T05:46:32.000Z"}, {"id": "6896e29942a07b0001ad0369", "name": "library", "slug": "library", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:52:58.000Z", "updated_at": "2025-08-09T05:52:58.000Z"}, {"id": "6896e29942a07b0001ad036b", "name": "data-structures", "slug": "data-structures", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:52:58.000Z", "updated_at": "2025-08-09T05:52:58.000Z"}, {"id": "6896e29942a07b0001ad036d", "name": "validation", "slug": "validation", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:52:58.000Z", "updated_at": "2025-08-09T05:52:58.000Z"}, {"id": "6896e29942a07b0001ad036e", "name": "personal", "slug": "personal", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:52:58.000Z", "updated_at": "2025-08-09T05:52:58.000Z"}, {"id": "6896e29942a07b0001ad036f", "name": "tdd", "slug": "tdd", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:52:58.000Z", "updated_at": "2025-08-09T05:52:58.000Z"}, {"id": "6896e29942a07b0001ad0371", "name": "vscode", "slug": "vscode", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:52:58.000Z", "updated_at": "2025-08-09T05:52:58.000Z"}, {"id": "6896e29942a07b0001ad0372", "name": "ruby", "slug": "ruby", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:52:58.000Z", "updated_at": "2025-08-09T05:52:58.000Z"}, {"id": "6896e29942a07b0001ad0374", "name": "hanami", "slug": "hanami", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:52:58.000Z", "updated_at": "2025-08-09T05:52:58.000Z"}, {"id": "6896e29942a07b0001ad0375", "name": "dry-rb", "slug": "dry-rb", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:52:58.000Z", "updated_at": "2025-08-09T05:52:58.000Z"}, {"id": "6896e29942a07b0001ad0376", "name": "rom-rb", "slug": "rom-rb", "description": null, "feature_image": null, "parent_id": null, "visibility": "public", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:52:58.000Z", "updated_at": "2025-08-09T05:52:58.000Z"}, {"id": "6896e29942a07b0001ad0377", "name": "#Import 2025-08-09 07:54", "slug": "hash-import-2025-08-09-07-54", "description": null, "feature_image": null, "parent_id": null, "visibility": "internal", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-09T05:54:33.000Z", "updated_at": "2025-08-09T05:54:33.000Z"}, {"id": "68990c75cf021a000108c0fe", "name": "#peter-solnica", "slug": "hash-peter-solnica", "description": null, "feature_image": null, "parent_id": null, "visibility": "internal", "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "codeinjection_head": null, "codeinjection_foot": null, "canonical_url": null, "accent_color": null, "created_at": "2025-08-10T21:17:41.000Z", "updated_at": "2025-08-10T21:17:41.000Z"}], "users": [{"id": "68969dd8ad492f0008be27d4", "name": "<PERSON>", "slug": "peter", "password": "$2a$10$gEwhWXlFS0VDyGRzXy9uN.lhVEYcwa9Tyx9hoQ/lYHp9EQLriQKpe", "email": "<EMAIL>", "profile_image": "https://www.gravatar.com/avatar/90aa8cd2dd794b4767885fd72e89d609?s=250&r=x&d=mp", "cover_image": null, "bio": null, "website": null, "location": null, "facebook": null, "twitter": null, "threads": null, "bluesky": null, "mastodon": null, "tiktok": null, "youtube": null, "instagram": null, "linkedin": null, "accessibility": "{\"onboarding\":{\"completedSteps\":[\"customize-design\"],\"checklistState\":\"dismissed\"},\"whatsNew\":{\"lastSeenDate\":\"2025-08-04T08:10:56.000+00:00\"},\"nightShift\":true,\"apOnboarding\":{\"welcomeStepsFinished\":true,\"exploreExplainerClosed\":true}}", "status": "active", "locale": null, "visibility": "public", "meta_title": null, "meta_description": null, "tour": null, "last_seen": "2025-08-11T08:58:46.000Z", "comment_notifications": 1, "free_member_signup_notification": 1, "paid_subscription_started_notification": 1, "paid_subscription_canceled_notification": 0, "mention_notifications": 1, "recommendation_notifications": 1, "milestone_notifications": 1, "donation_notifications": 1, "created_at": "2025-08-09T01:01:12.000Z", "updated_at": "2025-08-11T08:58:46.000Z"}]}}]}