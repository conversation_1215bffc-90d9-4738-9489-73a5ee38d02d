#!/usr/bin/env ruby

require "yaml"
require "json"
require "http"
require "byebug"

KEYS = %w[login name avatar_url html_url blog twitter_username]

INPUT_FILE = "data/sponsors.yml"
OUTPUT_FILE = "data/users.yml"

sponsors = YAML.load_file(INPUT_FILE)

refreshed = sponsors
  .map { |sponsor|
    name = sponsor["login"]

    if sponsor["name"]
      puts "Skipping #{name}"
      sponsor
    else
      puts "Grabbing #{name}..."
      user = JSON.parse(HTTP.get("https://api.github.com/users/#{name}").body)
      sleep 1
      sponsor.merge(user.slice(*KEYS))
    end
  }
  .sort_by { |sponsor| sponsor["login"] }

puts "Updating #{OUTPUT_FILE} with #{refreshed.size} entries"

File.write(OUTPUT_FILE, YAML.dump(refreshed))

puts "Done!"
