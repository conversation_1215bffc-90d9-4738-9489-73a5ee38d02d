#!/usr/bin/env ruby

require "yaml"
require "byebug"

KEYS = %w[login avatar_url html_url blog twitter_username]

INPUT_FILE = "data/users.yml"

sponsors = YAML
  .load_file(INPUT_FILE)
  .reject { |sponsor| sponsor["private"] }
  .sort_by { |sponsor| sponsor["name"].downcase }

sponsors.each do |sponsor|
  next if sponsor["private"]
  name = sponsor["name"]
  link = sponsor["blog"].empty? ? sponsor["html_url"] : sponsor["blog"]

  puts "- [#{name}](#{link})"
end
