---
title: Taking a Break From OSS
date: '2013-10-17'
tags: archives, blog, oss, personal
slug: taking-a-break-from-oss
seriesSlug: archives
domain: solnic.dev
hideFromHashnodeCommunity: true
---

Last two years have been pretty intensive for me. I became a husband, a father, a co-founder of [Powow](http://www.powow.no) and during that time I tried to contribute as much as I could to OSS. I was mostly focused on [Virtus](https://github.com/solnic/virtus) and [Ruby Object Mapper](https://github.com/rom-rb) which consumed a lot of my time and energy.

I thought I could go on but I was mistaken. I’ve burned out and decided to take a break from my OSS activities and focus on my family and daily work.

I’m really happy we managed to push the first version of ROM this year and just yesterday I [released Virtus 1.0.0](/2013/10/16/virtus-1-0-0-released.html). It’s probably not the best moment to take a break but I really reached my limits and I need to step back for a while.

Here’s what I decided to do. As of today:

- I won’t be monitoring github
- I won’t be replying to emails from github because I’ve set up a filter and all emails from github are going to my archive
- I won’t be in the #rom-rb irc channel
- I’ll try to avoid twitter ;)

I know this sounds a bit drastic but I don’t see any other way to do it. I feel passionate about OSS and it’s a bit like a drug for me so I need to stay away as much as possible otherwise I’ll get sucked in instantly.

While I’m “gone” I plan to blog a bit more though, hopefully you should see some new posts here. I’d love to finally finish the [“Get Rid of That Code Smell”](/2012/03/30/get-rid-of-that-code-smell.html) series and write more about my experiences while working on ROM.

Virtus issues will be handled by [elskwid](https://github.com/elskwid) who offered help (thank you!). ROM is under active development by the rest of the team, so don’t worry about it.

Thanks to everyone who I had the opportunity to work with and thanks for all the support - it means a lot to me.

I should be back before end of the year.

Cheers!
