---
title: "jQuery vs Prototype - part II"
datePublished: Sun Feb 03 2008 00:00:00 GMT+0000 (Coordinated Universal Time)
cuid: clo2s1t0o000g09jo33hmfuur
slug: jquery-vs-prototype-part-ii

---

Recently, new versions of jQuery and Prototype have been released – it’s a perfect moment for a part number 2. On the official Prototype blog we [can read](http://www.prototypejs.org/2008/1/25/prototype-1-6-0-2-bug-fixes-performance-improvements-and-security) that the general performance of CSS selectors is now improved, unfortunately only for Safari 3, but Element#up/#down/#next/#previous should now be faster on all browsers, it’s a good news as they were really slow. On the other hand we have jQ<PERSON>y [official announcement](http://jquery.com/blog/2008/01/15/jquery-122-2nd-birthday-present) with information that jQ<PERSON>y is now 300% faster – we’ll see!

This time I made a step forward and decided to use a custom JavaScript-based testing environment instead of running tests using Firebug profiler. The obvious advantage is that I was able to run all the tests on 4 different browsers. New test cases aren’t much different then in the first part, let’s say it’s a modification of the previous ones with some extra operations and a little more complex HTML structure.

### Test environment setup

Libraries:

* jQuery 1.2.2
    
* Prototype *******
    

All the tests were run on the following browsers:

* Firefox ********
    
* Konqueror 4.00.00
    
* Opera 9.50\_beta1
    
* Internet Explorer 7 (**but using Windows on VirtualBox!**)
    

A tiny piece of JavaScript code is responsible for running the tests, each operation is called only once inside a try-catch block, so the essential part looks like this:

```elixir
    try {
      var start = new Date;
      test();
      var end = new Date - start;
      this.writeResults(test, end);
    } catch(e) {
      test.resultCell.innerHTML = 'Exception caught: '+e.message+'';
    }
```

There is a 3 seconds break between each test run, results are automatically inserted into the results table. If you want, you can check it out on your own, just go [right here](http://solnic.codes/test_runner/index.html) and hit the ‘run tests!’ button.

### The results

I’m happy to see that all tests pass on the latest Konqueror, previous version from KDE3 fails on some Prototype tests. I don’t own Mac, so you won’t see Safari results here, although I’ve run the tests on my friend’s MacBook with very similar hardware as my laptop has (Intel Core Duo 2ghz + 2 gigs of RAM), and it was faster even then Konqueror (no, it doesn’t mean his MacBook is faster then my laptop!!!! ;)).

I’ve run everything 3 times, here are average results in ms:

| # | Library | Test | Firefox | Konqueror | IE7 | Opera |
| --- | --- | --- | --- | --- | --- | --- |
| **1** | jQuery | “\` erb $(‘td.counter’).addClass(‘marked’) ”\` | 96.6 | 32.3 | 70 | 37 |
| Prototype | “\` erb $$(‘td.counter’).each(function(el){el.addClassName(‘marked’)}) ”\` | 108.3 | 49.6 | 858 | 75.7 |
| **2** | jQuery | “\` erb$(‘td.counter span.special’).removeClass(‘special’)”\` | 62 | 23.6 | 46.6 | 25.6 |
| Prototype | “\` erb $$(‘td.counter span.special’).each(function(el) {el.removeClassName(‘special’)}) ”\` | 28 | 23.7 | 167 | 24.7 |
| **3** | jQuery | “\` erb$(‘td.content span.odd’).css(‘color’, ‘red’)”\` | 124.7 | 40.3 | 63.7 | 38.3 |
| Prototype | “\` erb$$(‘td.content span.odd’).each(function(el) {  
el.setStyle(‘color: red’)  
})”\` | 55.7 | 31 | 297 | 33.7 |
| **4** | jQuery | “\` erb $(‘td.content span.even’).before(‘text’) ”\` | 382.7 | 177.3 | 373.7 | 205.3 |
| Prototype | “\` erb $$(‘td.content span.even’).each(function(el) { el.insert({before:‘text’}) }) ”\` | 359 | 90.7 | 527 | 138.7 |
| **5** | jQuery | “\` erb$(‘td.content h3’).show()”\` | 178.7 | 227.7 | 83.3 | 1161.7 |
| Prototype | “\` erb$$(‘td.content h3’).each(Element.show)”\` | 38 | 21 | 250.7 | 19 |
| **6** | jQuery | “\` erb$(‘div.special’).hide()”\` | 90 | 81.3 | 33.7 | 375.3 |
| Prototype | “\` erb$$(‘div.special’).each(Element.hide)”\` | 18 | 7 | 73.3 | 12 |
| **7** | jQuery | “\` erb$(‘div.special, td.content .odd’).toggle()”\` | 637.7 | 431.7 | 517 | 1360.3 |
| Prototype | “\` erb$$(‘div.special, td.content .odd’).each(Element.toggle)”\` | 71 | 43.7 | 106.7 | 43 |
| **8** | jQuery | “\` erb$(‘span.odd’).remove()”\` | 132.7 | 59.3 | 123.3 | 66.7 |
| Prototype | “\` erb$$(‘span.odd’).each(Element.remove)”\` | 29 | 11.7 | 36.7 | 19.3 |
| **9** | jQuery | “\` erb$(‘#data p.lost:first’).html(‘gotcha!’)”\` | 5 | 1.7 | 10 | 3.3 |
| Prototype | “\` erb$(‘data’).down(‘p.lost’).update(‘gotcha!’)”\` | 11.7 | 2 | 10 | 7.3 |

### Conclusion #2

Prototype was at least 2 times faster then jQuery in 15 cases, and jQuery was faster then Prototype in 8 cases. What library should I choose? In my case I will stick with Prototype, because it offers the same functionality as jQuery does + more and it’s faster. jQuery is probably better for projects where there’s a need for some fancy UI effects and that’s it, but it’s just an assumption, correct me if I’m wrong…