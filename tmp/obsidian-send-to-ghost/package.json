{"name": "obsidian-ghost-publish", "version": "1.0.5", "description": "Obsidian plugin for easy publish to ghost with a single click.", "main": "src/main.ts", "scripts": {"preinstall": "npx only-allow pnpm", "dev": "./build.sh dev", "build": "./build.sh", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": [], "author": {"name": "Southpaw1496", "email": "<EMAIL>", "url": "https://github.com/southpaw1496/obsidian-send-to-ghost"}, "license": "MIT", "devDependencies": {"@types/jsonwebtoken": "^9.0.2", "@types/markdown-it": "^12.2.3", "@types/node": "^17.0.45", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "builtin-modules": "^3.3.0", "esbuild": "0.14.43", "gray-matter": "^4.0.3", "jsonwebtoken": "^9.0.1", "markdown-it": "^13.0.1", "obsidian": "^1.4.4", "tslib": "2.4.0", "typescript": "4.7.3"}}