<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TIL - Capturing logs in Elixir tests</title>
  <style>
    body {
      font-family: system-ui, -apple-system, sans-serif;
      line-height: 1.5;
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
    }
  </style>
</head>
<body>
  <p>Testing logging can be tricky but recently I&rsquo;ve learned that Elixir&rsquo;s ExUnit ships with a <code>CaptureLog</code> module that makes it easy to test logging behavior. I was really happy to see this built-in!</p>
<p>Let&rsquo;s say you have a function that processes a template and logs errors when something goes wrong. You want to test both the error handling and ensure the right messages are being logged. Here&rsquo;s a simplified version of such a function:</p>
<div class="highlight"><pre tabindex="0" style="color:#c8d3f5;background-color:#222436;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-elixir" data-lang="elixir"><span style="display:flex;"><span><span style="color:#c099ff">def</span> render_template(%<span style="color:#ff966c">SourceFile</span>{<span style="color:#c3e88d">is_template</span>: <span style="color:#ff966c">true</span>} <span style="color:#c3e88d;font-weight:bold">=</span> source_file, vars) <span style="color:#c099ff">do</span>
</span></span><span style="display:flex;"><span>  <span style="color:#c099ff">case</span> <span style="color:#ff966c">Solid</span><span style="color:#c3e88d;font-weight:bold">.</span>parse(source_file<span style="color:#c3e88d;font-weight:bold">.</span>content) <span style="color:#c099ff">do</span>
</span></span><span style="display:flex;"><span>    {<span style="color:#c3e88d">:ok</span>, template} <span style="color:#c3e88d;font-weight:bold">-&gt;</span>
</span></span><span style="display:flex;"><span>      <span style="color:#444a73;font-style:italic"># Template parsing succeeded, render it...</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>    {<span style="color:#c3e88d">:error</span>, reason} <span style="color:#c3e88d;font-weight:bold">-&gt;</span>
</span></span><span style="display:flex;"><span>      <span style="color:#ff966c">Logger</span><span style="color:#c3e88d;font-weight:bold">.</span>error(<span style="color:#c3e88d">&#34;Template parsing failed&#34;</span>, <span style="color:#c3e88d">reason</span>: reason)
</span></span><span style="display:flex;"><span>      {<span style="color:#c3e88d">:error</span>, <span style="color:#c3e88d">:template_parsing_failed</span>}
</span></span><span style="display:flex;"><span>  <span style="color:#c099ff">end</span>
</span></span><span style="display:flex;"><span><span style="color:#c099ff">end</span>
</span></span></code></pre></div><p>Without <code>ExUnit.CaptureLog</code>, testing this would result in error messages cluttering your test output, even though those errors are expected as part of the test.</p>
<p><code>ExUnit.CaptureLog</code> allows you to:</p>
<ol>
<li>Capture log messages during test execution</li>
<li>Assert against the content of those messages</li>
<li>Keep your test output clean by suppressing expected error logs</li>
</ol>
<p>Here&rsquo;s how I used it:</p>
<div class="highlight"><pre tabindex="0" style="color:#c8d3f5;background-color:#222436;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-elixir" data-lang="elixir"><span style="display:flex;"><span><span style="color:#c099ff">defmodule</span> <span style="color:#ff966c">YourTest</span> <span style="color:#c099ff">do</span>
</span></span><span style="display:flex;"><span>  <span style="color:#86e1fc">use</span> <span style="color:#ff966c">ExUnit.Case</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>  <span style="color:#86e1fc">import</span> <span style="color:#ff966c">ExUnit.CaptureLog</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>  test <span style="color:#c3e88d">&#34;handles invalid template syntax&#34;</span> <span style="color:#c099ff">do</span>
</span></span><span style="display:flex;"><span>    file <span style="color:#c3e88d;font-weight:bold">=</span> %<span style="color:#ff966c">SourceFile</span>{
</span></span><span style="display:flex;"><span>      <span style="color:#c3e88d">content</span>: <span style="color:#c3e88d">&#34;{{ unclosed tag&#34;</span>,
</span></span><span style="display:flex;"><span>      <span style="color:#c3e88d">is_template</span>: <span style="color:#ff966c">true</span>
</span></span><span style="display:flex;"><span>    }
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>    <span style="color:#444a73;font-style:italic"># Capture logs during template rendering</span>
</span></span><span style="display:flex;"><span>    log <span style="color:#c3e88d;font-weight:bold">=</span> capture_log(<span style="color:#c099ff">fn</span> <span style="color:#c3e88d;font-weight:bold">-&gt;</span>
</span></span><span style="display:flex;"><span>      assert {<span style="color:#c3e88d">:error</span>, <span style="color:#c3e88d">&#34;Template parsing failed: &#34;</span> <span style="color:#c3e88d;font-weight:bold">&lt;&gt;</span> _} <span style="color:#c3e88d;font-weight:bold">=</span>
</span></span><span style="display:flex;"><span>        render_template(file, %{})
</span></span><span style="display:flex;"><span>    <span style="color:#c099ff">end</span>)
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>    <span style="color:#444a73;font-style:italic"># Assert against the captured log content</span>
</span></span><span style="display:flex;"><span>    assert log <span style="color:#c3e88d;font-weight:bold">=~</span> <span style="color:#c3e88d">&#34;Template parsing failed&#34;</span>
</span></span><span style="display:flex;"><span>    assert log <span style="color:#c3e88d;font-weight:bold">=~</span> <span style="color:#c3e88d">&#34;expected end of string&#34;</span>
</span></span><span style="display:flex;"><span>    assert log <span style="color:#c3e88d;font-weight:bold">=~</span> <span style="color:#c3e88d">&#34;{{ unclosed tag&#34;</span>
</span></span><span style="display:flex;"><span>  <span style="color:#c099ff">end</span>
</span></span><span style="display:flex;"><span><span style="color:#c099ff">end</span>
</span></span></code></pre></div><h2 id="breaking-down-the-test">Breaking Down the Test</h2>
<p>Let&rsquo;s see what&rsquo;s happening:</p>
<ol>
<li>First, we import the helper function:</li>
</ol>
<div class="highlight"><pre tabindex="0" style="color:#c8d3f5;background-color:#222436;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-elixir" data-lang="elixir"><span style="display:flex;"><span><span style="color:#86e1fc">import</span> <span style="color:#ff966c">ExUnit.CaptureLog</span>
</span></span></code></pre></div><ol start="2">
<li>We wrap the code that generates logs in a <code>capture_log</code> function:</li>
</ol>
<div class="highlight"><pre tabindex="0" style="color:#c8d3f5;background-color:#222436;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-elixir" data-lang="elixir"><span style="display:flex;"><span>log <span style="color:#c3e88d;font-weight:bold">=</span> capture_log(<span style="color:#c099ff">fn</span> <span style="color:#c3e88d;font-weight:bold">-&gt;</span>
</span></span><span style="display:flex;"><span>  <span style="color:#444a73;font-style:italic"># Code that generates logs</span>
</span></span><span style="display:flex;"><span><span style="color:#c099ff">end</span>)
</span></span></code></pre></div><ol start="3">
<li>The <code>capture_log</code> function:</li>
</ol>
<ul>
<li>Takes a function as an argument</li>
<li>Executes that function</li>
<li>Captures any log output during execution</li>
<li>Returns the captured log as a string</li>
</ul>
<ol start="4">
<li>We can then make assertions about the log content using string matching:</li>
</ol>
<div class="highlight"><pre tabindex="0" style="color:#c8d3f5;background-color:#222436;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-elixir" data-lang="elixir"><span style="display:flex;"><span>assert log <span style="color:#c3e88d;font-weight:bold">=~</span> <span style="color:#c3e88d">&#34;Template parsing failed&#34;</span>
</span></span></code></pre></div><h2 id="benefits">Benefits</h2>
<p>Using <code>CaptureLog</code> provides several advantages:</p>
<ol>
<li>
<p><strong>Clean Test Output</strong>: Error logs don&rsquo;t pollute your test output, making it easier to spot real test failures.</p>
</li>
<li>
<p><strong>Explicit Verification</strong>: You can verify that your error handling is not just returning the right values, but also logging the right information.</p>
</li>
<li>
<p><strong>Better Documentation</strong>: The test clearly shows what log messages are expected when errors occur.</p>
</li>
</ol>
<p><code>ExUnit.CaptureLog</code> is a great tool for testing logging behavior in your Elixir applications. It helps you write more comprehensive tests while keeping your test output clean. Next time you need to test code that generates logs, remember that you can capture and verify those logs as part of your test assertions.</p>
<p>Today I learned!</p>
<h2 id="further-reading">Further Reading</h2>
<ul>
<li><a href="https://hexdocs.pm/ex_unit/ExUnit.CaptureLog.html">ExUnit.CaptureLog documentation</a></li>
<li><a href="https://hexdocs.pm/logger/Logger.html">Elixir Logger documentation</a></li>
</ul>

</body>
</html>
