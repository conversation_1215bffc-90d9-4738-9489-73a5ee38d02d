#!/bin/bash

# Open Source Portfolio Update Script
# This script helps update the portfolio with fresh GitHub statistics

set -e

echo "🚀 Open Source Portfolio Update Script"
echo "======================================"

# Check if GitHub API key is set
if [ -z "$GITHUB_API_KEY" ]; then
    echo "❌ Error: GITHUB_API_KEY environment variable is not set"
    echo "💡 Get a token from: https://github.com/settings/tokens"
    echo "💡 Required scopes: user:read"
    echo "💡 Usage: GITHUB_API_KEY=your_token ./scripts/update-portfolio.sh"
    exit 1
fi

# Check if node is available
if ! command -v node &> /dev/null; then
    echo "❌ Error: Node.js is not installed"
    echo "💡 Install Node.js from: https://nodejs.org/"
    exit 1
fi

# Check if required files exist
if [ ! -f "scripts/gh.js" ]; then
    echo "❌ Error: scripts/gh.js not found"
    exit 1
fi

if [ ! -f "data/open-source-projects.json" ]; then
    echo "❌ Error: data/open-source-projects.json not found"
    exit 1
fi

if [ ! -f "static/open-source-portfolio.html" ]; then
    echo "❌ Error: static/open-source-portfolio.html not found"
    exit 1
fi

echo "✅ All required files found"
echo ""

# Fetch GitHub statistics
echo "📊 Fetching GitHub contribution statistics..."
if node scripts/gh.js --stats --verbose; then
    echo "✅ GitHub statistics updated successfully"
else
    echo "⚠️  Warning: Failed to fetch GitHub statistics, using fallback data"
fi

echo ""

# Check if portfolio loads correctly
echo "🔍 Validating portfolio data..."

# Check if JSON files are valid
if node -e "JSON.parse(require('fs').readFileSync('data/open-source-projects.json', 'utf8'))"; then
    echo "✅ open-source-projects.json is valid"
else
    echo "❌ Error: open-source-projects.json is invalid"
    exit 1
fi

if [ -f "data/github-stats.json" ]; then
    if node -e "JSON.parse(require('fs').readFileSync('data/github-stats.json', 'utf8'))"; then
        echo "✅ github-stats.json is valid"
    else
        echo "❌ Error: github-stats.json is invalid"
        exit 1
    fi
else
    echo "⚠️  Warning: github-stats.json not found, portfolio will use fallback stats"
fi

echo ""

# Start local server for testing (optional)
if [ "$1" = "--serve" ]; then
    echo "🌐 Starting local server for testing..."
    echo "📱 Open http://localhost:8000/static/open-source-portfolio.html in your browser"
    echo "🛑 Press Ctrl+C to stop the server"
    echo ""

    if command -v python3 &> /dev/null; then
        python3 -m http.server 8000
    elif command -v python &> /dev/null; then
        python -m SimpleHTTPServer 8000
    else
        echo "❌ Error: Python is not installed, cannot start local server"
        exit 1
    fi
else
    echo ""
    echo "🔄 Syncing with Ghost.io..."
    if node scripts/ghost.js --page open-source --verbose; then
        echo "✅ Ghost.io open source page updated successfully"
    else
        echo "⚠️  Warning: Failed to sync with Ghost.io, check your API configuration"
    fi

    echo ""
    echo "✅ Portfolio update completed successfully!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Check https://solnic.dev/open-source/ to see the updated page"
    echo "2. Or open static/open-source-portfolio.html for local testing"
    echo "3. Run: ./scripts/update-portfolio.sh --serve for local preview"
    echo ""
    echo "📄 Files updated:"
    echo "   - data/github-stats.json (GitHub statistics)"
    echo "   - static/open-source-portfolio.html (standalone portfolio)"
    echo "   - Ghost.io open-source page (live website)"
    echo ""
    echo "💡 To update project data, edit: data/open-source-projects.json"
fi
