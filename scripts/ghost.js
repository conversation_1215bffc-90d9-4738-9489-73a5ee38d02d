#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const { program } = require('commander');
const GhostAdminAPI = require('@tryghost/admin-api');
const TemplateEngine = require('../lib/template-engine');
const FormData = require('form-data');
const fetch = require('node-fetch');
const { Octokit } = require('@octokit/rest');
const { marked } = require('marked');
const crypto = require('crypto');

// Import our new modules
const { GhostAPI } = require('../lib/ghost-api');
const { PostSync } = require('../lib/post-sync');

// Configure marked for better HTML output
marked.setOptions({
  gfm: true,
  breaks: false,
  sanitize: false,
  smartypants: true
});

// Command line argument parsing
program
  .name('ghost')
  .description('Sync content with Ghost.io using Admin API or export posts to Ghost JSON format')
  .option('--config <file>', 'Ghost config file path', 'config/ghost.yml')
  .option('--page <name>', 'Update specific page (github-sponsors, open-source, about)')
  .option('--export-posts', 'Export Hugo posts to Ghost JSON format')
  .option('--update-posts', 'Sync posts with Ghost via Admin API (creates missing, updates existing)')
  .option('--cleanup-duplicates', 'Find and remove duplicate posts in Ghost')
  .option('--sync-posts', 'Sync posts from Ghost to local articles directory')
  .option('--list-posts', 'List all posts from Ghost (for selection in UI)')
  .option('--title <title>', 'Specific post title to sync (used with --sync-posts)')
  .option('--from <date>', 'Start date for post export/update (YYYY-MM-DD)')
  .option('--to <date>', 'End date for post export/update (YYYY-MM-DD)')
  .option('--output <file>', 'Output file path for export', 'ghost-export.json')
  .option('--posts-dir <dir>', 'Posts directory for export/update', 'content/posts')
  .option('--articles-dir <dir>', 'Articles directory for sync', 'articles')
  .option('--author <name>', 'Default author name for export', 'Peter Solnica')
  .option('--author-email <email>', 'Default author email for export', '<EMAIL>')
  .option('--published', 'Export/update posts as published instead of draft')
  .option('--draft', 'Export/update posts as draft (default behavior)')
  .option('--html', 'Use HTML content instead of Markdown for post updates (renders Markdown to HTML)')
  .option('--dry-run', 'Show what would be updated/exported without making changes')
  .option('--verbose', 'Show detailed output')
  .addHelpText('after', `
Examples:
  Update GitHub sponsors page:
    $ node scripts/ghost.js --page github-sponsors

  Update open source portfolio:
    $ node scripts/ghost.js --page open-source

  Update about page:
    $ node scripts/ghost.js --page about

  Export all posts to Ghost JSON (as drafts):
    $ node scripts/ghost.js --export-posts

  Export posts from a date range:
    $ node scripts/ghost.js --export-posts --from 2024-01-01 --to 2024-12-31

  Export posts as published:
    $ node scripts/ghost.js --export-posts --published

  Export posts as draft (explicit):
    $ node scripts/ghost.js --export-posts --draft

  Sync posts with Ghost (creates missing, updates existing, preserves Markdown, sets author):
    $ node scripts/ghost.js --update-posts --from 2010 --to 2011 --published

  Sync posts as draft (creates missing, updates existing, preserves Markdown, sets author):
    $ node scripts/ghost.js --update-posts --from 2007 --to 2022 --draft

  Sync posts with HTML content (renders Markdown to HTML):
    $ node scripts/ghost.js --update-posts --from 2022-02-02 --to 2022-02-02 --html

  Clean up duplicate posts:
    $ node scripts/ghost.js --cleanup-duplicates

  Sync specific post from Ghost to local articles:
    $ node scripts/ghost.js --sync-posts --title "Announcing TextParser for Elixir"

  Sync all posts from Ghost to local articles:
    $ node scripts/ghost.js --sync-posts

  Dry run to see what would be updated:
    $ node scripts/ghost.js --page about --dry-run

  Generate HTML fallback if API fails:
    $ node scripts/ghost.js --page open-source --fallback
`)
  .parse();

const options = program.opts();

// Initialize template engine
const templateEngine = new TemplateEngine(path.join(__dirname, '..', 'templates'));

// Initialize GitHub API client
let octokit = null;
const initGitHubAPI = () => {
  if (!octokit) {
    // Try to get GitHub token from environment or config
    const githubToken = process.env.GITHUB_TOKEN || process.env.GH_TOKEN;
    octokit = new Octokit({
      auth: githubToken,
      userAgent: 'solnic.dev-portfolio-generator'
    });
  }
  return octokit;
};

// Load cached repository contribution data
const loadRepositoryContributions = () => {
  const repoContribPath = path.join(__dirname, '..', 'data', 'github-repo-contributions.json');

  if (!fs.existsSync(repoContribPath)) {
    if (options.verbose) {
      console.log('⚠️  Repository contributions cache not found. Run: node scripts/gh.js --repos');
    }
    return {};
  }

  try {
    const data = JSON.parse(fs.readFileSync(repoContribPath, 'utf8'));
    return data.repositories || {};
  } catch (error) {
    console.error(`❌ Failed to load repository contributions: ${error.message}`);
    return {};
  }
};

// Get repository data from cache
const getRepositoryData = (repoFullName, repoContributions) => {
  const repoData = repoContributions[repoFullName];

  if (!repoData) {
    if (options.verbose) {
      console.log(`⚠️  No cached data for ${repoFullName}. Run: node scripts/gh.js --repos`);
    }
    return null;
  }

  // Format commit information
  let commitInfo = 'Contributor';
  if (typeof repoData.user_commits === 'string') {
    commitInfo = repoData.user_commits;
  } else if (repoData.user_commits > 0) {
    commitInfo = `${repoData.user_commits} commits`;
  }

  // Generate commit URL for browsing user's commits
  const commitUrl = `https://github.com/${repoData.full_name}/commits/main/?author=solnic`;

  return {
    name: repoData.name,
    fullName: repoData.full_name,
    githubUrl: repoData.url,
    description: repoData.description || 'No description available',
    commits: commitInfo,
    commitUrl: commitUrl,
    stars: repoData.stars,
    language: repoData.language
  };
};

// Load Ghost configuration
const loadGhostConfig = () => {
  const configPath = path.resolve(options.config);

  if (!fs.existsSync(configPath)) {
    console.error(`❌ Ghost config file not found: ${configPath}`);
    process.exit(1);
  }

  try {
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = yaml.load(configContent);

    if (!config.integrations || !config.integrations.page_sync) {
      console.error('❌ Missing page_sync integration in Ghost config');
      console.error('💡 Add integration config to ghost.yml:');
      console.error('integrations:');
      console.error('  page_sync:');
      console.error('    admin_api_key: "your_admin_api_key"');
      process.exit(1);
    }

    const integration = config.integrations.page_sync;

    if (!integration.admin_api_key) {
      console.error('❌ Missing admin_api_key in Ghost config');
      console.error('💡 Get your Admin API key from Ghost Admin → Settings → Integrations');
      process.exit(1);
    }

    return integration;
  } catch (error) {
    console.error(`❌ Error loading Ghost config: ${error.message}`);
    process.exit(1);
  }
};

// Parse CSV data
const parseCSV = (csvPath) => {
  const content = fs.readFileSync(csvPath, 'utf8');
  const lines = content.trim().split('\n');
  const headers = lines[0].split(',');

  return lines.slice(1).map(line => {
    const values = line.split(',');
    const row = {};
    headers.forEach((header, index) => {
      row[header] = values[index] || '';
    });
    return row;
  });
};

// Generate GitHub profile URL
const githubProfileUrl = (handle) => {
  return `https://github.com/${handle}`;
};

// Helper function to get icon for link type
const getIconForLinkType = (type) => {
  const icons = {
    github: 'GitHub',
    website: 'Website',
    blog_post: 'Blog Post',
    ruby_feature_request: 'Ruby Feature'
  };
  return icons[type] || 'Link';
};

// Cache for uploaded image URLs to avoid re-uploading
const uploadedImageCache = new Map();

// Upload image to Ghost CMS and return the URL
const uploadImageToGhost = async (imagePath, config, imageType = 'logo') => {
  const filename = path.basename(imagePath);

  // Check cache first
  if (uploadedImageCache.has(filename)) {
    if (options.verbose) {
      console.log(`📋 Using cached URL for ${filename}`);
    }
    return uploadedImageCache.get(filename);
  }

  try {
    const imageBuffer = fs.readFileSync(imagePath);

    const form = new FormData();
    form.append('file', imageBuffer, {
      filename,
      contentType: 'image/png'
    });
    form.append('purpose', 'image');

    // Use different ref based on image type
    const ref = imageType === 'about' ? `about/${filename}` : `open-source/logos/${filename}`;
    form.append('ref', ref);

    // Extract the key parts for the JWT token
    const [id, secret] = config.admin_api_key.split(':');

    // Create JWT token manually (simplified version)
    const jwt = require('jsonwebtoken');
    const token = jwt.sign({}, Buffer.from(secret, 'hex'), {
      keyid: id,
      algorithm: 'HS256',
      expiresIn: '5m',
      audience: '/admin/'
    });

    const response = await fetch('https://solnic.ghost.io/ghost/api/admin/images/upload/', {
      method: 'POST',
      headers: {
        'Authorization': `Ghost ${token}`,
        'Accept-Version': 'v5.0',
        ...form.getHeaders()
      },
      body: form
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    const imageUrl = result.images[0].url;

    // Cache the result
    uploadedImageCache.set(filename, imageUrl);

    console.log(`✅ Uploaded ${filename} to Ghost CMS`);
    if (options.verbose) {
      console.log(`📄 URL: ${imageUrl}`);
    }
    return imageUrl;
  } catch (error) {
    console.error(`❌ Failed to upload ${imagePath}:`, error.message);
    return null;
  }
};

// Helper function to get logo URL (with Ghost CMS upload support)
const getLogoUrl = async (project, config) => {
  // Check if there's a local logo file for this project
  const localLogoPath = path.join(__dirname, '..', 'assets', 'images', `${project.id}.png`);

  if (fs.existsSync(localLogoPath)) {
    // Upload to Ghost CMS and return the Ghost URL
    const ghostUrl = await uploadImageToGhost(localLogoPath, config);
    if (ghostUrl) {
      return ghostUrl;
    }
  }

  // Fallback to external URLs for known projects
  if (project.id === 'dry-rb') {
    return 'https://dry-rb.org/assets/logo-symbol.svg';
  }
  if (project.id === 'rom-rb') {
    return 'https://rom-rb.org/assets/logo.svg';
  }
  if (project.logo) {
    return `/content/open-source/images/${project.logo}`;
  }
  return null;
};

// Helper function to get about photo URL (with Ghost CMS upload support)
const getAboutPhotoUrl = async (config) => {
  // Check if there's a local about photo file
  const localPhotoPath = path.join(__dirname, '..', 'assets', 'images', 'about.png');

  if (fs.existsSync(localPhotoPath)) {
    console.log('📸 Found local about photo, uploading to Ghost CMS...');
    // Upload to Ghost CMS and return the Ghost URL
    const ghostUrl = await uploadImageToGhost(localPhotoPath, config, 'about');
    if (ghostUrl) {
      console.log('✅ About photo uploaded successfully');
      return ghostUrl;
    }
  }

  // Fallback to the default avatar path
  console.log('⚠️  No local about photo found, using default avatar');
  return '/assets/images/avatar.png';
};

// Generate GitHub Sponsors page HTML with embedded CSS
const generateGitHubSponsorsHTML = () => {
  const csvPath = path.join(__dirname, '..', 'data', 'github-sponsors.csv');

  if (!fs.existsSync(csvPath)) {
    console.error(`❌ GitHub sponsors CSV file not found: ${csvPath}`);
    process.exit(1);
  }

  console.log('📊 Reading GitHub sponsors data...');
  const sponsorsData = parseCSV(csvPath);

  // Process sponsors data - only include public sponsors
  const currentSponsors = sponsorsData
    .filter(sponsor => sponsor.is_active === 'true' && sponsor.is_public === 'true')
    .sort((a, b) => new Date(a.sponsorship_started_on) - new Date(b.sponsorship_started_on))
    .reverse()
    .map(sponsor => ({
      ...sponsor,
      name: sponsor.sponsor_name || sponsor.sponsor_handle,
      profileUrl: githubProfileUrl(sponsor.sponsor_handle)
    }));

  const pastSponsors = sponsorsData
    .filter(sponsor => sponsor.is_active === 'false' && sponsor.is_public === 'true')
    .sort((a, b) => new Date(a.sponsorship_started_on) - new Date(b.sponsorship_started_on))
    .reverse()
    .map(sponsor => ({
      ...sponsor,
      name: sponsor.sponsor_name || sponsor.sponsor_handle,
      profileUrl: githubProfileUrl(sponsor.sponsor_handle)
    }));

  // Prepare template data
  const templateData = {
    lastUpdated: new Date().toISOString(),
    currentSponsors,
    pastSponsors
  };

  // Render using template engine
  const html = templateEngine.renderPage('github-sponsors', templateData);

  return {
    html,
    currentSponsors: currentSponsors.length,
    pastSponsors: pastSponsors.length
  };
};

// Generate Open Source Portfolio HTML with embedded CSS for Ghost.io
const generateOpenSourcePortfolioHTML = async (config) => {
  const projectsPath = path.join(__dirname, '..', 'data', 'open-source-projects.json');
  const statsPath = path.join(__dirname, '..', 'data', 'github-stats.json');

  if (!fs.existsSync(projectsPath)) {
    console.error(`❌ Open source projects file not found: ${projectsPath}`);
    process.exit(1);
  }

  console.log('📊 Reading open source projects data...');
  const projectsData = JSON.parse(fs.readFileSync(projectsPath, 'utf8'));

  // Try to load GitHub stats
  let statsData = null;
  if (fs.existsSync(statsPath)) {
    console.log('📈 Reading GitHub statistics...');
    statsData = JSON.parse(fs.readFileSync(statsPath, 'utf8'));
  }

  const allProjects = [...projectsData.projects, ...projectsData.past_projects];

  // Load cached repository contribution data
  const repoContributions = loadRepositoryContributions();

  // Process current projects with template data (async for logo uploads)
  const currentProjects = await Promise.all(projectsData.projects.map(async project => {
    const processedProject = {
      ...project,
      logoUrl: await getLogoUrl(project, config),
      yearRange: project.year_ended
        ? `${project.year_started} - ${project.year_ended}`
        : `${project.year_started} - Present`,
      links: Object.entries(project.links || {}).map(([key, url]) => ({
        url,
        label: getIconForLinkType(key)
      }))
    };

    // Get highlighted repositories data from cache if specified
    if (project.highlightedRepoNames && project.highlightedRepoNames.length > 0) {
      if (options.verbose) {
        console.log(`📊 Loading cached GitHub data for ${project.name}...`);
      }

      const repoDataResults = project.highlightedRepoNames.map(repoName =>
        getRepositoryData(repoName, repoContributions)
      ).filter(repo => repo !== null);

      // Add article URLs
      processedProject.highlightedRepos = repoDataResults.map(repo => ({
        ...repo,
        articleUrl: `https://solnic.dev/tags/${repo.name.replace('-', '')}/`
      }));
    }

    return processedProject;
  }));

  // Process past projects with template data (async for logo uploads)
  const pastProjects = await Promise.all(projectsData.past_projects.map(async project => {
    const processedProject = {
      ...project,
      logoUrl: await getLogoUrl(project, config),
      yearRange: project.year_ended
        ? `${project.year_started} - ${project.year_ended}`
        : `${project.year_started} - Present`,
      links: Object.entries(project.links || {}).map(([key, url]) => ({
        url,
        label: getIconForLinkType(key)
      }))
    };

    // Get highlighted repositories data from cache if specified
    if (project.highlightedRepoNames && project.highlightedRepoNames.length > 0) {
      if (options.verbose) {
        console.log(`📊 Loading cached GitHub data for ${project.name}...`);
      }

      const repoDataResults = project.highlightedRepoNames.map(repoName =>
        getRepositoryData(repoName, repoContributions)
      ).filter(repo => repo !== null);

      // Add article URLs
      processedProject.highlightedRepos = repoDataResults.map(repo => ({
        ...repo,
        articleUrl: `https://solnic.dev/tags/${repo.name.replace('-', '')}/`
      }));
    }

    return processedProject;
  }));

  // Prepare stats data
  const stats = [];

  if (statsData) {
    stats.push(
      { number: statsData.total_contributions?.toLocaleString() || '10,000+', label: 'Total Commits' },
      { number: statsData.total_repositories || '100+', label: 'Repositories' },
      { number: statsData.organizations_count || '20+', label: 'Organizations' },
      { number: '15+', label: 'Years Contributing' }
    );
  } else {
    // Fallback stats
    stats.push(
      { number: '10,000+', label: 'Total Commits' },
      { number: '100+', label: 'Repositories' },
      { number: '20+', label: 'Organizations' },
      { number: '15+', label: 'Years Contributing' }
    );
  }

  // Prepare template data
  const templateData = {
    lastUpdated: new Date().toISOString(),
    totalProjects: allProjects.length,
    stats,
    currentProjects,
    pastProjects,
    statsDataJson: statsData ? JSON.stringify(statsData) : 'null'
  };

  // Render using template engine
  const html = templateEngine.renderPage('open-source', templateData);



  return {
    html,
    totalProjects: allProjects.length,
    currentProjects: currentProjects.length,
    pastProjects: pastProjects.length
  };
};

// Generate About Page HTML with embedded CSS for Ghost.io
const generateAboutPageHTML = async (config) => {
  const aboutPath = path.join(__dirname, '..', 'data', 'about.json');

  if (!fs.existsSync(aboutPath)) {
    console.error(`❌ About data file not found: ${aboutPath}`);
    process.exit(1);
  }

  console.log('📊 Reading about page data...');
  const aboutData = JSON.parse(fs.readFileSync(aboutPath, 'utf8'));

  // Upload about photo and get Ghost URL
  const photoUrl = await getAboutPhotoUrl(config);

  // Update the photo URL in the data
  const updatedAboutData = {
    ...aboutData,
    personal: {
      ...aboutData.personal,
      photoUrl: photoUrl
    }
  };

  // Prepare template data
  const templateData = {
    lastUpdated: new Date().toISOString(),
    ...updatedAboutData
  };

  // Render using template engine
  const html = templateEngine.renderPage('about', templateData);

  return {
    html
  };
};

// Initialize Ghost Admin API
const initGhostAPI = (config) => {
  if (options.verbose) {
    console.log('🔧 API Configuration:');
    console.log(`   URL: https://solnic.ghost.io`);
    console.log(`   Key: ${config.admin_api_key.substring(0, 20)}...`);
    console.log(`   Version: v5.0`);
  }

  // Validate API key format (should be id:secret)
  if (!config.admin_api_key.includes(':')) {
    throw new Error('Invalid Admin API key format. Expected format: id:secret');
  }

  return new GhostAdminAPI({
    url: 'https://solnic.ghost.io',
    key: config.admin_api_key,
    version: 'v5.0'
  });
};

// Find page by slug
const findPageBySlug = async (api, slug) => {
  try {
    const pages = await api.pages.browse({
      filter: `slug:${slug}`,
      limit: 1
    });

    return pages.length > 0 ? pages[0] : null;
  } catch (error) {
    console.error(`❌ Error finding page with slug "${slug}":`, error.message);
    throw error;
  }
};

// Update Open Source Portfolio page
const updateOpenSourcePage = async (api) => {
  console.log('🔍 Looking for open source page...');

  // Find the existing page
  const existingPage = await findPageBySlug(api, 'open-source');

  if (!existingPage) {
    console.error('❌ Open source page not found. Please create it first in Ghost admin.');
    console.error('💡 Create a new page with slug "open-source" in Ghost Admin');
    process.exit(1);
  }

  console.log(`✅ Found page: "${existingPage.title}" (ID: ${existingPage.id})`);

  // Generate new content
  const config = loadGhostConfig();
  const { html, totalProjects, currentProjects, pastProjects } = await generateOpenSourcePortfolioHTML(config);

  if (options.dryRun) {
    console.log('\n🔍 Dry run - would update page with:');
    console.log(`📊 Total projects: ${totalProjects}`);
    console.log(`📊 Current projects: ${currentProjects}`);
    console.log(`📊 Past projects: ${pastProjects}`);
    console.log(`📄 HTML length: ${html.length} characters`);
    console.log('\n🔍 HTML preview (first 300 characters):');
    console.log(html.substring(0, 300) + '...');
    console.log('\n🔍 Dry run completed - no changes were made');
    return;
  }

  try {
    // Fetch the page again to get the latest updated_at timestamp
    console.log('🔄 Fetching latest page data for update...');
    const latestPage = await findPageBySlug(api, 'open-source');

    if (!latestPage) {
      throw new Error('Page not found when trying to update');
    }

    if (options.verbose) {
      console.log(`📄 Current page updated_at: ${latestPage.updated_at}`);
      console.log(`📄 HTML content length: ${html.length} characters`);
      console.log(`📄 Page status: ${latestPage.status}`);
      console.log(`📄 Page visibility: ${latestPage.visibility}`);
    }

    // Create lexical document with HTML card (as per Ghost forum guidance)
    const lexicalContent = {
      root: {
        children: [{ type: 'html', version: 1, html: html }],
        direction: null,
        format: '',
        indent: 0,
        type: 'root',
        version: 1
      }
    };

    const updateData = {
      id: latestPage.id,
      title: latestPage.title, // Keep the same title
      mobiledoc: null, // Clear mobiledoc to avoid conflicts
      lexical: JSON.stringify(lexicalContent),
      updated_at: latestPage.updated_at // Required for conflict detection
    };

    if (options.verbose) {
      console.log(`📄 Update data keys: ${Object.keys(updateData).join(', ')}`);
      console.log(`📄 Lexical content type: HTML card`);
    }

    // Update the page with lexical HTML card
    const updatedPage = await api.pages.edit(updateData);

    console.log('✅ Open source portfolio page updated successfully!');
    console.log(`📊 Total projects: ${totalProjects}`);
    console.log(`📊 Current projects: ${currentProjects}`);
    console.log(`📊 Past projects: ${pastProjects}`);
    console.log(`🔗 Page URL: https://solnic.dev/${updatedPage.slug}/`);

    if (options.verbose) {
      console.log(`📄 Updated page ID: ${updatedPage.id}`);
      console.log(`📄 New updated_at: ${updatedPage.updated_at}`);
    }

  } catch (error) {
    console.error('❌ Error updating page:', error.message);

    if (error.message.includes('UpdateCollisionError')) {
      console.error('💡 The page was modified by someone else. Please try again.');
    } else if (error.message.includes('Authorization')) {
      console.error('💡 API authorization failed. Check your Admin API key permissions.');
      console.error('💡 Make sure the integration has "Pages" permissions in Ghost Admin.');
    }

    throw error;
  }
};

// Update GitHub sponsors page
const updateGitHubSponsorsPage = async (api) => {
  console.log('🔍 Looking for GitHub sponsors page...');

  // Find the existing page
  const existingPage = await findPageBySlug(api, 'github-sponsors');

  if (!existingPage) {
    console.error('❌ GitHub sponsors page not found. Please create it first in Ghost admin.');
    console.error('💡 Create a new page with slug "github-sponsors" in Ghost Admin');
    process.exit(1);
  }

  console.log(`✅ Found page: "${existingPage.title}" (ID: ${existingPage.id})`);

  // Generate new content
  const { html, currentSponsors, pastSponsors } = generateGitHubSponsorsHTML();

  if (options.dryRun) {
    console.log('\n🔍 Dry run - would update page with:');
    console.log(`📊 Current sponsors: ${currentSponsors}`);
    console.log(`📊 Past sponsors: ${pastSponsors}`);
    console.log(`📄 HTML length: ${html.length} characters`);
    console.log('\n🔍 HTML preview (first 300 characters):');
    console.log(html.substring(0, 300) + '...');
    console.log('\n🔍 Dry run completed - no changes were made');
    return;
  }

  try {
    // Fetch the page again to get the latest updated_at timestamp
    console.log('🔄 Fetching latest page data for update...');
    const latestPage = await findPageBySlug(api, 'github-sponsors');

    if (!latestPage) {
      throw new Error('Page not found when trying to update');
    }

    if (options.verbose) {
      console.log(`📄 Current page updated_at: ${latestPage.updated_at}`);
      console.log(`📄 HTML content length: ${html.length} characters`);
      console.log(`📄 Page status: ${latestPage.status}`);
      console.log(`📄 Page visibility: ${latestPage.visibility}`);
    }

    // Create lexical document with HTML card (as per Ghost forum guidance)
    const lexicalContent = {
      root: {
        children: [{ type: 'html', version: 1, html: html }],
        direction: null,
        format: '',
        indent: 0,
        type: 'root',
        version: 1
      }
    };

    const updateData = {
      id: latestPage.id,
      title: latestPage.title, // Keep the same title
      mobiledoc: null, // Clear mobiledoc to avoid conflicts
      lexical: JSON.stringify(lexicalContent),
      updated_at: latestPage.updated_at // Required for conflict detection
    };

    if (options.verbose) {
      console.log(`📄 Update data keys: ${Object.keys(updateData).join(', ')}`);
      console.log(`📄 Lexical content type: HTML card`);
    }

    // Update the page with lexical HTML card
    const updatedPage = await api.pages.edit(updateData);

    console.log('✅ GitHub sponsors page updated successfully!');
    console.log(`📊 Current sponsors: ${currentSponsors}`);
    console.log(`📊 Past sponsors: ${pastSponsors}`);
    console.log(`🔗 Page URL: https://solnic.dev/${updatedPage.slug}/`);

    if (options.verbose) {
      console.log(`📄 Updated page ID: ${updatedPage.id}`);
      console.log(`📄 New updated_at: ${updatedPage.updated_at}`);
    }

  } catch (error) {
    console.error('❌ Error updating page:', error.message);

    if (error.message.includes('UpdateCollisionError')) {
      console.error('💡 The page was modified by someone else. Please try again.');
    } else if (error.message.includes('Authorization')) {
      console.error('💡 API authorization failed. Check your Admin API key permissions.');
      console.error('💡 Make sure the integration has "Pages" permissions in Ghost Admin.');
    }

    throw error;
  }
};

// Update About page
const updateAboutPage = async (api) => {
  console.log('🔍 Looking for about page...');

  // Find the about page
  const existingPage = await findPageBySlug(api, 'about');

  if (!existingPage) {
    console.error('❌ About page not found');
    console.error('💡 Please create an "About" page in Ghost Admin first');
    process.exit(1);
  }

  console.log(`✅ Found page: "${existingPage.title}" (ID: ${existingPage.id})`);

  // Generate new content
  const config = loadGhostConfig();
  const { html } = await generateAboutPageHTML(config);

  if (options.dryRun) {
    console.log('\n🔍 Dry run - would update page with:');
    console.log(`📄 HTML length: ${html.length} characters`);
    console.log('\n🔍 HTML preview (first 300 characters):');
    console.log(html.substring(0, 300) + '...');
    console.log('\n🔍 Dry run completed - no changes were made');
    return;
  }

  try {
    // Fetch the page again to get the latest updated_at timestamp
    console.log('🔄 Fetching latest page data for update...');
    const latestPage = await findPageBySlug(api, 'about');

    if (!latestPage) {
      throw new Error('Page not found when trying to update');
    }

    if (options.verbose) {
      console.log(`📄 Current page updated_at: ${latestPage.updated_at}`);
      console.log(`📄 HTML content length: ${html.length} characters`);
      console.log(`📄 Page status: ${latestPage.status}`);
      console.log(`📄 Page visibility: ${latestPage.visibility}`);
    }

    // Create lexical document with HTML card (as per Ghost forum guidance)
    const lexicalContent = {
      root: {
        children: [{ type: 'html', version: 1, html: html }],
        direction: null,
        format: '',
        indent: 0,
        type: 'root',
        version: 1
      }
    };

    const updateData = {
      id: latestPage.id,
      title: latestPage.title, // Keep the same title
      mobiledoc: null, // Clear mobiledoc to avoid conflicts
      lexical: JSON.stringify(lexicalContent),
      updated_at: latestPage.updated_at // Required for conflict detection
    };

    if (options.verbose) {
      console.log(`📄 Update data keys: ${Object.keys(updateData).join(', ')}`);
      console.log(`📄 Lexical content type: HTML card`);
    }

    console.log('🔄 Updating about page...');
    const updatedPage = await api.pages.edit(updateData);

    console.log('✅ About page updated successfully!');
    console.log(`📄 Page URL: https://solnic.ghost.io/${updatedPage.slug}/`);
    console.log(`📊 HTML length: ${html.length} characters`);

    if (options.verbose) {
      console.log('\n📄 Updated content preview (first 500 characters):');
      console.log(html.substring(0, 500) + '...');
    }

  } catch (error) {
    console.error('❌ Error updating page:', error.message);

    if (error.message.includes('UpdateCollisionError')) {
      console.error('💡 The page was modified by someone else. Please try again.');
    } else if (error.message.includes('Authorization')) {
      console.error('💡 API authorization failed. Check your Admin API key permissions.');
      console.error('💡 Make sure the integration has "Pages" permissions in Ghost Admin.');
    }

    throw error;
  }
};

// ============================================================================
// POST EXPORT FUNCTIONALITY
// ============================================================================

// Generate a unique ID for Ghost objects
const generateId = () => {
  return Math.floor(Math.random() * 1000000).toString();
};

// Format date for Ghost (for JSON export)
const formatGhostDate = (date) => {
  return date.toISOString().replace('T', ' ').replace(/\.\d{3}Z$/, '');
};

// Format date for Ghost API (needs full ISO format)
const formatGhostAPIDate = (date) => {
  return date.toISOString();
};

// Parse date string
const parseDate = (dateStr) => {
  if (!dateStr) return null;
  const date = new Date(dateStr);
  return isNaN(date.getTime()) ? null : date;
};

// Create slug from title
const slugify = (text) => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
};

// Decode HTML entities
const decodeHtmlEntities = (text) => {
  if (!text) return text;

  return text
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&nbsp;/g, ' ');
};

// Generate excerpt with 300 character limit
const generateExcerpt = (frontMatter, htmlContent) => {
  // First try frontmatter excerpt or description
  if (frontMatter.excerpt) {
    const decoded = decodeHtmlEntities(frontMatter.excerpt);
    return decoded.length > 300
      ? decoded.substring(0, 297) + '...'
      : decoded;
  }

  if (frontMatter.description) {
    const decoded = decodeHtmlEntities(frontMatter.description);
    return decoded.length > 300
      ? decoded.substring(0, 297) + '...'
      : decoded;
  }

  // Extract from content if no frontmatter excerpt
  if (htmlContent) {
    // Strip HTML tags and get plain text
    const plaintext = htmlContent.replace(/<[^>]*>/g, '').trim();
    const decoded = decodeHtmlEntities(plaintext);

    if (decoded.length <= 300) {
      return decoded;
    }

    // Truncate at word boundary to avoid cutting mid-word
    const truncated = decoded.substring(0, 297);
    const lastSpace = truncated.lastIndexOf(' ');

    if (lastSpace > 250) { // Only use word boundary if it's not too far back
      return truncated.substring(0, lastSpace) + '...';
    }

    return truncated + '...';
  }

  return null;
};

// Parse Hugo post file
const parseHugoPost = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');

    // Extract YAML front matter
    const frontMatterMatch = content.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
    if (!frontMatterMatch) {
      console.warn(`No front matter found in ${filePath}`);
      return null;
    }

    const frontMatter = yaml.load(frontMatterMatch[1]);
    const markdownContent = frontMatterMatch[2].trim();

    // Parse date
    const postDate = parseDate(frontMatter.date);
    if (!postDate) {
      console.warn(`Invalid date in ${filePath}: ${frontMatter.date}`);
      return null;
    }

    // Filter by date range if specified
    if (options.from) {
      const fromDate = parseDate(options.from);
      if (fromDate && postDate < fromDate) return null;
    }

    if (options.to) {
      const toDate = parseDate(options.to);
      if (toDate && postDate > toDate) return null;
    }

    // Convert markdown to HTML
    const htmlContent = marked(markdownContent);

    // Extract slug from front matter or generate from title
    const slug = frontMatter.slug || slugify(frontMatter.title);

    return {
      frontMatter,
      markdownContent,
      htmlContent,
      postDate,
      slug,
      filePath
    };
  } catch (error) {
    console.error(`Error parsing ${filePath}:`, error.message);
    return null;
  }
};

// Find all Hugo posts
const findHugoPosts = (postsDir) => {
  const posts = [];

  try {
    const entries = fs.readdirSync(postsDir, { withFileTypes: true });

    for (const entry of entries) {
      if (entry.name === '_index.md') continue;

      const fullPath = path.join(postsDir, entry.name);

      if (entry.isDirectory()) {
        // Check for index.md in subdirectory
        const indexPath = path.join(fullPath, 'index.md');
        if (fs.existsSync(indexPath)) {
          const post = parseHugoPost(indexPath);
          if (post) posts.push(post);
        }
      } else if (entry.name.endsWith('.md')) {
        // Direct markdown file
        const post = parseHugoPost(fullPath);
        if (post) posts.push(post);
      }
    }
  } catch (error) {
    console.error(`Error reading posts directory ${postsDir}:`, error.message);
    process.exit(1);
  }

  return posts.sort((a, b) => b.postDate - a.postDate);
};

// Create Ghost post object
const createGhostPost = (post, postId) => {
  const { frontMatter, markdownContent, htmlContent, postDate, slug } = post;

  // Determine status - respect both --published and --draft flags
  let status;
  if (options.published) {
    status = 'published';
  } else if (options.draft) {
    status = 'draft';
  } else {
    // Default to draft if neither flag is specified
    status = 'draft';
  }

  const publishedAt = status === 'published' ? formatGhostDate(postDate) : null;

  return {
    id: postId,
    uuid: crypto.randomUUID(),
    title: frontMatter.title,
    slug: slug,
    html: htmlContent, // Keep for compatibility, but posts will use markdown in lexical
    comment_id: postId,
    plaintext: markdownContent || htmlContent.replace(/<[^>]*>/g, ''), // Use markdown or strip HTML for plaintext
    feature_image: frontMatter.feature_image || frontMatter.image || null,
    featured: frontMatter.featured || false,
    type: frontMatter.type || 'post',
    status: status,
    visibility: frontMatter.visibility || 'public',
    created_at: formatGhostDate(postDate),
    updated_at: formatGhostDate(postDate),
    published_at: publishedAt,
    custom_excerpt: generateExcerpt(frontMatter, htmlContent),
    // Add markdown content for posts
    markdown: markdownContent
  };
};

// Create Ghost user object
const createGhostUser = (authorId) => {
  return {
    id: authorId,
    uuid: crypto.randomUUID(),
    name: options.author || 'Peter Solnica',
    slug: slugify(options.author || 'Peter Solnica'),
    email: options.authorEmail || '<EMAIL>',
    profile_image: null,
    cover_image: null,
    bio: null,
    website: null,
    location: null,
    facebook: null,
    twitter: null,
    accessibility: null,
    status: 'active',
    meta_title: null,
    meta_description: null,
    tour: null,
    last_seen: formatGhostDate(new Date()),
    created_at: formatGhostDate(new Date()),
    updated_at: formatGhostDate(new Date()),
    roles: [
      {
        id: generateId(),
        name: 'Author',
        description: 'Authors',
        created_at: formatGhostDate(new Date()),
        updated_at: formatGhostDate(new Date())
      }
    ]
  };
};

// Create Ghost tags
const createGhostTags = (tags) => {
  return tags.map(tag => ({
    id: generateId(),
    uuid: crypto.randomUUID(),
    name: tag,
    slug: slugify(tag),
    description: null,
    feature_image: null,
    visibility: 'public',
    meta_title: null,
    meta_description: null,
    created_at: formatGhostDate(new Date()),
    updated_at: formatGhostDate(new Date())
  }));
};

// Cleanup duplicate posts in Ghost
const cleanupDuplicatePosts = async () => {
  console.log('🧹 Starting duplicate post cleanup...');

  // Load configuration and initialize API
  const config = loadGhostConfig();
  const api = initGhostAPI(config);

  console.log('🔍 Fetching all posts from Ghost...');

  // Fetch all posts from Ghost
  let allPosts = [];
  let page = 1;
  const limit = 100;

  try {
    while (true) {
      const posts = await api.posts.browse({
        limit: limit,
        page: page,
        include: 'authors,tags'
      });

      if (posts.length === 0) break;

      allPosts = allPosts.concat(posts);
      console.log(`📄 Fetched ${allPosts.length} posts so far...`);
      page++;
    }

    console.log(`📊 Total posts found: ${allPosts.length}`);

    // Group posts by slug to find duplicates
    const postsBySlug = new Map();
    const postsByTitle = new Map();

    for (const post of allPosts) {
      // Group by slug
      if (!postsBySlug.has(post.slug)) {
        postsBySlug.set(post.slug, []);
      }
      postsBySlug.get(post.slug).push(post);

      // Also group by title (in case slugs are different but titles are same)
      if (!postsByTitle.has(post.title)) {
        postsByTitle.set(post.title, []);
      }
      postsByTitle.get(post.title).push(post);
    }

    // Find duplicates by slug
    const duplicateGroups = [];
    for (const [slug, posts] of postsBySlug) {
      if (posts.length > 1) {
        duplicateGroups.push({ type: 'slug', key: slug, posts });
      }
    }

    // Find duplicates by title (but different slugs)
    for (const [title, posts] of postsByTitle) {
      if (posts.length > 1) {
        // Check if this isn't already covered by slug duplicates
        const slugs = new Set(posts.map(p => p.slug));
        if (slugs.size > 1) { // Different slugs but same title
          duplicateGroups.push({ type: 'title', key: title, posts });
        }
      }
    }

    if (duplicateGroups.length === 0) {
      console.log('✅ No duplicate posts found!');

      if (options.verbose) {
        console.log('\n📊 Post analysis:');
        console.log(`   Total posts: ${allPosts.length}`);
        console.log(`   Unique slugs: ${postsBySlug.size}`);
        console.log(`   Unique titles: ${postsByTitle.size}`);

        // Show some recent posts for debugging
        console.log('\n📄 Recent posts (last 10):');
        const recentPosts = allPosts
          .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
          .slice(0, 10);

        for (const post of recentPosts) {
          console.log(`   "${post.title}" (${post.slug}) - ${post.created_at}`);
        }
      }

      return;
    }

    console.log(`⚠️  Found ${duplicateGroups.length} sets of duplicate posts:`);

    let totalDuplicates = 0;
    for (const group of duplicateGroups) {
      totalDuplicates += group.posts.length - 1; // Keep one, delete the rest
      console.log(`\n📝 Duplicates by ${group.type}: "${group.key}" - ${group.posts.length} copies:`);

      // Sort by created_at to keep the oldest one
      group.posts.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

      for (let i = 0; i < group.posts.length; i++) {
        const post = group.posts[i];
        const status = i === 0 ? '✅ KEEP (oldest)' : '❌ DELETE';
        console.log(`   ${status}: ID ${post.id} - "${post.title}" (${post.slug})`);
        console.log(`      Created: ${post.created_at} - Status: ${post.status}`);
      }
    }

    console.log(`\n📊 Summary: ${totalDuplicates} duplicate posts will be deleted`);

    if (options.dryRun) {
      console.log('\n🔍 Dry run completed - no posts were deleted');
      console.log('Run without --dry-run to delete the duplicate posts');
      return;
    }

    // Ask for confirmation if not in dry run mode
    console.log('\n⚠️  WARNING: This will permanently delete duplicate posts!');
    console.log('Press Ctrl+C to cancel, or any key to continue...');

    // Wait for user input
    process.stdin.setRawMode(true);
    process.stdin.resume();
    await new Promise(resolve => {
      process.stdin.once('data', () => {
        process.stdin.setRawMode(false);
        process.stdin.pause();
        resolve();
      });
    });

    console.log('\n🗑️  Starting deletion process...');

    let deletedCount = 0;
    let errorCount = 0;

    for (const group of duplicateGroups) {
      // Skip the first (oldest) post, delete the rest
      for (let i = 1; i < group.posts.length; i++) {
        const post = group.posts[i];

        try {
          if (options.verbose) {
            console.log(`🗑️  Deleting: "${post.title}" (ID: ${post.id})`);
          }

          await api.posts.delete({ id: post.id });
          console.log(`✅ Deleted duplicate: "${post.title}"`);
          deletedCount++;

        } catch (error) {
          console.error(`❌ Error deleting "${post.title}" (ID: ${post.id}):`, error.message);
          errorCount++;
        }
      }
    }

    console.log(`\n✅ Cleanup completed!`);
    console.log(`🗑️  Deleted ${deletedCount} duplicate posts`);
    if (errorCount > 0) {
      console.log(`❌ Failed to delete ${errorCount} posts`);
    }

  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
    process.exit(1);
  }
};

// Find existing post by slug in Ghost
const findPostBySlug = async (api, slug) => {
  try {
    if (options.verbose) {
      console.log(`🔍 Searching for post with slug: "${slug}"`);
    }

    const posts = await api.posts.browse({
      filter: `slug:${slug}`,
      limit: 1
    });

    if (options.verbose) {
      console.log(`   Found ${posts.length} posts matching slug "${slug}"`);
    }

    return posts.length > 0 ? posts[0] : null;
  } catch (error) {
    console.error(`Error finding post with slug "${slug}":`, error.message);
    if (error.response) {
      console.error(`   HTTP Status: ${error.response.status}`);
      if (error.response.data) {
        console.error(`   Response:`, JSON.stringify(error.response.data, null, 2));
      }
    }
    return null;
  }
};

// Get the current user (author) from Ghost
const getCurrentUser = async (api) => {
  try {
    const users = await api.users.browse({
      limit: 1
    });

    if (users.length === 0) {
      throw new Error('No users found in Ghost');
    }

    // Return the first user (should be the owner/admin)
    return users[0];
  } catch (error) {
    console.error('Error getting current user:', error.message);
    throw error;
  }
};

// Update existing posts via Admin API
const updatePostsViaAPI = async () => {
  console.log('🔄 Starting post update via Admin API...');

  // Load configuration and initialize API
  const config = loadGhostConfig();
  const api = initGhostAPI(config);

  // Get the current user to set as author
  console.log('👤 Getting current user...');
  const currentUser = await getCurrentUser(api);
  console.log(`✅ Found user: ${currentUser.name} (${currentUser.email})`);

  console.log('🔍 Finding Hugo posts...');
  const hugoPosts = findHugoPosts(options.postsDir);

  if (hugoPosts.length === 0) {
    console.log('❌ No posts found matching criteria');
    process.exit(0);
  }

  console.log(`📄 Found ${hugoPosts.length} posts to update`);

  let updatedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;

  for (const post of hugoPosts) {
    const { frontMatter, markdownContent, htmlContent, postDate, slug } = post;
    let existingPost = null;
    let updateData = null;

    try {
      // Find existing post by slug
      existingPost = await findPostBySlug(api, slug);

      const isNewPost = !existingPost;

      if (isNewPost) {
        console.log(`➕ Post not found in Ghost, creating: "${frontMatter.title}" (${slug})`);
      } else {
        if (options.verbose) {
          console.log(`📍 Found post in Ghost: "${existingPost.title}" (ID: ${existingPost.id})`);
          console.log(`   Current status: ${existingPost.status}`);
          console.log(`   Last updated: ${existingPost.updated_at}`);
          console.log(`   Published at: ${existingPost.published_at || 'not published'}`);
        }
      }

      if (options.dryRun) {
        const action = isNewPost ? 'create' : 'update';
        console.log(`🔍 Would ${action}: "${frontMatter.title}" (${slug})`);
        if (options.verbose) {
          if (options.html) {
            console.log(`   Content format: HTML (${htmlContent.length} chars, rendered from Markdown)`);
          } else {
            console.log(`   Content format: Mobiledoc Markdown (${markdownContent.length} chars)`);
          }
        }
        updatedCount++;
        continue;
      }

      // Determine status - respect both --published and --draft flags
      let status;
      if (options.published) {
        status = 'published';
      } else if (options.draft) {
        status = 'draft';
      } else {
        // Default to draft if neither flag is specified
        status = 'draft';
      }

      const publishedAt = status === 'published' ? formatGhostAPIDate(postDate) : null;

      // Create post data in the format expected by Ghost Admin API
      updateData = {
        title: frontMatter.title,
        slug: slug,
        feature_image: frontMatter.feature_image || frontMatter.image || null,
        featured: frontMatter.featured || false,
        status: status,
        visibility: frontMatter.visibility || 'public',
        custom_excerpt: generateExcerpt(frontMatter, htmlContent),
        published_at: publishedAt,
        authors: [{ id: currentUser.id }] // Set the current user as author
      };

      // Set content format based on --html flag
      if (options.html) {
        // Use HTML content directly (rendered from GitHub-flavored Markdown)
        updateData.html = htmlContent;
        updateData.mobiledoc = null;
        updateData.lexical = null;
      } else {
        // Use mobiledoc with markdown card (preserves original Markdown)
        const mobiledocContent = {
          version: '0.3.1',
          atoms: [],
          cards: [['markdown', { cardName: 'markdown', markdown: markdownContent }]],
          markups: [],
          sections: [[10, 0]]
        };
        updateData.mobiledoc = JSON.stringify(mobiledocContent);
        updateData.lexical = null;
      }

      // Add fields specific to updates (not needed for new posts)
      if (!isNewPost) {
        updateData.id = existingPost.id;
        updateData.updated_at = existingPost.updated_at; // Required for conflict detection
      }

      if (options.verbose) {
        const action = isNewPost ? 'Creating' : 'Updating';
        console.log(`🔄 ${action}: "${frontMatter.title}" (${slug})`);
        if (!isNewPost) {
          console.log(`   Ghost ID: ${existingPost.id}`);
        }
        console.log(`   Status: ${status}`);
        if (options.html) {
          console.log(`   Content: HTML (${htmlContent.length} chars, rendered from Markdown)`);
        } else {
          console.log(`   Content: Mobiledoc Markdown (${markdownContent.length} chars)`);
        }
        console.log(`   Author: ${currentUser.name} (${currentUser.email})`);
        console.log(`   Excerpt: ${updateData.custom_excerpt?.substring(0, 50)}...`);
        if (!isNewPost) {
          console.log(`   Updated at: ${existingPost.updated_at}`);
        }
      }

      // Use the appropriate Ghost Admin API method
      let result;
      if (isNewPost) {
        if (options.html) {
          result = await api.posts.add(updateData, {source: 'html'});
        } else {
          result = await api.posts.add(updateData);
        }
        console.log(`✅ Created: "${frontMatter.title}"`);
      } else {
        if (options.html) {
          result = await api.posts.edit(updateData, {source: 'html'});
        } else {
          result = await api.posts.edit(updateData);
        }
        console.log(`✅ Updated: "${frontMatter.title}"`);
      }
      updatedCount++;

    } catch (error) {
      const action = existingPost ? 'updating' : 'creating';
      console.error(`❌ Error ${action} "${frontMatter.title}" (${slug}):`);
      console.error(`   Ghost ID: ${existingPost?.id || 'new post'}`);
      console.error(`   Error: ${error.message}`);

      // Show more detailed error information
      if (error.response) {
        console.error(`   HTTP Status: ${error.response.status}`);
        if (error.response.data) {
          console.error(`   Response:`, JSON.stringify(error.response.data, null, 2));
        }
      }

      if (error.details) {
        console.error(`   Details:`, JSON.stringify(error.details, null, 2));
      }

      if (error.context) {
        console.error(`   Context:`, JSON.stringify(error.context, null, 2));
      }

      // Show the update data that was being sent
      if (options.verbose && updateData) {
        console.error(`   Update data keys: ${Object.keys(updateData).join(', ')}`);
        console.error(`   Title: "${updateData.title}"`);
        console.error(`   Slug: "${updateData.slug}"`);
        console.error(`   Status: "${updateData.status}"`);
      }

      errorCount++;
    }
  }

  // Show summary
  let status;
  if (options.published) {
    status = 'published';
  } else if (options.draft) {
    status = 'draft';
  } else {
    status = 'draft'; // default
  }

  console.log(`\n✅ Sync completed!`);
  console.log(`📄 Processed ${updatedCount} posts (status: ${status})`);
  if (skippedCount > 0) {
    console.log(`⚠️  Skipped ${skippedCount} posts`);
  }
  if (errorCount > 0) {
    console.log(`❌ Failed to process ${errorCount} posts`);
  }

  if (options.dryRun) {
    console.log('\n🔍 Dry run completed - no posts were actually created or updated');
    console.log('Run without --dry-run to sync the posts');
  }
};

// Export posts to Ghost JSON format
const exportPostsToGhost = async () => {
  console.log('🔍 Finding Hugo posts...');
  const hugoPosts = findHugoPosts(options.postsDir);

  if (hugoPosts.length === 0) {
    console.log('❌ No posts found matching criteria');
    process.exit(0);
  }

  console.log(`📄 Found ${hugoPosts.length} posts to export`);

  // Extract all unique tags
  const allTags = [...new Set(hugoPosts.flatMap(post => post.frontMatter.tags || []))];

  // Generate IDs
  const authorId = generateId();
  const postIds = hugoPosts.map(() => generateId());

  // Create Ghost objects
  const ghostPosts = hugoPosts.map((post, index) => createGhostPost(post, postIds[index]));
  const ghostTags = createGhostTags(allTags);
  const ghostUsers = [createGhostUser(authorId)];

  // Create tag mappings
  const tagMap = new Map();
  ghostTags.forEach(tag => tagMap.set(tag.name, tag.id));

  // Create relationships
  const postsAuthors = ghostPosts.map(post => ({
    id: generateId(),
    post_id: post.id,
    author_id: authorId,
    sort_order: 0
  }));

  const postsTags = [];
  hugoPosts.forEach((post, index) => {
    const postTags = post.frontMatter.tags || [];
    postTags.forEach(tagName => {
      const tagId = tagMap.get(tagName);
      if (tagId) {
        postsTags.push({
          id: generateId(),
          post_id: postIds[index],
          tag_id: tagId,
          sort_order: 0
        });
      }
    });
  });

  // Create Ghost JSON structure
  const ghostData = {
    meta: {
      exported_on: Date.now(),
      version: "6.0.0"
    },
    data: {
      posts: ghostPosts,
      tags: ghostTags,
      posts_tags: postsTags,
      users: ghostUsers,
      posts_authors: postsAuthors
    }
  };

  // Show summary
  let status;
  if (options.published) {
    status = 'published';
  } else if (options.draft) {
    status = 'draft';
  } else {
    status = 'draft'; // default
  }

  console.log(`✅ Export completed successfully!`);
  console.log(`📄 Exported ${ghostPosts.length} posts (status: ${status})`);
  console.log(`🏷️  Created ${ghostTags.length} tags`);

  if (options.verbose) {
    console.log('\n📋 Posts exported:');
    hugoPosts.forEach((post) => {
      console.log(`  - ${post.frontMatter.title} (${post.postDate.toISOString().split('T')[0]})`);
    });

    console.log('\n🏷️  Tags created:');
    ghostTags.forEach(tag => {
      console.log(`  - ${tag.name}`);
    });
  }

  if (options.dryRun) {
    console.log('\n🔍 Dry run completed - no file was created');
    console.log('Run without --dry-run to create the export file');
    return;
  }

  // Write to file
  try {
    fs.writeFileSync(options.output, JSON.stringify(ghostData, null, 2));
    console.log(`💾 Output saved to: ${options.output}`);
  } catch (error) {
    console.error('❌ Error writing output file:', error.message);
    process.exit(1);
  }
};

// ============================================================================
// POST SYNC FUNCTIONALITY
// ============================================================================







// List posts from Ghost
const listGhostPosts = async () => {
  try {
    // Load configuration
    const config = loadGhostConfig();

    // Create GhostAPI instance
    const ghostAPI = new GhostAPI(config, {
      verbose: options.verbose,
      dryRun: false
    });

    // Get all posts
    const posts = await ghostAPI.findPosts();

    if (posts.length === 0) {
      console.log('📄 No posts found in Ghost');
      return;
    }

    // Output posts in a format that can be parsed by the plugin
    console.log('📄 Posts from Ghost:');
    posts.forEach(post => {
      const publishedDate = post.published_at ? new Date(post.published_at).toLocaleDateString() : 'Draft';
      console.log(`${post.title}|${post.slug}|${publishedDate}|${post.tags?.map(t => t.name).join(', ') || 'No tags'}`);
    });

  } catch (error) {
    console.error('❌ Error listing posts from Ghost:', error.message);
    throw error;
  }
};

// Sync posts from Ghost to local articles directory
const syncPostsToLocal = async () => {
  try {
    // Load configuration
    const config = loadGhostConfig();

    // Create PostSync instance with options
    const postSync = new PostSync(config, {
      verbose: options.verbose,
      dryRun: options.dryRun,
      articlesDir: options.articlesDir
    });

    // Sync posts
    await postSync.syncPostsToLocal(options.title);

  } catch (error) {
    console.error('❌ Error during post sync:', error.message);
    throw error;
  }
};

// Main function
const main = async () => {
  try {
    // Handle post export functionality
    if (options.exportPosts) {
      console.log('📄 Starting post export to Ghost JSON format...');
      await exportPostsToGhost();
      return;
    }

    // Handle post update functionality
    if (options.updatePosts) {
      await updatePostsViaAPI();
      return;
    }

    // Handle duplicate cleanup functionality
    if (options.cleanupDuplicates) {
      await cleanupDuplicatePosts();
      return;
    }

    // Handle list posts functionality
    if (options.listPosts) {
      await listGhostPosts();
      return;
    }

    // Handle post sync functionality
    if (options.syncPosts) {
      await syncPostsToLocal();
      return;
    }

    console.log('🚀 Starting Ghost.io sync...');

    // Load configuration
    const config = loadGhostConfig();
    console.log('✅ Ghost configuration loaded');

    // Initialize Ghost API
    const api = initGhostAPI(config);
    console.log('✅ Ghost Admin API initialized');

    // Handle specific page updates
    if (options.page) {
      if (options.page === 'github-sponsors') {
        try {
          await updateGitHubSponsorsPage(api);
        } catch (error) {
          if (error.message.includes('Authorization') || error.message.includes('401')) {
            console.error('❌ Admin API authorization failed');
            console.error('💡 Falling back to HTML generation mode...');

            // Generate HTML as fallback
            const { html, currentSponsors, pastSponsors } = generateGitHubSponsorsHTML();
            const outputFile = 'github-sponsors-manual-update.html';

            try {
              fs.writeFileSync(outputFile, html);
              console.log('✅ HTML with embedded CSS generated successfully!');
              console.log(`💾 Output saved to: ${outputFile}`);
              console.log(`📊 Current sponsors: ${currentSponsors}`);
              console.log(`📊 Past sponsors: ${pastSponsors}`);
              console.log('\n📋 Manual Update Instructions:');
              console.log('1. Copy the entire content from the generated HTML file');
              console.log('2. Go to Ghost.io Admin → Pages → GitHub Sponsors');
              console.log('3. Delete current content and add an HTML card');
              console.log('4. Paste the HTML content into the HTML card');
              console.log('5. Publish the page');
              console.log('\n💡 To fix API access:');
              console.log('💡 1. Go to https://solnic.ghost.io/ghost/#/settings/integrations');
              console.log('💡 2. Create a new Custom Integration');
              console.log('💡 3. Copy the Admin API Key');
              console.log('💡 4. Update ghost.yml with the new key');
            } catch (writeError) {
              console.error('❌ Error writing HTML file:', writeError.message);
              process.exit(1);
            }
          } else {
            throw error;
          }
        }
      } else if (options.page === 'open-source') {
        try {
          await updateOpenSourcePage(api);
        } catch (error) {
          if (error.message.includes('Authorization') || error.message.includes('401')) {
            console.error('❌ Admin API authorization failed');
            console.error('💡 Falling back to HTML generation mode...');

            // Generate HTML as fallback
            const config = loadGhostConfig();
            const { html, totalProjects, currentProjects, pastProjects } = await generateOpenSourcePortfolioHTML(config);
            const outputFile = 'open-source-manual-update.html';

            try {
              fs.writeFileSync(outputFile, html);
              console.log('✅ HTML with embedded CSS generated successfully!');
              console.log(`💾 Output saved to: ${outputFile}`);
              console.log(`📊 Total projects: ${totalProjects}`);
              console.log(`📊 Current projects: ${currentProjects}`);
              console.log(`📊 Past projects: ${pastProjects}`);
              console.log('\n📋 Manual Update Instructions:');
              console.log('1. Copy the entire content from the generated HTML file');
              console.log('2. Go to Ghost.io Admin → Pages → Open Source');
              console.log('3. Delete current content and add an HTML card');
              console.log('4. Paste the HTML content into the HTML card');
              console.log('5. Publish the page');
              console.log('\n💡 To fix API access:');
              console.log('💡 1. Go to https://solnic.ghost.io/ghost/#/settings/integrations');
              console.log('💡 2. Create a new Custom Integration');
              console.log('💡 3. Copy the Admin API Key');
              console.log('💡 4. Update ghost.yml with the new key');
            } catch (writeError) {
              console.error('❌ Error writing HTML file:', writeError.message);
              process.exit(1);
            }
          } else {
            throw error;
          }
        }
      } else if (options.page === 'about') {
        try {
          await updateAboutPage(api);
        } catch (error) {
          if (error.message.includes('Authorization') || error.message.includes('401')) {
            console.error('❌ Admin API authorization failed');
            console.error('💡 Falling back to HTML generation mode...');

            // Generate HTML as fallback
            const config = loadGhostConfig();
            const { html } = await generateAboutPageHTML(config);
            const outputFile = 'about-manual-update.html';

            try {
              fs.writeFileSync(outputFile, html);
              console.log('✅ HTML with embedded CSS generated successfully!');
              console.log(`💾 Output saved to: ${outputFile}`);
              console.log('\n📋 Manual Update Instructions:');
              console.log('1. Copy the entire content from the generated HTML file');
              console.log('2. Go to Ghost.io Admin → Pages → About');
              console.log('3. Delete current content and add an HTML card');
              console.log('4. Paste the HTML content into the HTML card');
              console.log('5. Publish the page');
              console.log('\n💡 To fix API access:');
              console.log('💡 1. Go to https://solnic.ghost.io/ghost/#/settings/integrations');
              console.log('💡 2. Create a new Custom Integration');
              console.log('💡 3. Copy the Admin API Key');
              console.log('💡 4. Update ghost.yml with the new key');
            } catch (writeError) {
              console.error('❌ Error writing HTML file:', writeError.message);
              process.exit(1);
            }
          } else {
            throw error;
          }
        }
      } else {
        console.error(`❌ Unknown page: ${options.page}`);
        console.error('💡 Supported pages: github-sponsors, open-source, about');
        process.exit(1);
      }
      return;
    }

    // If no specific page, export, update, or cleanup option, show help
    console.log('💡 Please specify an action:');
    console.log('💡   --page <name>          Update a specific page');
    console.log('💡   --export-posts         Export posts to JSON');
    console.log('💡   --update-posts         Sync posts with Ghost (create/update)');
    console.log('💡   --cleanup-duplicates   Remove duplicate posts');
    console.log('💡 Run with --help for more information');

  } catch (error) {
    console.error('❌ Error:', error.message);

    if (options.verbose) {
      console.error('Stack trace:', error.stack);
    }

    process.exit(1);
  }
};

// Run the script
if (require.main === module) {
  main();
}
