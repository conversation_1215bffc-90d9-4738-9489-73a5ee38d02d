#!/bin/bash

# Update About Page Script
# This script updates the About page on Ghost.io with the latest content

set -e

echo "🚀 About Page Update Script"
echo "=========================="
echo ""

# Check if we're in the right directory
if [ ! -f "data/about.json" ]; then
    echo "❌ Error: data/about.json not found"
    echo "💡 Please run this script from the project root directory"
    exit 1
fi

if [ ! -f "scripts/ghost.js" ]; then
    echo "❌ Error: scripts/ghost.js not found"
    exit 1
fi

echo "✅ All required files found"

# Check for about photo
if [ -f "assets/images/about.png" ]; then
    echo "📸 Found about photo: assets/images/about.png"
    echo "💡 This will be uploaded to Ghost.io automatically"
else
    echo "⚠️  No about photo found at assets/images/about.png"
    echo "💡 Add your photo there to include it in the About page"
fi

echo ""

# Check if Ghost.io configuration exists
if [ ! -f "config/ghost.yml" ]; then
    echo "⚠️  Warning: ghost.yml not found"
    echo "💡 You'll need to configure Ghost.io API access for automatic sync"
    echo "💡 See GHOST_INTEGRATION_SUMMARY.md for setup instructions"
    echo ""
fi

# Update the About page
echo "🔄 Updating About page on Ghost.io..."
if node scripts/ghost.js --page about --verbose; then
    echo "✅ About page updated successfully!"
    echo "🌐 View your updated page at: https://solnic.dev/about/"
else
    echo "⚠️  Warning: Failed to sync with Ghost.io automatically"
    echo ""
    echo "📋 Manual Update Instructions:"
    echo "1. A fallback HTML file has been generated: about-manual-update.html"
    echo "2. Copy the entire content from that file"
    echo "3. Go to Ghost.io Admin → Pages → About"
    echo "4. Delete current content and add an HTML card"
    echo "5. Paste the HTML content into the HTML card"
    echo "6. Publish the page"
    echo ""
    echo "💡 To fix automatic sync:"
    echo "💡 1. Go to https://solnic.ghost.io/ghost/#/settings/integrations"
    echo "💡 2. Create a new Custom Integration"
    echo "💡 3. Copy the Admin API Key"
    echo "💡 4. Update ghost.yml with the new key"
fi

echo ""
echo "🎉 About page update process completed!"
echo ""

# Optional: Start local server for testing
if [ "$1" = "--preview" ]; then
    echo "🌐 Generating local preview..."
    node -e "
const TemplateEngine = require('./lib/template-engine');
const fs = require('fs');
const path = require('path');

const templateEngine = new TemplateEngine(path.join(__dirname, 'templates'));
const aboutData = JSON.parse(fs.readFileSync('data/about.json', 'utf8'));

const templateData = {
  lastUpdated: new Date().toISOString(),
  ...aboutData
};

const html = templateEngine.renderPage('about', templateData);
fs.writeFileSync('about-preview.html', html);
console.log('✅ Preview generated: about-preview.html');
"

    echo "📱 Opening preview in browser..."
    if command -v open &> /dev/null; then
        open about-preview.html
    elif command -v xdg-open &> /dev/null; then
        xdg-open about-preview.html
    else
        echo "📱 Please open about-preview.html in your browser"
    fi
fi
