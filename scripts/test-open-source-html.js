#!/usr/bin/env node

// Test script to generate open source portfolio HTML for Ghost.io
const fs = require('fs');
const path = require('path');

// Load the ghost.js functions
const ghostScript = fs.readFileSync(path.join(__dirname, 'ghost.js'), 'utf8');

// Extract the generateOpenSourcePortfolioHTML function
eval(ghostScript.split('// Generate Open Source Portfolio HTML')[1].split('// Initialize Ghost Admin API')[0]);

// Generate the HTML
try {
  const { html, totalProjects, currentProjects, pastProjects } = generateOpenSourcePortfolioHTML();
  
  console.log('✅ Generated open source portfolio HTML');
  console.log(`📊 Total projects: ${totalProjects}`);
  console.log(`📊 Current projects: ${currentProjects}`);
  console.log(`📊 Past projects: ${pastProjects}`);
  console.log(`📄 HTML length: ${html.length} characters`);
  
  // Write to file for testing
  const outputFile = 'open-source-ghost-preview.html';
  fs.writeFileSync(outputFile, html);
  console.log(`💾 HTML saved to: ${outputFile}`);
  
} catch (error) {
  console.error('❌ Error generating HTML:', error.message);
  process.exit(1);
}
