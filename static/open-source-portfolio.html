<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> - Open Source Portfolio</title>
    <meta name="description" content="15 years of open source contributions - Ruby, Elixir, and modern software architecture">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --accent-color: #0ea5e9;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
            --text-primary: #0f172a;
            --text-secondary: #475569;
            --text-muted: #94a3b8;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--light-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header p {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Stats Section */
        .stats-section {
            background: white;
            margin: -2rem auto 4rem;
            border-radius: 1rem;
            box-shadow: var(--shadow-xl);
            padding: 2rem;
            max-width: 800px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
        }

        .stat-card {
            text-align: center;
            padding: 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.05em;
        }

        .stat-icon {
            font-size: 1.5rem;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        /* Contributions Chart */
        .contributions-chart {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
        }

        .chart-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .chart-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .chart-subtitle {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 0 auto;
        }

        @media (max-width: 768px) {
            .chart-container {
                height: 250px;
            }
        }

        /* Projects Section */
        .projects-section {
            padding: 4rem 0;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .projects-filter {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.75rem 1.5rem;
            border: 2px solid var(--border-color);
            background: white;
            color: var(--text-secondary);
            border-radius: 2rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .filter-btn:hover,
        .filter-btn.active {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }

        /* Project Cards */
        .project-card {
            background: white;
            border-radius: 1rem;
            box-shadow: var(--shadow-md);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .project-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .project-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .project-logo {
            width: 60px;
            height: 60px;
            border-radius: 0.75rem;
            margin-bottom: 1rem;
            background: var(--light-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        .project-logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 0.75rem;
        }

        .project-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .project-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .project-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .badge-active {
            background: #dcfce7;
            color: #166534;
        }

        .badge-discontinued {
            background: #fef3c7;
            color: #92400e;
        }

        .badge-framework {
            background: #dbeafe;
            color: #1e40af;
        }

        .badge-library {
            background: #f3e8ff;
            color: #7c3aed;
        }

        .badge-organization {
            background: #fce7f3;
            color: #be185d;
        }

        .project-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .project-body {
            padding: 1.5rem;
        }

        .project-highlights {
            margin-bottom: 1.5rem;
        }

        .highlights-list {
            list-style: none;
            margin-top: 1rem;
        }

        .highlights-list li {
            padding: 0.5rem 0;
            color: var(--text-secondary);
            position: relative;
            padding-left: 1.5rem;
        }

        .highlights-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: var(--success-color);
            font-weight: bold;
        }

        .project-links {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .project-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .project-link:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: var(--light-color);
        }

        .expand-btn {
            width: 100%;
            padding: 1rem;
            border: none;
            background: var(--light-color);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .expand-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .project-details {
            display: none;
            padding: 1.5rem;
            border-top: 1px solid var(--border-color);
            background: var(--light-color);
        }

        .project-details.expanded {
            display: block;
        }

        .sub-projects {
            margin-top: 1.5rem;
        }

        .sub-project {
            padding: 1rem;
            background: white;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            border: 1px solid var(--border-color);
        }

        .sub-project-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .sub-project-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        /* Loading State */
        .loading {
            text-align: center;
            padding: 4rem 0;
            color: var(--text-secondary);
        }

        .spinner {
            display: inline-block;
            width: 2rem;
            height: 2rem;
            border: 3px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .header p {
                font-size: 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .projects-grid {
                grid-template-columns: 1fr;
            }

            .project-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .project-links {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1>Open Source Portfolio</h1>
            <p>15 years of contributions to Ruby, Elixir, and modern software architecture</p>
        </div>
    </header>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="stats-grid" id="statsGrid">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Loading contribution statistics...</p>
                </div>
            </div>

            <!-- Contributions Chart -->
            <div class="contributions-chart" id="contributionsChart" style="display: none;">
                <div class="chart-header">
                    <h3 class="chart-title">Contribution Timeline</h3>
                    <p class="chart-subtitle">16 years of open source contributions (2010-2025)</p>
                </div>
                <div class="chart-container">
                    <canvas id="contributionsCanvas"></canvas>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section class="projects-section">
        <div class="container">
            <h2 class="section-title">Projects & Contributions</h2>
            <p class="section-subtitle">
                A showcase of my open source work spanning frameworks, libraries, and tools that have shaped modern Ruby and Elixir development.
            </p>

            <!-- Filter Buttons -->
            <div class="projects-filter">
                <button class="filter-btn active" data-filter="all">All Projects</button>
                <button class="filter-btn" data-filter="current">Current</button>
                <button class="filter-btn" data-filter="past">Past Projects</button>
                <button class="filter-btn" data-filter="framework">Frameworks</button>
                <button class="filter-btn" data-filter="library">Libraries</button>
            </div>

            <!-- Projects Grid -->
            <div class="projects-grid" id="projectsGrid">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Loading projects...</p>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Global state
        let projectsData = null;
        let statsData = null;
        let currentFilter = 'all';

        // Initialize the portfolio
        document.addEventListener('DOMContentLoaded', async () => {
            await loadData();
            renderStats();
            renderContributionsChart();
            renderProjects();
            setupEventListeners();
        });

        // Load data from JSON files
        async function loadData() {
            try {
                // Load projects data
                const projectsResponse = await fetch('/data/open-source-projects.json');
                projectsData = await projectsResponse.json();

                // Try to load GitHub stats (may not exist yet)
                try {
                    const statsResponse = await fetch('/data/github-stats.json');
                    if (statsResponse.ok) {
                        statsData = await statsResponse.json();
                    }
                } catch (error) {
                    console.log('GitHub stats not available yet');
                }
            } catch (error) {
                console.error('Error loading data:', error);
            }
        }

        // Render statistics section
        function renderStats() {
            const statsGrid = document.getElementById('statsGrid');

            if (statsData) {
                statsGrid.innerHTML = `
                    <div class="stat-card">
                        <i class="fas fa-code-commit stat-icon"></i>
                        <span class="stat-number">${statsData.total_contributions?.toLocaleString() || '10,000+'}</span>
                        <span class="stat-label">Total Commits</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-code-branch stat-icon"></i>
                        <span class="stat-number">${statsData.total_repositories || '100+'}</span>
                        <span class="stat-label">Repositories</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-users stat-icon"></i>
                        <span class="stat-number">${statsData.organizations_count || '20+'}</span>
                        <span class="stat-label">Organizations</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-calendar stat-icon"></i>
                        <span class="stat-number">15+</span>
                        <span class="stat-label">Years Contributing</span>
                    </div>
                `;
            } else {
                // Fallback stats
                statsGrid.innerHTML = `
                    <div class="stat-card">
                        <i class="fas fa-code-commit stat-icon"></i>
                        <span class="stat-number">10,000+</span>
                        <span class="stat-label">Total Commits</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-code-branch stat-icon"></i>
                        <span class="stat-number">100+</span>
                        <span class="stat-label">Repositories</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-users stat-icon"></i>
                        <span class="stat-number">20+</span>
                        <span class="stat-label">Organizations</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-calendar stat-icon"></i>
                        <span class="stat-number">15+</span>
                        <span class="stat-label">Years Contributing</span>
                    </div>
                `;
            }
        }

        // Render contributions chart
        function renderContributionsChart() {
            if (!statsData || !statsData.yearly_contributions) {
                return;
            }

            const chartContainer = document.getElementById('contributionsChart');
            chartContainer.style.display = 'block';

            const ctx = document.getElementById('contributionsCanvas').getContext('2d');

            // Prepare data for the chart
            const yearlyData = statsData.yearly_contributions.sort((a, b) => a.year - b.year);
            const years = yearlyData.map(item => item.year);
            const contributions = yearlyData.map(item => item.totalContributions);

            // Create gradient
            const gradient = ctx.createLinearGradient(0, 0, 0, 300);
            gradient.addColorStop(0, 'rgba(37, 99, 235, 0.3)');
            gradient.addColorStop(1, 'rgba(37, 99, 235, 0.05)');

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: years,
                    datasets: [{
                        label: 'Contributions',
                        data: contributions,
                        borderColor: '#2563eb',
                        backgroundColor: gradient,
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#2563eb',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        pointHoverBackgroundColor: '#1d4ed8',
                        pointHoverBorderColor: '#ffffff',
                        pointHoverBorderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(15, 23, 42, 0.9)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#2563eb',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: false,
                            callbacks: {
                                title: function(context) {
                                    return `Year ${context[0].label}`;
                                },
                                label: function(context) {
                                    return `${context.parsed.y.toLocaleString()} contributions`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#64748b',
                                font: {
                                    size: 12,
                                    weight: '500'
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(226, 232, 240, 0.5)',
                                drawBorder: false
                            },
                            ticks: {
                                color: '#64748b',
                                font: {
                                    size: 12
                                },
                                callback: function(value) {
                                    return value.toLocaleString();
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    elements: {
                        point: {
                            hoverRadius: 8
                        }
                    }
                }
            });
        }

        // Render projects
        function renderProjects() {
            const projectsGrid = document.getElementById('projectsGrid');

            if (!projectsData) {
                projectsGrid.innerHTML = '<div class="loading"><p>Error loading projects data</p></div>';
                return;
            }

            const allProjects = [...projectsData.projects, ...projectsData.past_projects];
            const filteredProjects = filterProjects(allProjects, currentFilter);

            projectsGrid.innerHTML = filteredProjects.map(project => createProjectCard(project)).join('');
        }

        // Filter projects based on current filter
        function filterProjects(projects, filter) {
            switch (filter) {
                case 'current':
                    return projects.filter(p => p.category === 'current');
                case 'past':
                    return projects.filter(p => p.category === 'past');
                case 'framework':
                    return projects.filter(p => p.type === 'framework');
                case 'library':
                    return projects.filter(p => p.type === 'library');
                case 'organization':
                    return projects.filter(p => p.type === 'organization');
                default:
                    return projects;
            }
        }

        // Create project card HTML
        function createProjectCard(project) {
            const logoHtml = project.logo
                ? `<img src="/content/open-source/images/${project.logo}" alt="${project.name} logo">`
                : `<i class="fab fa-github"></i>`;

            const statusBadge = project.status === 'active' ? 'badge-active' : 'badge-discontinued';
            const typeBadge = `badge-${project.type}`;

            const linksHtml = Object.entries(project.links || {}).map(([key, url]) => {
                const icon = getIconForLinkType(key);
                const label = key.charAt(0).toUpperCase() + key.slice(1).replace('_', ' ');
                return `<a href="${url}" class="project-link" target="_blank" rel="noopener">
                    <i class="${icon}"></i> ${label}
                </a>`;
            }).join('');

            const highlightsHtml = project.highlights ? `
                <div class="project-highlights">
                    <h4>Key Highlights</h4>
                    <ul class="highlights-list">
                        ${project.highlights.map(highlight => `<li>${highlight}</li>`).join('')}
                    </ul>
                </div>
            ` : '';

            const subProjectsHtml = project.sub_projects ? `
                <div class="sub-projects">
                    <h4>Sub-projects</h4>
                    ${project.sub_projects.map(sub => `
                        <div class="sub-project">
                            <div class="sub-project-name">${sub.name}</div>
                            <div class="sub-project-description">${sub.description}</div>
                        </div>
                    `).join('')}
                </div>
            ` : '';

            const yearRange = project.year_ended
                ? `${project.year_started} - ${project.year_ended}`
                : `${project.year_started} - Present`;

            return `
                <div class="project-card" data-category="${project.category}" data-type="${project.type}">
                    <div class="project-header">
                        <div class="project-logo">${logoHtml}</div>
                        <h3 class="project-title">${project.name}</h3>
                        <div class="project-meta">
                            <span class="project-badge ${statusBadge}">${project.status}</span>
                            <span class="project-badge ${typeBadge}">${project.type}</span>
                            <span class="project-badge">${yearRange}</span>
                        </div>
                        <p class="project-description">${project.description}</p>
                    </div>
                    <div class="project-body">
                        <div class="project-links">${linksHtml}</div>
                    </div>
                    ${highlightsHtml || subProjectsHtml ? `
                        <button class="expand-btn" onclick="toggleProjectDetails('${project.id}')">
                            <i class="fas fa-chevron-down"></i> Show Details
                        </button>
                        <div class="project-details" id="details-${project.id}">
                            ${highlightsHtml}
                            ${subProjectsHtml}
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // Get icon for link type
        function getIconForLinkType(type) {
            const icons = {
                github: 'fab fa-github',
                website: 'fas fa-globe',
                blog_post: 'fas fa-blog',
                ruby_feature_request: 'fas fa-gem'
            };
            return icons[type] || 'fas fa-link';
        }

        // Toggle project details
        function toggleProjectDetails(projectId) {
            const details = document.getElementById(`details-${projectId}`);
            const button = details.previousElementSibling;
            const icon = button.querySelector('i');

            if (details.classList.contains('expanded')) {
                details.classList.remove('expanded');
                button.innerHTML = '<i class="fas fa-chevron-down"></i> Show Details';
            } else {
                details.classList.add('expanded');
                button.innerHTML = '<i class="fas fa-chevron-up"></i> Hide Details';
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            // Filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    // Update active button
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');

                    // Update filter and re-render
                    currentFilter = e.target.dataset.filter;
                    renderProjects();
                });
            });
        }

        // Make toggleProjectDetails available globally
        window.toggleProjectDetails = toggleProjectDetails;
    </script>
</body>
</html>
